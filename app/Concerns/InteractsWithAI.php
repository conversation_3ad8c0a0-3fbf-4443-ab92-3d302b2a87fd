<?php

namespace App\Concerns;

use Http;
use UnexpectedValueException;

trait InteractsWithAI
{
    /**
     * API地址
     */
    protected string $cozeAPI = 'https://api.coze.cn/v3';

    /**
     * 配置
     */
    protected array $cozeConfig = [
        'bot_id' => '',
        'command_id' => '',
        'parameter_name' => ''
    ];

    /**
     * 设置Coze配置
     *
     * @param   string  $botId
     * @param   string  $commandId
     * @param   string  $parameterName
     * @return  self
     */
    protected function coze(string $botId, string $commandId, string $parameterName)
    {
        $this->cozeConfig = [
            'bot_id' => $botId,
            'command_id' => $commandId,
            'parameter_name' => $parameterName
        ];

        return $this;
    }

    /**
     * 创建会话
     *
     * @param   string  $content
     * @return  array
     */
    protected function newAIChat(string $content): array
    {
        $chat = Http::withHeaders([
            'Authorization' => 'Bearer ' . config('baoya.ai.coze.token')
        ])
            ->post("$this->cozeAPI/chat", [
                'bot_id' => $this->cozeConfig['bot_id'],
                'user_id' => 'yingrtech',
                'stream' => false,
                'auto_save_history' => true,
                'shortcut_command' => [
                    'command_id' => $this->cozeConfig['command_id'],
                    'parameters' => [
                        $this->cozeConfig['parameter_name'] => json_encode([
                            [
                                'type' => 'text',
                                'text' => $content
                            ]
                        ])
                    ]
                ]
            ])
            ->throw()
            ->json();

        if ($chat['code'] ?? 0 !== 0) {
            throw new UnexpectedValueException($chat['msg']);
        }

        return [
            'chat_id' => $chat['data']['id'],
            'conversation_id' => $chat['data']['conversation_id']
        ];
    }

    /**
     * 获取回答
     *
     * @param   array   $query
     * @return  array
     */
    protected function retrieveAnswer(array $query)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . config('baoya.ai.coze.token')
        ])
            ->get("$this->cozeAPI/chat/message/list", $query)
            ->throw()
            ->json();

        if ($response['code'] !== 0) {
            throw new UnexpectedValueException($response['msg']);
        }

        if (!isset($response['data'])) {
            return [];
        }

        $answer = [];
        foreach ($response['data'] as $message) {
            if ($message['role'] === 'assistant' && $message['type'] === 'answer') {
                $answer = json_decode($message['content'], true);
            }
        }

        return $answer;
    }

    /**
     * AI建议
     *
     * @param   string  $content
     * @return  array
     */
    protected function askAI(string $content): array
    {
        $chat = $this->newAIChat($content);

        $times = 60;
        while ($times-- > 0) {
            $answer = $this->retrieveAnswer($chat);
            if (count($answer) > 0) {
                break;
            }
            sleep(1);
        }

        return $answer;
    }
}
