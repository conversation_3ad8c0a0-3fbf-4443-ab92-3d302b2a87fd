<?php

namespace App\Console\Commands\Account;

use App\Models\CompanyBranchAccount;
use App\Notifications\AccountExpiringNotification;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Notification;

class AccountExpiring extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'account:expiring';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '检查账号是否过期并发送邮件通知';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        CompanyBranchAccount::where('expires_on', '<=', Carbon::now()->addDays(15))
            ->where('is_enabled', true)
            ->with('platform')
            ->eachById(function ($account) {
                Notification::send($account['platform']['admin'], new AccountExpiringNotification($account));
            });
    }
}
