<?php

namespace App\Console\Commands\Data;

use App\Enums\DataChartAccordingTo;
use App\Models\Platform;
use App\Services\Data\Chart;
use Arr;
use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;

class ChartCommand extends Command
{
    /**
     * 缓存键名
     *
     * @var string
     */
    const DATA_CACHE_KEY = 'data:charts:%d:1:065232aa9ee99d94f6ff8400c349abb9';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:chart
                            {--p|platform=* : 平台 ID}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '[数据] 统计数据图表';

    /**
     * Handle the command.
     *
     *
     * @return void
     */
    public function handle()
    {
        ini_set('memory_limit', '4096M');
        ini_set('default_socket_timeout', '3600');

        $platforms = Platform::when(
            $this->option('platform'),
            fn($q, $p) => $q->whereIn('id', Arr::wrap($p))
        )->get();

        $progressBar = $this->output->createProgressBar($platforms->count());
        $platforms->each(function ($platform) use ($progressBar) {
            $progressBar->advance();
            Cache::put(
                sprintf(self::DATA_CACHE_KEY, $platform['id']),
                (new Chart)->handle($platform['id'], [], DataChartAccordingTo::OurUser),
                Carbon::now()->endOfDay()
            );
        });
        $progressBar->finish();
    }
}
