<?php

namespace App\Console\Commands\Data;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Throwable;

class PolicyAggregate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:policy-aggregate';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '[数据] 合并数据到汇总表';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        try {
            DB::unprepared($this->rawSql());
            if (Schema::hasTable('policy_aggregations')) {
                Schema::rename('policy_aggregations', 'policy_aggregations_old');
            }

            Schema::rename('policy_aggregations_temporary', 'policy_aggregations');

            $this->createIndexes();

            if (Schema::hasTable('policy_aggregations_old')) {
                Schema::drop('policy_aggregations_old');
            }
        } catch (Throwable $e) {
            $this->error('[command:policy-aggregate] error: ' . $e->getMessage());

            if (Schema::hasTable('policy_aggregations_old')) {
                if (!Schema::hasTable('policy_aggregations')) {
                    Schema::rename('policy_aggregations_old', 'policy_aggregations');
                } else {
                    Schema::drop('policy_aggregations_old');
                }
            }
        }
    }

    /**
     * 创建索引
     *
     * @return  void
     */
    protected function createIndexes()
    {
        DB::statement('ALTER TABLE `policy_aggregations` ADD INDEX `policy_aggregations_type_index` (`type`)');
        DB::statement('ALTER TABLE `policy_aggregations` ADD INDEX `policy_aggregations_company_id_index` (`company_id`)');
        DB::statement('ALTER TABLE `policy_aggregations` ADD INDEX `policy_aggregations_company_branch_id_index` (`company_branch_id`)');
        DB::statement('ALTER TABLE `policy_aggregations` ADD INDEX `policy_aggregations_channel_id_index` (`channel_id`)');
        DB::statement('ALTER TABLE `policy_aggregations` ADD INDEX `policy_aggregations_user_id_index` (`user_id`)');
        DB::statement('ALTER TABLE `policy_aggregations` ADD INDEX `policy_aggregations_platform_id_index` (`platform_id`)');
        DB::statement('ALTER TABLE `policy_aggregations` ADD INDEX `policy_aggregations_salesman_id_index` (`salesman_id`)');
        DB::statement('ALTER TABLE `policy_aggregations` ADD INDEX `policy_aggregations_status_index` (`status`)');
    }

    /**
     * 保单汇总表 SQL 语句
     *
     * @return  string
     */
    protected function rawSql(): string
    {
        return <<<SQL
            CREATE TEMPORARY TABLE IF NOT EXISTS `policy_temporary` AS (
                SELECT
                    @r := @r + 1 AS page_id,
                    d.*
                FROM
                    (SELECT @r := 0) as vars,
                    (SELECT
                        p.id,
                        5 AS `type`,
                        p.company_id,
                        p.company_branch_id,
                        p.channel_id,
                        p.user_id,
                        p.platform_id,
                        p.salesman_id,
                        p.policyholder,
                        p.insured,
                        pge.endorse_file as policy_file,
                        0 as coverage,
                        p.service_charge,
                        p.rate,
                        p.premium,
                        p.platform_rate,
                        p.platform_premium,
                        p.platform_commission,
                        p.platform_commission_rate,
                        p.agent_commission,
                        p.agent_commission_rate,
                        p.agent_rate,
                        pge.total_fee * 100 as agent_premium,
                        p.user_rate,
                        pge.total_fee * 100 as user_premium,
                        pge.id AS pge_id,
                        pge.policy_group_id,
                        p.order_no,
                        IFNULL(pge.endorse_no, p.policy_no) AS policy_no,
                        p.sticky_note,
                        p.is_premium_sync,
                        p.is_allowed_invoice,
                        CASE
                            WHEN p.status = 6 THEN 6
                            WHEN pge.status = 0 THEN 2
                            WHEN pge.status = 1 THEN 5
                            WHEN pge.status = 2 THEN 10
                            WHEN pge.status = 3 THEN 1
                            ELSE p.status
                        END AS `status`,
                        pge.created_at as submitted_at,
                        pge.updated_at AS issued_at,
                        pge.created_at,
                        pge.updated_at,
                        pge.deleted_at
                    FROM
                        policy_group_endorses AS pge
                    LEFT JOIN policies AS p ON p.id = pge.policy_id
                        AND pge.deleted_at IS NULL
                    UNION ALL
                    SELECT
                        p.id,
                        p. `type`,
                        p.company_id,
                        p.company_branch_id,
                        p.channel_id,
                        p.user_id,
                        p.platform_id,
                        p.salesman_id,
                        p.policyholder,
                        p.insured,
                        p.policy_file,
                        p.coverage,
                        p.service_charge,
                        p.rate,
                        p.premium,
                        p.platform_rate,
                        p.platform_premium,
                        p.platform_commission,
                        p.platform_commission_rate,
                        p.agent_commission,
                        p.agent_commission_rate,
                        p.agent_rate,
                        p.agent_premium,
                        p.user_rate,
                        p.user_premium,
                        NULL as pge_id,
                        NULL as policy_group_id,
                        p.order_no,
                        p.policy_no,
                        p.sticky_note,
                        p.is_premium_sync,
                        p.is_allowed_invoice,
                        p.status,
                        p.submitted_at,
                        p.issued_at,
                        p.created_at,
                        p.updated_at,
                        p.deleted_at
                    FROM
                        policies AS p
                    WHERE
                        p.`type` IN (1, 2, 3, 4, 6, 7)
                        AND p.deleted_at IS NULL
                    ) as d
            );

            CREATE TABLE IF NOT EXISTS `policy_aggregations_temporary` LIKE `policy_temporary`;
            INSERT INTO `policy_aggregations_temporary` SELECT * FROM `policy_temporary`;
        SQL;
    }
}
