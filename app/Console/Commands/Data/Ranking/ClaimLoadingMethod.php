<?php

namespace App\Console\Commands\Data\Ranking;

use App\Console\Commands\Data\Ranking\Concerns\InteractsWithClaim;
use Illuminate\Support\Collection;

class ClaimLoadingMethod extends DataTable
{
    use InteractsWithClaim;

    // { label: '厢式货车', value: 1 },
    //     { label: '非厢式货车', value: 2 },
    //     { label: '集装箱（拼箱）', value: 3 },
    //     { label: '集装箱（整箱）', value: 4 },
    //     { label: '非集装箱运输', value: 5 }

    /**
     * 装载方式
     *
     * @var array
     */
    protected array $loadingMethods = [
        -1 => '未知',
        0 => '未知',
        1 => '厢式货车',
        2 => '非厢式货车',
        3 => '集装箱（拼箱）',
        4 => '集装箱（整箱）',
        5 => '非集装箱运输',
    ];

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData()
            ->groupBy(fn($item) => $this->loadingMethods[$item['loading_method']])
            ->map(function ($item, $id) {
                return $this->marshal($id, $item);
            })
            ->values();
    }
}
