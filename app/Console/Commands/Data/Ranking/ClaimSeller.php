<?php

namespace App\Console\Commands\Data\Ranking;

use App\Console\Commands\Data\Ranking\Concerns\InteractsWithClaim;
use Illuminate\Support\Collection;


class ClaimSeller extends DataTable
{
    use InteractsWithClaim;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData()
            ->groupBy('buyer')
            ->map(function ($item, $id) {
                return $this->marshal($id, $item);
            })
            ->values();
    }
}
