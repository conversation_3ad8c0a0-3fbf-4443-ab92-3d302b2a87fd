<?php

namespace App\Console\Commands\Data\Ranking;

use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class Company extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData(['company_id'], ['company'])
            ->groupBy('company_id')
            ->map(function ($item, $id) {
                $claims = $this->loadClaims('company_id', $id);

                return $this->marshal($item->first()['company']['name'], $item, $claims);
            })
            ->values();
    }
}
