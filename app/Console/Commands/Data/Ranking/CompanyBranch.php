<?php

namespace App\Console\Commands\Data\Ranking;

use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class CompanyBranch extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData(['company_branch_id'], ['companyBranch'])
            ->groupBy('company_branch_id')
            ->map(function ($item, $id) {
                $claims = $this->loadClaims('company_branch_id', $id);

                return $this->marshal($item->first()['companyBranch']['name'], $item, $claims);
            })
            ->values();
    }
}
