<?php

namespace App\Console\Commands\Data\Ranking\Concerns;

use App\Enums\ProductFrom;
use App\Enums\DataRankingAccordingTo;
use App\Models\Claim;
use App\Models\Policy;
use Illuminate\Support\Collection;

trait InteractsWithClaim
{
    /**
     * 获取数据集
     *
     * @return  array
     */
    public function handle(): array
    {
        $dataset = [];
        $sourceData = $this->data();

        $dataset['by_claim_payment_amount'] = $sourceData->sortByDesc('claim_payment_amount')->take(100)->toArray();
        $dataset['by_five_ten_thousand_claim_amount_rate'] = $sourceData->sortByDesc('claim_five_ten_thousand_amount_rate')->take(100)->toArray();
        $dataset['by_claim_finished_rate'] = $sourceData->sortByDesc('claim_finished_rate')->take(100)->toArray();

        return $dataset;
    }

    /**
     * 整理数据
     *
     * @param  string     $primaryName
     * @param  Collection $claims
     *
     * @return array
     */
    protected function marshal(string $primaryName, Collection $claims): array
    {
        $claimsCount = $claims->count();

        $data = [
            'name' => $primaryName,
            'claims_count' => $claimsCount,
            'claim_payment_amount' => $this->claimPaymentAmount($claims),
            // 结案赔款
            'claim_finished_settlement_payment_amount' => $this->claimFinishedSettementPaymentAmount($claims),
            // 立案金额
            'claim_pending_settlement_amount' => $this->claimPendingSettlementAmount($claims),
            'claim_five_ten_thousand_amount_count' => $this->claimFiveTenThousandAmountCount($claims),
        ];

        $data['claim_five_ten_thousand_amount_rate'] = $this->claimFiveTenThousandAmountRate($data['claims_count'], $data['claim_five_ten_thousand_amount_count']);
        $data['claim_finished_rate'] = $this->claimFinishedRate($data['claims_count'], $claims);
        $data['claim_days'] = $this->avgClaimDays($claims);

        return $data;
    }

    /**
     * 获取数据集
     *
     * @param  array $with
     * @param  array $onlyCargo
     *
     * @return Collection
     */
    protected function sourceData(array $with = []): Collection
    {
        return Claim::when(count($with) > 0, fn($q) => $q->with($with))
            ->whereHas('policy', function ($q) {
                $q->when($this->accordingTo === DataRankingAccordingTo::OurProduct, function ($q) {
                    $q->where(function ($q) {
                        $q->where('platform_id', $this->platformId)
                            ->orWhere(function ($q) {
                                $q->where('platform_id', $this->platformId)
                                    ->whereHas('policyOffline', fn($q) => $q->where('product_from', ProductFrom::Our->value));
                            });
                    });
                }, function ($q) {
                    $q->where(function ($q) {
                        $q->whereHas('user', fn($q) => $q->where('platform_id', $this->platformId))
                            ->orWhere(function ($q) {
                                $q->where('platform_id', $this->platformId)
                                    ->whereHas('policyOffline', fn($q) => $q->where('product_from', ProductFrom::Thirdparty->value));
                            });
                    });
                })
                    ->when($this->year, fn($q) => $q->whereYear('submitted_at', $this->year))
                    ->where('status', Policy::STATUS_ISSUED);
            })
            ->with([
                'policyCoverageCurrency',
                'claimSettlementAmountCurrency',
                'claimLodgingFeeCurrency',
                'lossAmountCurrency',
                'settlementPaymentAmountCurrency',
                'settlementCostsCurrency',
                'company',
                'logs'
            ])
            ->get();
    }
}
