<?php

namespace App\Console\Commands\Data\Ranking\Concerns;

use App\Enums\DataRankingAccordingTo;
use App\Enums\ProductFrom;
use App\Models\Claim;
use App\Models\Policy;
use Illuminate\Support\Collection;

trait InteractsWithPolicy
{
    /**
     * 获取数据集
     *
     * @return  array
     */
    public function handle(): array
    {
        $dataset = [];
        $sourceData = $this->data();

        $dataset['by_policy_count'] = $sourceData->sortByDesc('policies_count')->take(100)->toArray();
        $dataset['by_premium'] = $sourceData->sortByDesc('premium')->take(100)->toArray();
        $dataset['by_rate_avg'] = $sourceData->sortByDesc('rate_avg')->take(100)->toArray();
        $dataset['by_claim_count'] = $sourceData->sortByDesc('claims_count')->take(100)->toArray();
        $dataset['by_claim_payment_rate'] = $sourceData->sortByDesc('claim_payment_rate')->take(100)->toArray();
        $dataset['by_claim_rate'] = $sourceData->sortByDesc('claim_rate')->take(100)->toArray();
        $dataset['by_claim_payment_amount'] = $sourceData->sortByDesc('claim_payment_amount')->take(100)->toArray();
        $dataset['by_five_ten_thousand_claim_amount_rate'] = $sourceData->sortByDesc('claim_five_ten_thousand_amount_rate')->take(100)->toArray();
        $dataset['by_claim_finished_rate'] = $sourceData->sortByDesc('claim_finished_rate')->take(100)->toArray();

        return $dataset;
    }

    /**
     * 整理数据
     *
     * @param  string     $primaryName
     * @param  array      $columns
     * @param  Collection $claims
     *
     * @return array
     */
    protected function marshal(string $primaryName, Collection $items, Collection $claims): array
    {
        $claimsCount = $claims->count();
        $policyCount = $items->count();
        $data = [
            'name' => $primaryName,
            'policies_count' => $policyCount,
            'claims_count' => $claimsCount,
            // 保费（元）
            'premium' => round($items->sum($this->premiumColumn()) / 100, 2),
            'rate_avg' => round($items->avg($this->rateColumn()), 2),
            // 年化费率增长，拿上一年度费率均值，和当前年度费率均值. 计算增长率
            'rate_yearly_increase_rate' => $this->getRateYearlyIncreaseRate($items),
            'claim_rate' => round(($policyCount > 0 ? $claimsCount / $policyCount : 0) * 1000, 2),
            // 赔款金额
            'claim_payment_amount' => $this->claimPaymentAmount($claims),
            // 已决金额
            'claim_finished_settlement_payment_amount' => $this->claimFinishedSettementPaymentAmount($claims),
            // 未决金额
            'claim_pending_settlement_amount' => $this->claimPendingSettlementAmount($claims),
            'claim_five_ten_thousand_amount_count' => $this->claimFiveTenThousandAmountCount($claims),
        ];

        $data['claim_payment_rate'] = $this->claimPaymentRate($claims, $data['premium'] * 100);
        $data['claim_five_ten_thousand_amount_rate'] = $this->claimFiveTenThousandAmountRate($data['claims_count'], $data['claim_five_ten_thousand_amount_count']);
        $data['claim_finished_rate'] = $this->claimFinishedRate($data['claims_count'], $claims);
        $data['claim_days'] = $this->avgClaimDays($claims);

        return $data;
    }

    /**
     * 加载理赔数据
     *
     * @param  string|callable  $column
     * @param  string|null      $value
     *
     * @return Collection
     */
    protected function loadClaims(string|callable $column, ?string $value = null)
    {
        return Claim::whereHas('policy', function ($query) use ($column, $value) {
            $query->when(
                is_callable($column),
                fn($query) => $column($query, $value),
                fn($query) => $query->where($column, $value)
            )
                ->when($this->accordingTo === DataRankingAccordingTo::OurProduct, function ($q) {
                    $q->where(function ($q) {
                        $q->where('platform_id', $this->platformId)
                            ->orWhere(function ($q) {
                                $q->where('platform_id', $this->platformId)
                                    ->whereHas('policyOffline', fn($q) => $q->where('product_from', ProductFrom::Our->value));
                            });
                    });
                }, function ($q) {
                    $q->where(function ($q) {
                        $q->whereHas('user', fn($q) => $q->where('platform_id', $this->platformId))
                            ->orWhere(function ($q) {
                                $q->where('platform_id', $this->platformId)
                                    ->whereHas('policyOffline', fn($q) => $q->where('product_from', ProductFrom::Thirdparty->value));
                            });
                    });
                })
                ->when($this->year, fn($q) => $q->whereYear('submitted_at', $this->year))
                ->where('status', Policy::STATUS_ISSUED);
        })
            ->with([
                'policyCoverageCurrency',
                'claimSettlementAmountCurrency',
                'claimLodgingFeeCurrency',
                'lossAmountCurrency',
                'settlementPaymentAmountCurrency',
                'settlementCostsCurrency',
                'company',
                'logs'
            ])
            ->get();
    }

    /**
     * 获取数据集
     *
     * @param  array $columns
     * @param  array $with
     * @param  array $onlyCargo
     *
     * @return Collection
     */
    protected function sourceData(array $columns = [], ?array $with = [], ?bool $onlyCargo = false): Collection
    {
        return Policy::when(count($with) > 0, fn($q) => $q->with($with))
            ->when($onlyCargo, fn($q) => $q->whereIn('type', [
                Policy::TYPE_DOMESTIC,
                Policy::TYPE_INTL,
                Policy::TYPE_CBEC,
            ]))
            ->when($this->accordingTo === DataRankingAccordingTo::OurProduct, function ($q) {
                $q->where(function ($q) {
                    $q->where('platform_id', $this->platformId)
                        ->orWhere(function ($q) {
                            $q->where('platform_id', $this->platformId)
                                ->whereHas('policyOffline', fn($q) => $q->where('product_from', ProductFrom::Our->value));
                        });
                });
            }, function ($q) {
                $q->where(function ($q) {
                    $q->whereHas('user', fn($q) => $q->where('platform_id', $this->platformId))
                        ->orWhere(function ($q) {
                            $q->where('platform_id', $this->platformId)
                                ->whereHas('policyOffline', fn($q) => $q->where('product_from', ProductFrom::Thirdparty->value));
                        });
                });
            })
            ->where('status', Policy::STATUS_ISSUED)
            ->when($this->year, fn($q) => $q->whereYear('submitted_at', $this->year))
            ->get(array_merge([
                'id',
                'rate',
                'platform_rate',
                'user_rate',
                'premium',
                'platform_premium',
                'user_premium',
                'issued_at'
            ], $columns));
    }
}
