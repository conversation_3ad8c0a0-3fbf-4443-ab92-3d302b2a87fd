<?php

namespace App\Console\Commands\Data\Ranking;

use App\Enums\DataRankingAccordingTo;
use App\Models\Claim;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;

abstract class DataTable
{
    /**
     * DataTable constructor.
     *
     * @param  int  $platformId
     * @param  DataRankingAccordingTo  $accordingTo
     * @param  int  $year
     *
     * @return void
     */
    public function __construct(protected int $platformId, protected DataRankingAccordingTo $accordingTo, protected ?int $year = null)
    {
    }

    /**
     * 费率年华增长率
     *
     * @param  \Illuminate\Database\Eloquent\Collection $items
     *
     * @return  mixed
     */
    protected function getRateYearlyIncreaseRate(Collection $items)
    {
        if ($this->year) {
            return 0;
        }

        $lastYear = Carbon::now()->subYear()->year;
        $currentYear = Carbon::now()->year;

        $lastYearAvgRate = $items->filter(
            fn($item) => Carbon::parse($item['issued_at'])->year === $lastYear
        )->avg($this->rateColumn());

        $currentYearAvgRate = $items->filter(
            fn($item) => Carbon::parse($item['issued_at'])->year === $currentYear
        )->avg($this->rateColumn());

        // 年化增长率为：=(期末值0.00024/期初值 0.0002) ^ 【1/（180天/365天）】 - 1 = 44%。
        if ($lastYearAvgRate <= 0) {
            return 0;
        }
        $diffDays = Carbon::now()->diffInDays(Carbon::now()->startOfYear()) + 1 ?: 1;

        return round(pow($currentYearAvgRate / $lastYearAvgRate, 1 / ($diffDays / Carbon::now()->daysInYear)) - 1, 2);
    }

    /**
     * 赔款金额.
     *
     * @param \Illuminate\Database\Eloquent\Collection $claims
     *
     * @return mixed
     */
    protected function claimPaymentAmount(Collection $claims)
    {
        $amount = $claims->map(function ($item) {
            if (in_array($item['status'], Claim::$typeStatus[Claim::TYPE_SETTLEMENT])) {
                return $item['settlement_payment_amount'] * $item['settlementPaymentAmountCurrency']['rate'];
            } else {
                return $item['claim_settlement_amount'] * $item['claimSettlementAmountCurrency']['rate'];
            }
        })->sum() ?: 0;

        return round($amount / 100, 2);
    }

    /**
     * 已决金额
     *
     * @param  \Illuminate\Database\Eloquent\Collection $claims
     *
     * @return mixed
     */
    protected function claimFinishedSettementPaymentAmount(Collection $claims)
    {
        $amount = $claims->map(function ($item) {
            if (in_array($item['status'], Claim::$typeStatus[Claim::TYPE_SETTLEMENT])) {
                return $item['settlement_payment_amount'] * $item['settlementPaymentAmountCurrency']['rate'];
            } else {
                return 0;
            }
        })->sum() / 100 ?: 0;

        return round($amount, 2);
    }

    /**
     * 未决金额
     *
     * @param  \Illuminate\Database\Eloquent\Collection $claims
     *
     * @return mixed
     */
    protected function claimPendingSettlementAmount(Collection $claims)
    {
        $amount = $claims->map(function ($item) {
            if (!in_array($item['status'], Claim::$typeStatus[Claim::TYPE_SETTLEMENT])) {
                return $item['claim_settlement_amount'] * $item['claimSettlementAmountCurrency']['rate'];
            } else {
                return 0;
            }
        })->sum() ?: 0;

        return round($amount / 100, 2);
    }

    /**
     * 五万元以上理赔金额案件数
     *
     * @param  \Illuminate\Database\Eloquent\Collection $claims
     *
     * @return int
     */
    protected function claimFiveTenThousandAmountCount(Collection $claims)
    {
        return $claims->filter(function ($item) {
            if (in_array($item['status'], Claim::$typeStatus[Claim::TYPE_SETTLEMENT])) {
                $amount = $item['settlement_payment_amount'] * $item['settlementPaymentAmountCurrency']['rate'];
            } else {
                $amount = $item['claim_settlement_amount'] && $item['claim_lodging_fee']
                    ?
                    (
                        $item['claim_settlement_amount'] * $item['claimSettlementAmountCurrency']['rate']
                        + $item['claim_lodging_fee'] * $item['claimLodgingFeeCurrency']['rate']
                    )
                    : $item['loss_amount'] * $item['lossAmountCurrency']['rate'];
            }

            return $amount >= 50000_00;
        })->count();
    }

    /**
     * 赔付率
     *
     * @param  \Illuminate\Database\Eloquent\Collection $claims
     * @param  float $premium
     *
     * @return float
     */
    protected function claimPaymentRate(Collection $claims, ?float $premium): float
    {
        if ($premium <= 0) {
            return 0;
        }

        $claimPaymentAmount = $claims->sum(function ($item) use (&$finishedCount) {
            // 在统计口径下，在理赔系统中在这（些）统计口径下所有已结理赔案件的结案金额之和（结案金额=结案赔款+结案费用）；
            if (in_array($item['status'], Claim::$typeStatus[Claim::TYPE_SETTLEMENT])) {
                return $item['settlement_costs'] * $item['settlementCostsCurrency']['rate']
                    + $item['settlement_payment_amount'] * $item['settlementPaymentAmountCurrency']['rate'];
            } else {
                // 在统计口径下，在理赔系统中，在这（些）统计口径下所有未结理赔案件的立案金额之和（立案金额=立案赔款+立案费用）；
                return $item['claim_settlement_amount'] * $item['claimSettlementAmountCurrency']['rate']
                    + $item['claim_lodging_fee'] * $item['claimLodgingFeeCurrency']['rate'];
            }
        });

        return round(($claimPaymentAmount / $premium) * 100, 2);
    }

    /**
     * 五万元以上理赔金额比例
     *
     * @param  int $count
     * @param  int|null $claimFiveTenThousandAmountCount
     * @return float
     */
    protected function claimFiveTenThousandAmountRate(int $count, ?int $claimFiveTenThousandAmountCount): float
    {
        if ($count <= 0) {
            return 0;
        }

        return round(($claimFiveTenThousandAmountCount ?? 0) / $count * 100, 2);
    }

    /**
     * 结案率
     *
     * @param  int $claimsCount
     * @param  Collection $claims
     *
     * @return float|int
     */
    protected function claimFinishedRate(int $claimsCount, Collection $claims)
    {
        if ($claimsCount <= 0) {
            return 0;
        }

        $rate = $claims->filter(function ($item) {
            return in_array($item['status'], Claim::$typeStatus[Claim::TYPE_SETTLEMENT]);
        })->count() / $claimsCount;

        return round($rate * 100, 2);
    }

    /**
     * 平均理赔时长.
     *
     * @param   Collection $claims
     *
     * @return  float
     */
    protected function avgClaimDays(Collection $claims)
    {
        $days = $claims
            ->whereIn('status', [
                Claim::STATUS_CLAIM_PAYMENT,
                Claim::STATUS_CANCELLED,
                Claim::STATUS_ZERO_CLAIM,
                Claim::STATUS_REFUSED,
                Claim::STATUS_CLAIM_SETTLEMENT,
                Claim::STATUS_ARCHIVED,
            ])
            ->filter(fn($item) => $item->logs->isNotEmpty())
            ->map(function ($item) {
                $endDate = $item->logs->whereIn('status', [
                    Claim::STATUS_CLAIM_PAYMENT,
                    Claim::STATUS_CANCELLED,
                    Claim::STATUS_ZERO_CLAIM,
                    Claim::STATUS_REFUSED,
                    Claim::STATUS_CLAIM_SETTLEMENT,
                ])->last();

                if (!$endDate) {
                    return Carbon::parse($item['created_at'])->diffInSeconds(Carbon::now());
                }

                return Carbon::parse($item['created_at'])->diffInSeconds($endDate->started_at);
            })
            ->avg();

        return round($days ? $days / 86400 : 0, 2);

    }

    /**
     * 费率字段.
     *
     * @return string
     */
    protected function rateColumn(): string
    {
        return $this->accordingTo === DataRankingAccordingTo::OurProduct ? 'rate' : 'platform_rate';
    }

    /**
     * 保费字段
     *
     * @return string
     */
    protected function premiumColumn(): string
    {
        return $this->accordingTo === DataRankingAccordingTo::OurProduct ? 'premium' : 'platform_premium';
    }

    /**
     * 获取数据源
     *
     * @return \Illuminate\Support\Collection
     */
    protected abstract function data();
}
