<?php

namespace App\Console\Commands\Data\Ranking;


use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class Destination extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData([], ['policyCargo'], true)
            ->groupBy(fn($item, $key) => explode(':', $item['policyCargo']['destination'] ?? '其他')[0])
            ->map(function ($item, $id) {
                $claims = $this->loadClaims(
                    fn($query) =>
                    $query->whereHas('policyCargo', fn($q) => $q->where('destination', 'like', $id . '%'))
                );

                return $this->marshal($id, $item, $claims);
            })
            ->values();
    }
}
