<?php

namespace App\Console\Commands\Data\Ranking;

use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class Holder extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData(['policyholder'])
            ->groupBy('policyholder')
            ->map(function ($item, $name) {
                $claims = $this->loadClaims('policyholder', $name);

                return $this->marshal($name, $item, $claims);
            })
            ->values();
    }
}
