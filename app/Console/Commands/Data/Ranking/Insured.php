<?php

namespace App\Console\Commands\Data\Ranking;

use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class Insured extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData(['insured', 'policy_no'])
            ->groupBy('insured')
            ->map(function ($item, $name) {
                $claims = $this->loadClaims('insured', $name);

                return $this->marshal($name, $item, $claims);
            })
            ->values();
    }
}
