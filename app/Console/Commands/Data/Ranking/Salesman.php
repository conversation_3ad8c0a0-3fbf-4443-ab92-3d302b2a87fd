<?php

namespace App\Console\Commands\Data\Ranking;

use App\Enums\DataRankingAccordingTo;
use App\Models\User;
use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use DB;
use Illuminate\Support\Collection;

class Salesman extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据集
     *
     * @return  array
     */
    public function handle(): array
    {
        $dataset = [];
        $sourceData = $this->data();

        $dataset['by_user'] = $this->byUser();
        $dataset['by_policy_count'] = $sourceData->sortByDesc('policies_count')->take(100)->toArray();
        $dataset['by_premium'] = $sourceData->sortByDesc('premium')->take(100)->toArray();
        $dataset['by_rate_avg'] = $sourceData->sortByDesc('rate_avg')->take(100)->toArray();
        $dataset['by_claim_count'] = $sourceData->sortByDesc('claims_count')->take(100)->toArray();
        $dataset['by_claim_payment_rate'] = $sourceData->sortByDesc('claim_payment_rate')->take(100)->toArray();
        $dataset['by_claim_rate'] = $sourceData->sortByDesc('claim_rate')->take(100)->toArray();
        $dataset['by_claim_payment_amount'] = $sourceData->sortByDesc('claim_payment_amount')->take(100)->toArray();
        $dataset['by_five_ten_thousand_claim_amount_rate'] = $sourceData->sortByDesc('claim_five_ten_thousand_amount_rate')->take(100)->toArray();
        $dataset['by_claim_finished_rate'] = $sourceData->sortByDesc('claim_finished_rate')->take(100)->toArray();

        return $dataset;
    }

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData(['salesman_id'], ['salesman'])
            ->groupBy('salesman_id')
            ->map(function ($item, $id) {
                $salesman = $item->first()['salesman']['name'];
                $claims = $this->loadClaims('salesman_id', $item->first()['salesman_id']);
                return $this->marshal($salesman, $item, $claims);
            })
            ->values();
    }

    /**
     * 准备用户相关数据.
     *
     * @return void
     */
    protected function prepare()
    {
        $whereCondition = $this->accordingTo === DataRankingAccordingTo::OurProduct
            ? " AND policies.platform_id = $this->platformId "
            : '';

        if ($this->year) {
            $whereCondition .= " AND user_id in (SELECT id FROM users WHERE platform_id = $this->platformId AND YEAR(created_at) = $this->year) ";
        }

        $validUsersSql = <<<SQL
            CREATE TEMPORARY TABLE temp_valid_users AS
            SELECT
                salesman_id,
                COUNT(DISTINCT user_id) AS valid_users_count
            FROM
                policies
            WHERE
                status = 5 $whereCondition
            GROUP BY
                salesman_id;
        SQL;

        $premiumColumn = $this->premiumColumn();
        $premiumSql = <<<SQL
            CREATE TEMPORARY TABLE temp_premium_users AS
            SELECT
                salesman_id,
                COUNT(DISTINCT user_id) AS ten_thousand_premium_count
            FROM
            (
                SELECT
                    salesman_id,
                    user_id,
                    SUM($premiumColumn) AS sum_premium
                FROM
                    policies
                WHERE
                    status = 5 $whereCondition
                GROUP BY
                    salesman_id,
                    user_id,
                    YEAR(created_at)
                HAVING
                    sum_premium > 1000000
            ) AS t
            GROUP BY
            salesman_id;
        SQL;

        try {
            DB::unprepared('DROP TEMPORARY TABLE temp_valid_users');
        } catch (\Exception $e) {
            // ignore
        }
        DB::unprepared($validUsersSql);

        try {
            DB::unprepared('DROP TEMPORARY TABLE temp_premium_users');
        } catch (\Exception $e) {
            // ignore
        }
        DB::unprepared($premiumSql);
    }

    /**
     * 用户数据
     *
     * @return array
     */
    protected function byUser()
    {
        $this->prepare();

        return User::select([
            'users.salesman_id',
            DB::raw('count(*) as users_count'),
            'temp_valid_users.valid_users_count',
            'temp_premium_users.ten_thousand_premium_count',
        ])
            ->leftJoin('temp_valid_users', 'users.salesman_id', '=', 'temp_valid_users.salesman_id')
            ->leftJoin('temp_premium_users', 'users.salesman_id', '=', 'temp_premium_users.salesman_id')
            ->orderBy('users_count', 'desc')
            ->groupBy('salesman_id')
            ->where('platform_id', $this->platformId)
            ->when($this->year, fn($q) => $q->whereYear('created_at', $this->year))
            ->get()
            ->map(function ($data) {
                return [
                    'name' => $data['salesman']['name'],
                    'users_count' => $data['users_count'],
                    'valid_users_count' => $data['valid_users_count'] ?: 0,
                    'ten_thousand_premium_count' => $data['ten_thousand_premium_count'] ?: 0,
                ];
            })
            ->toArray();
    }
}
