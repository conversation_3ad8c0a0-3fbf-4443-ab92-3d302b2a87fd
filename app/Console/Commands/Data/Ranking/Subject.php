<?php

namespace App\Console\Commands\Data\Ranking;


use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class Subject extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData([], ['policyCargo.subject'], true)
            ->groupBy(fn($item, $key) => $item['policyCargo']['subject_id'] ?? -1)
            ->map(function ($item, $id) {
                $claims = $this->loadClaims(
                    fn($query) =>
                    $query->whereHas('policyCargo', fn($q) => $q->where('subject_id', $item->first()['policyCargo']['subject_id'] ?? '-1'))
                );

                return $this->marshal($item->first()['policyCargo']['subject']['name'] ?? '未知', $item, $claims);
            })
            ->values();
    }
}
