<?php

namespace App\Console\Commands\Data\Ranking;

use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class TransportMethod extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData([], ['policyCargo.transportMethod'], true)
            ->groupBy(fn($item, $key) => $item['policyCargo']['transportMethod']['name'] ?? '未定义运输方式')
            ->map(function ($item, $id) {
                $transportMethodIds = $item->pluck('policyCargo.transport_method_id')
                    ->unique()
                    ->toArray();

                $claims = $this->loadClaims(
                    fn($query) =>
                    $query->whereHas('policyCargo', fn($q) => $q->whereIn('transport_method_id', $transportMethodIds))
                );

                return $this->marshal($id, $item, $claims);
            })
            ->values();
    }
}
