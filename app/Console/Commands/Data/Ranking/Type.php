<?php

namespace App\Console\Commands\Data\Ranking;


use App\Models\Policy;
use App\Console\Commands\Data\Ranking\Concerns\InteractsWithPolicy;
use Illuminate\Support\Collection;

class Type extends DataTable
{
    use InteractsWithPolicy;

    /**
     * 获取数据源
     *
     * @return Collection
     */
    protected function data(): Collection
    {
        return $this->sourceData(['type'])
            ->groupBy('type')
            ->map(function ($item, $id) {
                $claims = $this->loadClaims('type', $id);

                return $this->marshal(Policy::$types[$item->first()['type']] ?? '-', $item, $claims);
            })
            ->values();
    }
}
