<?php

namespace App\Console\Commands\Data;

use App\Enums\DataRankingAccordingTo;
use App\Models\Platform;
use App\Models\Policy;
use Arr;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class RankingCommand extends Command
{
    /**
     * 缓存键名
     *
     * @var string
     */
    const DATA_CACHE_KEY = 'data:ranking:%d:%s:%s';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:ranking
                            {--p|platform=* : 平台 ID}
                            {--b|by=* : 排行依据}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '[数据] 统计排行榜';

    /**
     * 计算类
     *
     * @var array
     */
    protected $runners = [
        'index' => Ranking\Index::class,
        'policyholder' => Ranking\Holder::class,
        'insured' => Ranking\Insured::class,
        'salesman' => Ranking\Salesman::class,
        'agent' => Ranking\Agent::class,
        'user' => Ranking\User::class,
        'loading_method' => Ranking\LoadingMethod::class,
        'claim_loading_method' => Ranking\ClaimLoadingMethod::class,
        'packing_method' => Ranking\PackagingMethod::class,
        'claim_packaging_method' => Ranking\ClaimPackagingMethod::class,
        'transport_method' => Ranking\TransportMethod::class,
        'departure' => Ranking\Departure::class,
        'destination' => Ranking\Destination::class,
        'trade_type' => Ranking\TradeType::class,
        'type' => Ranking\Type::class,
        'claim_loss_reason' => Ranking\ClaimLossReason::class,
        'claim_operator' => Ranking\ClaimOperator::class,
        'claim_seller' => Ranking\ClaimSeller::class,
        'claim_buyer' => Ranking\ClaimBuyer::class,
        'claim_external_adjuster' => Ranking\ClaimExternalAdjuster::class,
        'subject' => Ranking\Subject::class,
        'company' => Ranking\Company::class,
        'company_branch' => Ranking\CompanyBranch::class,
    ];

    /**
     * Handle the command.
     *
     *
     * @return void
     */
    public function handle()
    {
        ini_set('memory_limit', '6144M');

        $platforms = Platform::when(
            $this->option('platform'),
            fn($q, $p) => $q->whereIn('id', Arr::wrap($p))
        )->get();

        $runners = $this->option('by')
            ? array_filter($this->runners, fn($by) => in_array($by, $this->option('by')), ARRAY_FILTER_USE_KEY)
            : $this->runners;

        $progressBar = $this->output->createProgressBar($platforms->count() * count($runners) * 2);
        foreach ($platforms as $platform) {
            foreach ($runners as $by => $runner) {
                $progressBar->advance();
                foreach ([DataRankingAccordingTo::OurProduct, DataRankingAccordingTo::OurUser,] as $accordingTo) {
                    $this->executeRunner($runner, $platform->id, $by, $accordingTo);

                    $progressBar->advance();
                }
            }
        }
        $progressBar->finish();
    }

    /**
     * 执行计算
     *
     * @param  string  $runner
     * @param  int  $platformId
     * @param  string  $by
     * @param  DataRankingAccordingTo  $accordingTo
     *
     * @return void
     */
    protected function executeRunner($runner, int $platformId, string $by, DataRankingAccordingTo $accordingTo): void
    {
        // 所有年度的
        Cache::put(
            sprintf(self::DATA_CACHE_KEY, $platformId, $by, $accordingTo->value),
            (new $runner($platformId, $accordingTo))->handle()
        );

        $years = Policy::whereHas('user', fn($q) => $q->where('platform_id', $platformId))
            ->where('status', Policy::STATUS_ISSUED)
            ->groupByRaw('year(submitted_at)')
            ->selectRaw('year(submitted_at) as year')
            ->orderByDesc('year')
            ->get()
            ->pluck('year')
            ->filter()
            ->take(2)
            ->toArray();

        foreach ($years as $year) {
            Cache::put(
                sprintf(self::DATA_CACHE_KEY, $platformId, $by, $accordingTo->value . ':' . $year),
                (new $runner($platformId, $accordingTo, $year))->handle()
            );
        }
    }
}
