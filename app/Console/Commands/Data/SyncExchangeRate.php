<?php

namespace App\Console\Commands\Data;

use App\Models\Currency;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use LogicException;
use UnexpectedValueException;

class SyncExchangeRate extends Command
{
    /**
     * 数据源. (https://www.chinamoney.com.cn/chinese/bkccpr/)
     *
     * @var string
     */
    protected const DATA_API = 'https://www.chinamoney.com.cn/ags/ms/cm-u-bk-ccpr/CcprHisNew';

    /**
     * 支持的货币.
     *
     * @var array<string>
     */
    protected const SUPPORTED_CURRENCIES = [
        'USD/CNY',
        'EUR/CNY',
        '100JPY/CNY',
        'HKD/CNY',
        'GBP/CNY',
        'AUD/CNY',
        'NZD/CNY',
        'SGD/CNY',
        'CHF/CNY',
        'CAD/CNY',
        'CNY/MOP',
        'CNY/MYR',
        'CNY/RUB',
        'CNY/ZAR',
        'CNY/KRW',
        'CNY/AED',
        'CNY/SAR',
        'CNY/HUF',
        'CNY/PLN',
        'CNY/DKK',
        'CNY/SEK',
        'CNY/NOK',
        'CNY/TRY',
        'CNY/MXN',
        'CNY/THB'
    ];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'data:sync-exchange-rate';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '[数据] 同步汇率';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        $date = Carbon::now()->startOfMonth()->format('Y-m-d');
        $this->info("开始同步 {$date} 汇率");
        $exchangeRates = $this->exchangeRates();
        $failedCurrencies = [];
        foreach (Currency::$currencies as $code => $name) {
            if (isset($exchangeRates[$code])) {
                if ($exchangeRates[$code] === 0) {
                    $failedCurrencies[$code];
                    continue;
                }

                Currency::updateOrCreate([
                    'code' => $code,
                    'date' => $date,
                ], [
                    'name' => $name,
                    'rate' => $exchangeRates[$code],
                ]);

                $this->info("同步汇率 {$code} 成功 1 {$code} = {$exchangeRates[$code]} CNY");
            } else {
                $this->error("同步汇率 {$code} 失败");
            }
        }

        $this->info("同步 {$date} 汇率完成");

        if (count($failedCurrencies) > 0) {
            $this->error('同步汇率失败的货币: ' . implode(', ', array_keys($failedCurrencies)));
            throw new LogicException(implode(', ', array_keys($failedCurrencies)) . '同步汇率失败');
        }
    }

    /**
     * 解析数据源数据.
     *
     * @return  array
     */
    protected function exchangeRates()
    {
        $sourceData = $this->fetchSourceData();
        $exchangeRates = [
            'CNY' => 1.00000,
        ];
        $records = $sourceData['records'][0]['values'] ?? [];
        if (count($records) !== count($sourceData['data']['head'])) {
            throw new UnexpectedValueException('同步汇率失败，数据源数据不完整');
        }

        foreach ($sourceData['data']['head'] as $idx => $code) {
            $codes = explode('/', $code);
            switch ($codes[0]) {
                case '100JPY':
                    $exchangeRates['JPY'] = is_numeric($records[$idx]) ? bcdiv($records[$idx], 100, 5) : 0;
                    break;
                case 'CNY':
                    $exchangeRates[$codes[1]] = is_numeric($records[$idx]) ? bcdiv(1, $records[$idx], 5) : 0;
                    break;
                default:
                    $exchangeRates[$codes[0]] = is_numeric($records[$idx]) ? $records[$idx] : 0;
                    break;
            }
        }

        return $exchangeRates;
    }

    /**
     * 获取数据源数据.
     *
     * @return  array
     */
    protected function fetchSourceData(): array
    {
        $wants = Carbon::now()->subMonth()->endOfMonth();

        // 目前最长的节假日也就是8天, 所以最多尝试10次。
        $maxTries = 10;
        while ($maxTries-- > 0) {
            $data = $this->fetchFromApi($wants->format('Y-m-d'));
            if (count($data['records']) === 0) {
                $this->warn("{$wants->format('Y-m-d')} 没有数据");
                $wants = $wants->subDay();
                continue;
            }
            $this->info("{$wants->format('Y-m-d')} 有数据");

            return $data;
        }

        return [];
    }

    /**
     * 从 API 获取数据.
     *
     * @param  string  $day
     *
     * @return mixed
     */
    protected function fetchFromApi(string $day)
    {
        return Http::asForm()->post(self::DATA_API, [
            'startDate' => $day,
            'endDate' => $day,
            'currency' => implode(',', self::SUPPORTED_CURRENCIES),
            'pageNum' => 1,
            'pageSize' => 10
        ])->throw()->json();
    }
}
