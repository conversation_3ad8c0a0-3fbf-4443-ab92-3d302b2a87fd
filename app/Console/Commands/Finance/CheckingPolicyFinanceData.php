<?php

namespace App\Console\Commands\Finance;

use App\Models\Policy;
use App\Services\Finance\CreateFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Log;
use Mail;

class CheckingPolicyFinanceData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:checking-policy-finance-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            // 获取当月的第一天和当前日期
            $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d H:i:s');
            $endOfMonth = Carbon::now()->endOfMonth()->format('Y-m-d H:i:s');

            // 查询当月创建的符合条件的保单，并预加载相关数据
            $builder = Policy::with('policyFinances')  // 预加载 policyFinances
                ->whereNotIn('policy_no', ['PYIE202544011607E31471'])
                ->whereIn('type', [Policy::TYPE_DOMESTIC, Policy::TYPE_INTL, Policy::TYPE_CBEC])
                ->whereIn('status', [Policy::STATUS_ISSUED, Policy::STATUS_CANCELLED, Policy::STATUS_REVISION, Policy::STATUS_CANCELLING])
                ->whereBetween('created_at', [$startOfMonth, $endOfMonth])  // 当月的数据
                ->doesntHave('policyFinances');

            $count = $builder->count();  // 统计总数

            $progressBar = $this->output->createProgressBar($count);

            $missingFinances = [];

            // 分批处理每一条保单数据
            $builder->eachById(function ($policy) use ($progressBar, &$missingFinances) {

                try {
                    // 执行财务创建操作
                    CreateFinances::handle($policy, CreateFinances::TYPE_INSURE, $policy['premium'], $policy['platform_premium'], $policy['agent_premium'], $policy['user_premium']);
                    $missingFinances[] = $policy['policy_no'];
                } catch (\Exception $e) {
                    Log::error('财务创建失败', [
                        'policy_no' => $policy['policy_no'],
                        'error' => $e->getMessage(),
                    ]);
                } finally {
                    $progressBar->advance();
                }

                $progressBar->advance();  // 更新进度条
            });

            $progressBar->finish();

            if (!empty($missingFinances)) {
                $policyNumbers = implode(",", $missingFinances);
                Mail::raw('处理保单号: ' . $policyNumbers, function ($mail) {
                    $mail->to('<EMAIL>')
                        ->subject('财务记录缺失自动补充提醒');
                });
            }
        });

        return 0;
    }
}
