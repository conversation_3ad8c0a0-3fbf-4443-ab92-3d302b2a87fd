<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\User;
use App\Services\Finance\CreateFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class FixCargoFinanceData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-cargo-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复货运险财务数据';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '512M');
        DB::transaction(function () {
            $count = Policy::whereIn('type', [1, 2])->whereIn('status', [5, 6])->count();
            $progressBar = $this->output->createProgressBar($count);
            Policy::with(['user', 'product'])
                ->whereIn('type', [1, 2])
                ->whereIn('status', [5, 6])
                ->each(function ($policy) use ($progressBar) {
                    $res = $this->clearData($policy['id']);
                    if ($res) {
                        CreateFinances::handle($policy, 1, $policy['premium'], $policy['platform_premium'], $policy['agent_premium'], $policy['user_premium']);
                    }
                    if ($policy['status'] === Policy::STATUS_CANCELLED) {
                        CreateFinances::handle($policy, 3, -$policy['premium'], -$policy['platform_premium'], -$policy['agent_premium'], -$policy['user_premium']);
                    }
                    $progressBar->advance();
                });
            $progressBar->finish();
        });
    }

    protected function getNo($model, $prefix)
    {
        $sn = order_no($prefix);

        if (PremiumReceivable::where('order_no', $sn)->exists()) {

            $orderNo = $this->getNo($model, $sn);

            return $orderNo;
        }
    }

    protected function clearData($policyId)
    {
        $res = true;
        $models = [PremiumReceivable::class, PremiumPayment::class, CommissionReceivable::class, CommissionPayment::class, PoundagePayment::class];
        foreach ($models as $mode) {
            $model = new $mode();

            if ($model->where('policy_id', $policyId)->whereNotNull('bill_id')->exists()) {
                $res = false;
            }

            $model->where('policy_id', $policyId)
                ->whereNull('bill_id')
                ->delete();
        }
        return $res;
    }
}
