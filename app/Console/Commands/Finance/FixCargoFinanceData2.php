<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\User;
use App\Services\Finance\FixFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class FixCargoFinanceData2 extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-cargo-data2';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复货运险财务数据2';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '512M');
        DB::transaction(function () {
            $policyIds = $this->getPolicyIds();
            $count = Policy::whereIn('id', $policyIds)->count();
            $progressBar = $this->output->createProgressBar($count);
            Policy::with(['user', 'product'])
                ->whereIn('id', $policyIds)
                ->each(function ($policy) use ($progressBar) {
                    $res = $this->clearData($policy['id']);
                    if ($res) {
                        FixFinances::handle($policy, 1, $policy['premium'], $policy['platform_premium'], $policy['agent_premium'], $policy['user_premium']);
                    }
                    if ($policy['status'] === Policy::STATUS_CANCELLED) {
                        FixFinances::handle($policy, 3, -$policy['premium'], -$policy['platform_premium'], -$policy['agent_premium'], -$policy['user_premium']);
                    }
                    $progressBar->advance();
                });
            $progressBar->finish();
        });
    }

    protected function getNo($model, $prefix)
    {
        $sn = order_no($prefix);

        if (PremiumReceivable::where('order_no', $sn)->exists()) {

            $orderNo = $this->getNo($model, $sn);

            return $orderNo;
        }
    }

    protected function clearData($policyId)
    {
        $res = true;
        $models = [PremiumReceivable::class, PremiumPayment::class, CommissionReceivable::class, CommissionPayment::class, PoundagePayment::class];
        foreach ($models as $mode) {
            $model = new $mode();

            //            if($model->where('policy_id', $policyId)->whereNotNull('bill_id')->exists()){
            //                $res = false;
            //            }

            $model->where('policy_id', $policyId)
                ->whereNull('bill_id')
                ->delete();
        }
        return $res;
    }

    protected function getPolicyIds()
    {
        $models = [PremiumReceivable::class, PremiumPayment::class, CommissionReceivable::class, CommissionPayment::class, PoundagePayment::class];
        $ids = [];
        foreach ($models as $mode) {
            $model = new $mode();

            $ids = array_merge($ids, $model->whereNotNull('bill_id')
                ->groupBy('policy_id')
                ->pluck('policy_id')
                ->toArray());
        }

        return $ids;
    }
}
