<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\Policy;
use App\Models\PolicyGroupEndorse;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\User;
use App\Services\Finance\FixFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixCommissionPaymentData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-commission-payment-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new CommissionPayment())
                ->where('premium', '>', 0)
                ->whereHas('policyGroupEndorse', function ($q){
                    $q->where('total_fee', '<', 0);
                })->count();
            $progressBar = $this->output->createProgressBar($count);
            $this->info('CommissionPayment - START');
            (new CommissionPayment())
                ->where('premium', '>', 0)
                ->whereHas('policyGroupEndorse', function ($q){
                    $q->where('total_fee', '<', 0);
                })->eachById(function ($model) use ($progressBar) {
                    $model->premium = -$model->premium;
                    $model->cost_premium = -$model->cost_premium;
                    $model->platform_premium = -$model->platform_premium;
                    $model->agent_premium = -$model->agent_premium;
                    $model->user_premium = -$model->user_premium;
                    $model->commission = -$model->commission;
                    $model->save();
                    $progressBar->advance();
                });
            $progressBar->finish();

        });
    }
}
