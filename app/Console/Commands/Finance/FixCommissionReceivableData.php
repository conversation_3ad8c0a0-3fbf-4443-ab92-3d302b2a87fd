<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixCommissionReceivableData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-commission-receivable-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new CommissionReceivable())
                ->where('commission_receivables.type', 3)
                ->whereHas('policy', function ($q) {
                    $q->where('policies.type', 5);
                })->count();
            $progressBar = $this->output->createProgressBar($count);
            $this->info('CommissionReceivable - START');
            (new CommissionReceivable())
                ->with(['policy', 'endorse'])
                ->where('commission_receivables.type', 3)
                ->whereHas('policy', function ($q) {
                    $q->where('policies.type', 5);
                })->eachById(function ($model) use ($progressBar) {
                    if ($model['policy']['status'] != 6 && $model['endorse']['total_fee'] > 0) {
                        $model->premium = abs($model->premium);
                        $model->cost_premium = abs($model->cost_premium);
                        $model->platform_premium = abs($model->platform_premium);
                        $model->agent_premium = abs($model->agent_premium);
                        $model->user_premium = abs($model->user_premium);
                        $model->commission = abs($model->commission);
                    }
                    $model->type = 4;
                    $model->save();

                    $progressBar->advance();
                });
            $progressBar->finish();

        });
    }
}
