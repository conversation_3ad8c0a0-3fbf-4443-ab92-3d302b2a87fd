<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionPaymentBill;
use App\Models\CommissionReceivable;
use App\Models\CommissionReceivableBill;
use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\PoundagePaymentBill;
use App\Models\PremiumPayment;
use App\Models\PremiumPaymentBill;
use App\Models\PremiumReceivable;
use App\Models\PremiumReceivableBill;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixFinanceBills extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-bills-add-relations';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {


        DB::transaction(function () {
            $progressBar = $this->output->createProgressBar((new PremiumReceivableBill())->count());
            $this->info('PremiumReceivableBill - START');
            (new PremiumReceivableBill())->eachById(function ($model) use ($progressBar) {
                $ids = PremiumReceivable::where('bill_id', $model['id'])->pluck('id');
                $model->receivables()->attach($ids);
                $model->save();

                $progressBar->advance();
            });
            $progressBar->finish();

            $progressBar2 = $this->output->createProgressBar((new PremiumPaymentBill())->count());
            $this->info('PremiumPaymentBill - START');
            (new PremiumPaymentBill())->eachById(function ($model) use ($progressBar2) {
                $ids = PremiumPayment::where('bill_id', $model['id'])->pluck('id');
                $model->payment()->attach($ids);
                $model->save();

                $progressBar2->advance();
            });
            $progressBar2->finish();
//
            $progressBar3 = $this->output->createProgressBar((new PoundagePaymentBill())->count());
            $this->info('PoundagePaymentBill - START');
            (new PoundagePaymentBill())->eachById(function ($model) use ($progressBar3) {
                $ids = PoundagePayment::where('bill_id', $model['id'])->pluck('id');
                $model->payment()->attach($ids);
                $model->save();

                $progressBar3->advance();
            });
            $progressBar3->finish();

            $progressBar4 = $this->output->createProgressBar((new CommissionReceivableBill())->count());
            $this->info('CommissionReceivableBill - START');
            (new CommissionReceivableBill())->eachById(function ($model) use ($progressBar4) {
                $ids = CommissionReceivable::where('bill_id', $model['id'])->pluck('id');
                $model->receivables()->attach($ids);
                $model->save();

                $progressBar4->advance();
            });
            $progressBar4->finish();

            $progressBar5 = $this->output->createProgressBar((new CommissionPaymentBill())->count());
            $this->info('CommissionPaymentBill - START');
            (new CommissionPaymentBill())->eachById(function ($model) use ($progressBar5) {
                $ids = CommissionPayment::where('bill_id', $model['id'])->pluck('id');
                $model->payment()->attach($ids);
                $model->save();

                $progressBar5->advance();
            });
            $progressBar5->finish();
        });
    }
}
