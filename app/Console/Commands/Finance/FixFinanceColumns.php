<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Policy;
use App\Models\PolicyFinance;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\PremiumReceivableBill;
use App\Models\Settlement;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixFinanceColumns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = PolicyFinance::with('policy')
                ->count();
            $progressBar = $this->output->createProgressBar($count);
            PolicyFinance::with('policy')
                ->eachById(function ($policyFinance) use ($progressBar) {
                    $policyFinance['company_id'] = $policyFinance['policy']['company_id'] ?? -1;
                    $policyFinance['company_branch_id'] = $policyFinance['policy']['company_branch_id'] ?? -1;
                    $policyFinance['channel_id'] = $policyFinance['policy']['channel_id'] ?? -1;
                    $policyFinance['salesman_id'] = $policyFinance['policy']['salesman_id'] ?? -1;
                    $policyFinance['policyholder'] = $policyFinance['policy']['policyholder'] ?? '';
                    $policyFinance['insured'] = $policyFinance['policy']['insured'] ?? '';
                    $policyFinance['rate'] = $policyFinance['policy']['rate'] ?? 0;
                    $policyFinance['platform_rate'] = $policyFinance['policy']['platform_rate'] ?? 0;
                    $policyFinance['agent_rate'] = $policyFinance['policy']['agent_rate'] ?? 0;
                    $policyFinance['user_rate'] = $policyFinance['policy']['user_rate'] ?? 0;
                    $policyFinance['platform_commission_rate'] = $policyFinance['policy']['platform_commission_rate'] ?? 0;
                    $policyFinance['agent_commission_rate'] = $policyFinance['policy']['agent_commission_rate'] ?? 0;
                    $policyFinance->save();

                    $progressBar->advance();
                });

            $progressBar->finish();
        });
    }
}
