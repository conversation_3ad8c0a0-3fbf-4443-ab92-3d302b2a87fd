<?php

namespace App\Console\Commands\Finance;

use App\Models\PolicyFinance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixFinanceCommissionData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-commission';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new PolicyFinance())
                ->where('agent_premium', '<', 0)
                ->where('agent_commission', '>=', 0)
                ->whereHas('policy', function ($q){
                    $q->where('agent_commission_rate', '!=', 0);
                })
                ->count();
            $progressBar = $this->output->createProgressBar($count);
            $this->info('FIX-AGENT-COMMISSION-START');
            (new PolicyFinance())->with('policy')
                ->where('agent_premium', '<', 0)
                ->where('agent_commission', '>=', 0)
                ->whereHas('policy', function ($q){
                    $q->where('agent_commission_rate', '!=', 0);
                })
                ->eachById(function ($model) use ($progressBar) {
                $model['agent_commission'] = round(bcmul($model['agent_premium'], bcdiv($model['policy']['agent_commission_rate'], 100, 5), 5), 2);
                $model->save();

                $progressBar->advance();
            });
            $progressBar->finish();

            $count2 = (new PolicyFinance())
                ->where('platform_premium', '<', 0)
                ->where('platform_commission', '>=', 0)
                ->whereHas('policy', function ($q){
                    $q->where('platform_commission_rate', '!=', 0);
                })
                ->count();
            $progressBar2 = $this->output->createProgressBar($count2);
            $this->info('FIX-PLATFORM-COMMISSION-START');
            (new PolicyFinance())->with('policy')
                ->where('platform_premium', '<', 0)
                ->where('platform_commission', '>=', 0)
                ->whereHas('policy', function ($q){
                    $q->where('platform_commission_rate', '!=', 0);
                })
                ->eachById(function ($model) use ($progressBar2) {
                    $model['platform_commission'] = round(bcmul($model['platform_premium'], bcdiv($model['policy']['platform_commission_rate'], 100, 5), 5), 2);
                    $model->save();

                    $progressBar2->advance();
                });
            $progressBar2->finish();

        });
    }
}
