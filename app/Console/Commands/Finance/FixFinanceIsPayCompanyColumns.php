<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Policy;
use App\Models\PolicyFinance;
use App\Models\PolicyOffline;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\PremiumReceivableBill;
use App\Models\Settlement;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixFinanceIsPayCompanyColumns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-premium-is-pay-company-columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = PolicyFinance::with('policy')
                ->count();
            $progressBar = $this->output->createProgressBar($count);
            PolicyFinance::with('policy')
                ->eachById(function ($policyFinance) use ($progressBar) {
                    $policyFinance['premium_is_pay_company'] = $this->getData($policyFinance) ?? 0;
                    $policyFinance->save();

                    $progressBar->advance();
                });

            $progressBar->finish();
        });
    }

    protected function getData($finance)
    {
        if ($finance['policy'] === Policy::TYPE_OFFLINE) {
            return $finance['policy']['policyOffline']['premium_pay_type'] === PolicyOffline::PREMIUM_TYPE_PAID ? 1 : 0;
        }

        if ($this->isSettlement($finance) && $finance['premiumReceivable']->settlement()->exists() && $finance['premiumReceivable']['settlement']['type'] === Settlement::TYPE_PAID_OF_COMPANY) {
            return 1;
        }

        if (!empty($finance['premiumPayment'])) {
            return (!empty($finance['premiumPayment']['bill_id'] ?? null) && $finance['premiumPayment']['bill']['status'] ?? null == 2 && !empty($finance['premiumPayment']['bill']['proof'] ?? null)) ? 1 : 0;
        }

        //        $paymentsCount = $policy['premiumPayments']->whereNotNull('bill_id')->count();
        if ($finance->policy()->exists() && $finance['policy']->premiumPayments()->exists()) {

            $paymentsCount = optional($finance['policy']['premiumPayments'])->filter(function ($item) {
                    return !empty($item['bill_id']) && $item['bill']['status'] == 2 && !empty($item['bill']['proof']);
                })->count() ?? 0;

            return $paymentsCount > 0 ? 1 : 0;
        } else {
            return 0;
        }
    }

    protected function isSettlement($finance)
    {
        if (empty($finance['premiumReceivable'])) {
            return false;
        }

        return optional($finance['premiumReceivable'])->settlement_id !== -1;
    }
}
