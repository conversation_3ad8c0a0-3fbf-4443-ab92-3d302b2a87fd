<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Policy;
use App\Models\PolicyFinance;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\User;
use App\Services\Finance\CreateFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixFinancePolicyNoData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-policy_nos';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new Policy())
                ->whereIn('type', [1, 2])
                ->whereHas('policyFinances', function ($q){
                    $q->whereColumn('policy_finances.policy_id', 'policies.id')
                        ->whereColumn('policy_finances.policy_no', '!=', 'policies.policy_no');
                })
                ->whereHas('policyCargo', function ($q){
                    $q->where('policy_cargos.subject_id', '!=', 3);
                })
                ->count();

            $progressBar = $this->output->createProgressBar($count);
            $this->info('START');
            (new Policy())
                ->whereIn('type', [1, 2])
                ->with(['policyFinances', 'policyCargo'])
                ->whereHas('policyFinances', function ($q){
                    $q->whereColumn('policy_finances.policy_id', 'policies.id')
                        ->whereColumn('policy_finances.policy_no', '!=', 'policies.policy_no');
                })
                ->whereHas('policyCargo', function ($q){
                    $q->where('policy_cargos.subject_id', '!=', 3);
                })
                ->eachById(function ($policy) use ($progressBar) {
                    if($policy['policy_no'] != $policy['policyFinances'][0]['policy_no']){
                        $newPolicy = clone $policy;
                        $policy['policy_no'] = $policy['policyFinances'][0]['policy_no'];

                        CreateFinances::handle(
                            $policy,
                            CreateFinances::TYPE_MODIFY,
                            -$policy['premium'],
                            -$policy['platform_premium'],
                            -$policy['agent_premium'],
                            -$policy['user_premium']
                        );

                        CreateFinances::handle(
                            $newPolicy,
                            CreateFinances::TYPE_MODIFY,
                            $newPolicy['premium'],
                            $newPolicy['platform_premium'],
                            $newPolicy['agent_premium'],
                            $newPolicy['user_premium']
                        );

                    }
                $progressBar->advance();
            });
            $progressBar->finish();

        });
    }
}
