<?php

namespace App\Console\Commands\Finance;

use App\Models\PolicyFinance;
use App\Models\PoundagePayment;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixFinancePoundageData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-poundage';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new PolicyFinance())
                ->groupBy('policy_id')
                ->havingRaw('count(policy_id) = ?', [1])
                ->count();
            $progressBar = $this->output->createProgressBar($count);
            (new PolicyFinance())->with('policy')
                ->groupBy('policy_id')
                ->havingRaw('count(policy_id) = ?', [1])
                ->eachById(function ($model) use ($progressBar) {
                    if(PoundagePayment::where('policy_id', $model['policy_id'])
                        ->where('platform_id', $model['platform_id'])
                        ->groupBy('policy_id')
                        ->havingRaw('count(policy_id) = ?', [1])
                        ->exists())
                    {
                        $poundage = PoundagePayment::where('policy_id', $model['policy_id'])->where('platform_id', $model['platform_id'])->first();
                        if($poundage['type'] == 1 && $poundage['premium'] == $model['premium'] && $poundage['poundage'] == $model['poundage']){
                            $model['poundage_id'] = $poundage['id'];
                            $model->save();
                        }
                    }

                $progressBar->advance();
            });
            $progressBar->finish();
        });
    }
}
