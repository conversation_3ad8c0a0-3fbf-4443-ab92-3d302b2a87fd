<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Policy;
use App\Models\PolicyFinance;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixFinanceUserPremium extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-finance-user-premium';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            //            $progressBar = $this->output->createProgressBar((new PremiumReceivable())->count());
            //            $this->info('PremiumReceivable - START');
            //            (new PremiumReceivable())->with('policy')->eachById(function ($model) use ($progressBar) {
            //                if($model['cost_premium'] == $model['policy']['premium'] && $model['platform_premium'] == $model['policy']['platform_premium'] && $model['agent_premium'] != $model['policy']['agent_premium'] && $model['user_premium'] != $model['policy']['user_premium']){
            //                    $model->agent_premium = $model['policy']['agent_premium'];
            //                    $model->user_premium = $model['policy']['user_premium'];
            //                    $model->save();
            //                }
            //
            //                $progressBar->advance();
            //            });
            //            $progressBar->finish();
            //
            //            $progressBar2 = $this->output->createProgressBar((new PremiumPayment())->count());
            //            $this->info('PremiumPayment - START');
            //            (new PremiumPayment())->with('policy')->eachById(function ($model) use ($progressBar2) {
            //                if($model['cost_premium'] == $model['policy']['premium'] && $model['platform_premium'] == $model['policy']['platform_premium'] && $model['agent_premium'] != $model['policy']['agent_premium'] && $model['user_premium'] != $model['policy']['user_premium']){
            //                    $model->agent_premium = $model['policy']['agent_premium'];
            //                    $model->user_premium = $model['policy']['user_premium'];
            //                    $model->save();
            //                }
            //
            //                $progressBar2->advance();
            //            });
            //            $progressBar2->finish();
            //
            //            $progressBar3 = $this->output->createProgressBar((new PoundagePayment())->count());
            //            $this->info('PoundagePayment - START');
            //            (new PoundagePayment())->with('policy')->eachById(function ($model) use ($progressBar3) {
            //                if($model['cost_premium'] == $model['policy']['premium'] && $model['platform_premium'] == $model['policy']['platform_premium'] && $model['agent_premium'] != $model['policy']['agent_premium'] && $model['user_premium'] != $model['policy']['user_premium']){
            //                    $model->agent_premium = $model['policy']['agent_premium'];
            //                    $model->user_premium = $model['policy']['user_premium'];
            //                    $model->save();
            //                }
            //
            //                $progressBar3->advance();
            //            });
            //            $progressBar3->finish();
            //
            //            $progressBar4 = $this->output->createProgressBar((new CommissionReceivable())->count());
            //            $this->info('CommissionReceivable - START');
            //            (new CommissionReceivable())->with('policy')->eachById(function ($model) use ($progressBar4) {
            //                if($model['cost_premium'] == $model['policy']['premium'] && $model['platform_premium'] == $model['policy']['platform_premium'] && $model['agent_premium'] != $model['policy']['agent_premium'] && $model['user_premium'] != $model['policy']['user_premium']){
            //                    $model->agent_premium = $model['policy']['agent_premium'];
            //                    $model->user_premium = $model['policy']['user_premium'];
            //                    $model->save();
            //                }
            //
            //                $progressBar4->advance();
            //            });
            //            $progressBar4->finish();
            //
            //            $progressBar5 = $this->output->createProgressBar((new CommissionPayment())->count());
            //            $this->info('CommissionPayment - START');
            //            (new CommissionPayment())->with('policy')->eachById(function ($model) use ($progressBar5) {
            //                if($model['cost_premium'] == $model['policy']['premium'] && $model['platform_premium'] == $model['policy']['platform_premium'] && $model['agent_premium'] != $model['policy']['agent_premium'] && $model['user_premium'] != $model['policy']['user_premium']){
            //                    $model->agent_premium = $model['policy']['agent_premium'];
            //                    $model->user_premium = $model['policy']['user_premium'];
            //                    $model->save();
            //                }
            //
            //                $progressBar5->advance();
            //            });
            //            $progressBar5->finish();

            $progressBar6 = $this->output->createProgressBar((new PolicyFinance())->count());
            $this->info('PolicyFinance - START');
            (new PolicyFinance())->with('policy')->eachById(function ($model) use ($progressBar6) {
                if ($model['policy']['is_premium_sync'] == 0) {
                    if ($model['premium'] == $model['policy']['premium'] && $model['platform_premium'] == $model['policy']['platform_premium'] && $model['agent_premium'] == $model['policy']['agent_premium'] && $model['user_premium'] != $model['policy']['user_premium']) {
                        $model->agent_premium = $model['policy']['agent_premium'];
                        $model->user_premium = $model['policy']['user_premium'];
                        $model->save();
                    }
                    if ($model['premium'] == $model['policy']['premium'] && $model['platform_premium'] == $model['policy']['platform_premium'] && $model['agent_premium'] != $model['policy']['agent_premium'] && $model['user_premium'] != $model['policy']['user_premium']) {
                        $model->agent_premium = $model['policy']['agent_premium'];
                        $model->user_premium = $model['policy']['user_premium'];
                        $model->save();
                    }
                    if ($model['premium'] == $model['policy']['premium'] && $model['platform_premium'] != $model['policy']['platform_premium'] && $model['agent_premium'] != $model['policy']['agent_premium'] && $model['user_premium'] != $model['policy']['user_premium']) {
                        $model->agent_premium = $model['policy']['agent_premium'];
                        $model->user_premium = $model['policy']['user_premium'];
                        $model->save();
                    }
                }
                $progressBar6->advance();
            });
            $progressBar6->finish();
        });
    }
}
