<?php

namespace App\Console\Commands\Finance;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\User;
use App\Services\Finance\CreateFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class FixGroupFinanceData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-group-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复雇主财务数据';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '512M');
        DB::transaction(function () {
            $count = Policy::whereIn('type', [5])->whereIn('status', [5, 8])->count();
            $progressBar = $this->output->createProgressBar($count);
            Policy::with(['user', 'product', 'policyGroupEndorses' => function ($q) {
                $q->with('transaction:id,amount');
            }])
                ->whereIn('type', [5])
                ->whereIn('status', [5, 8])
                ->each(function ($policy) use ($progressBar) {
                    $res = $this->clearData($policy['id']);
                    if ($res) {
                        $policy->policyGroupEndorses->where('status', 1)->each(function ($endorse, $key) use ($policy) {
                            $this->info($endorse['id']);
                            $type = 4;
                            if ($key == 0) {
                                $type = 1;
                            }
                            $amount = $endorse['transaction']['amount'] ?? 0;
                            if ($endorse['total_fee'] < 0) {
                                $amount = -$amount;
                            }
                            if ($amount != 0) {
                                CreateFinances::handle($policy, $type, $amount, $amount, $amount, $amount, $endorse['id']);
                            }
                        });
                    }
                    $progressBar->advance();
                });
            $progressBar->finish();
        });
    }

    protected function clearData($policyId)
    {
        $res = true;
        $models = [PremiumReceivable::class, PremiumPayment::class, CommissionReceivable::class, CommissionPayment::class, PoundagePayment::class];
        foreach ($models as $mode) {
            $model = new $mode();

            if ($model->where('policy_id', $policyId)->whereNotNull('bill_id')->exists()) {
                $res = false;
            }

            $model->where('policy_id', $policyId)
                ->whereNull('bill_id')
                ->delete();
        }
        return $res;
    }
}
