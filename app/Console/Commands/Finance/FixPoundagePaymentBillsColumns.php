<?php

namespace App\Console\Commands\Finance;

use App\Models\Policy;
use App\Models\PoundagePaymentBill;
use App\Models\PremiumReceivable;
use App\Models\User;
use Illuminate\Console\Command;

class FixPoundagePaymentBillsColumns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-poundage-bills-columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = (new PoundagePaymentBill())->count();
        $progressBar = $this->output->createProgressBar($count);

        (new PoundagePaymentBill())->with(['companyBranch'])->eachById(function ($poundageBill) use ($progressBar) {
            $poundageBill->company_id = $poundageBill['companyBranch']['company_id'];
            $poundageBill->save();

            $progressBar->advance();
        });
        $progressBar->finish();
    }
}
