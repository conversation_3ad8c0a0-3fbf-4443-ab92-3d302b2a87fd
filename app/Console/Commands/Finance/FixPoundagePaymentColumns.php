<?php

namespace App\Console\Commands\Finance;

use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\User;
use Illuminate\Console\Command;

class FixPoundagePaymentColumns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-poundage-payment-columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = (new PoundagePayment())->count();
        $progressBar = $this->output->createProgressBar($count);

        (new PoundagePayment())->with(['user', 'policy'])->eachById(function ($poundagePayment) use ($progressBar) {
            $poundagePayment->policy_source = Policy::class;
            $poundagePayment->user_source = User::class;
            $poundagePayment->save();

            $progressBar->advance();
        });
        $progressBar->finish();

        $progressBarTwo = $this->output->createProgressBar($count);

        (new PoundagePayment())->with(['user', 'policy'])->eachById(function ($poundagePayment) use ($progressBarTwo) {
            $poundagePayment->poundage_rate = $poundagePayment->policy->service_charge;
            $poundagePayment->source_platform_id = $poundagePayment->user->platform_id;
            $poundagePayment->save();

            $progressBarTwo->advance();
        });

        $progressBarTwo->finish();
    }
}
