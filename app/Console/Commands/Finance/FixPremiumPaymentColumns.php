<?php

namespace App\Console\Commands\Finance;

use App\Models\Policy;
use App\Models\PremiumPayment;
use App\Models\User;
use Illuminate\Console\Command;

class FixPremiumPaymentColumns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-premium-payment-columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = (new PremiumPayment())->count();
        $progressBar = $this->output->createProgressBar($count);

        (new PremiumPayment())->with(['user', 'policy'])->eachById(function ($premiumPayment) use ($progressBar) {
            $premiumPayment->policy_source = Policy::class;
            $premiumPayment->user_source = User::class;
            $premiumPayment->save();

            $progressBar->advance();
        });
        $progressBar->finish();
    }
}
