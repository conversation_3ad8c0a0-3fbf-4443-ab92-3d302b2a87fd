<?php

namespace App\Console\Commands\Finance;

use App\Models\Policy;
use App\Models\PolicyOffline;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\PremiumReceivableBill;
use App\Models\User;
use App\Services\Finance\CreateFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class FixPremiumReceivableBillData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-premium-receivable-bill-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new PremiumReceivableBill())
                ->where('created_at', '>', '2023-03-17 00:00:00')
                ->where('created_at', '<', '2023-04-21 00:00:00')
                ->whereNotNull('payee_type')
                ->count();
            $progressBar = $this->output->createProgressBar($count);
            (new PremiumReceivableBill())
                ->where('created_at', '>', '2023-03-17 00:00:00')
                ->where('created_at', '<', '2023-04-21 00:00:00')
                ->whereNotNull('payee_type')
                ->with(['receivable'])
                ->eachById(function ($bill) use ($progressBar) {
                    (new PremiumReceivable())->where('bill_id', $bill['id'])->eachById(function ($premiumReceivable) use ($bill) {
                        if ($bill['payee_type'] == PremiumReceivableBill::PAYEE_TYPE_PLATFORM) {
                            $this->clearPoundagePaymentData($premiumReceivable, $bill['created_at']->format('Y-m-d'));
                            $premiumPayment = $this->preparePremiumPaymentData($premiumReceivable);

                            if (!PremiumPayment::where('type', $premiumReceivable['type'])
                                ->where('policy_id', $premiumReceivable['policy_id'])
                                ->where('platform_id', $premiumReceivable['platform_id'])
                                ->where('premium', $premiumReceivable['premium'])
                                ->where('cost_premium', $premiumReceivable['cost_premium'])
                                ->where('platform_premium', $premiumReceivable['platform_premium'])
                                ->where('agent_premium', $premiumReceivable['agent_premium'])
                                ->where('user_premium', $premiumReceivable['user_premium'])
                                ->exists()) {
                                PremiumPayment::create($premiumPayment);
                            }
                        } else {
                            $this->clearPremiumPaymentData($premiumReceivable, $bill['created_at']->format('Y-m-d'));
                            $poundagePaymentData = $this->preparePoundagePaymentData($premiumReceivable);

                            if (!PoundagePayment::where('type', $premiumReceivable['type'])
                                ->where('policy_id', $premiumReceivable['policy_id'])
                                ->where('platform_id', $premiumReceivable['platform_id'])
                                ->where('premium', $premiumReceivable['premium'])
                                ->where('cost_premium', $premiumReceivable['cost_premium'])
                                ->where('platform_premium', $premiumReceivable['platform_premium'])
                                ->where('agent_premium', $premiumReceivable['agent_premium'])
                                ->where('user_premium', $premiumReceivable['user_premium'])
                                ->exists()) {
                                PoundagePayment::create($poundagePaymentData);
                            }
                        }
                    });
                    $progressBar->advance();
                });
            $progressBar->finish();
        });
    }


    /**
     * 准备保费应付数据
     *
     * @param $premiumReceivable
     * @return array
     */
    protected function preparePremiumPaymentData($premiumReceivable)
    {
        $premiumPayment = new PremiumPayment();
        $data = Arr::only($premiumReceivable->toArray(), $premiumPayment->getFillable());
        $data['payment_platform_id'] = $data['platform_id'];

        $data = array_merge($data, [
            'order_no' => CreateFinances::getOrderNo($premiumPayment, 'PRP'),
            'premium' => $premiumReceivable['premium'],
        ]);
        unset($data['bill_id']);

        return $data;
    }

    protected function preparePoundagePaymentData($premiumReceivable)
    {
        $poundagePayment = new PoundagePayment();
        $data = Arr::only($premiumReceivable->toArray(), $poundagePayment->getFillable());

        $poundageRate = $premiumReceivable['policy']['service_charge'];
        $premium = $premiumReceivable['premium'];
        if ($premiumReceivable['policy']['type'] === Policy::TYPE_OFFLINE && $premiumReceivable['policy']['policyOffline']['settlement_type'] === PolicyOffline::SETTLEMENT_TYPE_UNTAX) {
            $premium = bcdiv($premium, 1.06, 5);
        }

        $data = array_merge($data, [
            'order_no' => CreateFinances::getOrderNo($poundagePayment, 'POP'),
            'source_platform_id' => $premiumReceivable['user']['platform_id'],
            'poundage_rate' => $poundageRate,
            'poundage' => round(bcmul($premium, bcdiv($poundageRate, 100, 5), 5), 2),
        ]);

        unset($data['bill_id']);

        return $data;
    }

    protected function clearPoundagePaymentData($premiumReceivable, $date)
    {
        $poundagePayment = new PoundagePayment();

        $poundagePayment->where('type', $premiumReceivable['type'])
            ->where('policy_id', $premiumReceivable['policy_id'])
            ->where('platform_id', $premiumReceivable['platform_id'])
            ->where('premium', $premiumReceivable['premium'])
            ->where('cost_premium', $premiumReceivable['cost_premium'])
            ->where('platform_premium', $premiumReceivable['platform_premium'])
            ->where('agent_premium', $premiumReceivable['agent_premium'])
            ->where('user_premium', $premiumReceivable['user_premium'])
            ->whereDate('created_at', $date)
            ->delete();
    }

    protected function clearPremiumPaymentData($premiumReceivable, $date)
    {
        $premiumPayment = new PremiumPayment();

        $premiumPayment->where('type', $premiumReceivable['type'])
            ->where('policy_id', $premiumReceivable['policy_id'])
            ->where('platform_id', $premiumReceivable['platform_id'])
            ->where('premium', $premiumReceivable['premium'])
            ->where('cost_premium', $premiumReceivable['cost_premium'])
            ->where('platform_premium', $premiumReceivable['platform_premium'])
            ->where('agent_premium', $premiumReceivable['agent_premium'])
            ->where('user_premium', $premiumReceivable['user_premium'])
            ->whereDate('created_at', $date)
            ->delete();
    }
}
