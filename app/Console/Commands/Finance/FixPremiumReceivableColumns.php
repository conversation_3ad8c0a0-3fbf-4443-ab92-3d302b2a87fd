<?php

namespace App\Console\Commands\Finance;

use App\Models\Policy;
use App\Models\PremiumReceivable;
use App\Models\User;
use Illuminate\Console\Command;

class FixPremiumReceivableColumns extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-premium-receivable-columns';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = (new PremiumReceivable())->count();
        $progressBar = $this->output->createProgressBar($count);

        (new PremiumReceivable())->with(['user', 'policy'])->eachById(function ($premiumReceivable) use ($progressBar) {
            $premiumReceivable->policy_source = Policy::class;
            $premiumReceivable->user_source = User::class;
            $premiumReceivable->save();

            $progressBar->advance();
        });
        $progressBar->finish();
    }
}
