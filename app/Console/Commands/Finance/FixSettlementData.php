<?php

namespace App\Console\Commands\Finance;

use App\Models\Policy;
use App\Models\PolicyOffline;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\PremiumReceivableBill;
use App\Models\Settlement;
use App\Models\User;
use App\Services\Finance\CreateFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FixSettlementData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-settlement-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '512M');
        DB::transaction(function () {
            $count = Settlement::where('operated_at', '>=', '2024-03-05 00:00:00')
                ->where('operated_at', '<=', '2024-03-13 13:20:00')
                ->where('status', Settlement::STATUS_DONE)
                ->where('type', Settlement::TYPE_PAID_OF_PLATFORM)
                ->count();
            $progressBar = $this->output->createProgressBar($count);
            Settlement::where('operated_at', '>=', '2024-03-05 00:00:00')
                ->where('operated_at', '<=', '2024-03-13 13:20:00')
                ->where('status', Settlement::STATUS_DONE)
                ->where('type', Settlement::TYPE_PAID_OF_PLATFORM)
                ->with(['receivables'])
                ->eachById(function ($settlement) use ($progressBar){
                    $settlement['receivables']->each(function ($receivable) use ($settlement){
                        if($receivable['policy']['platform_id'] === $receivable['user']['platform_id']){
                            if($settlement['type'] == PremiumReceivableBill::PAYEE_TYPE_PLATFORM){
                                $premiumPayment = $this->preparePremiumPaymentData($receivable);

                                $payment = PremiumPayment::create($premiumPayment);
                                PoundagePayment::where('policy_id', $receivable['policy_id'])->delete();
                                if($receivable['policyFinance']){
                                    $receivable->policyFinance()->update(['poundage_id' => -1]);
                                    $receivable->policyFinance()->update(['payment_id' => $payment['id']]);
                                }
                            }
                        }
                    });
                    $progressBar->advance();
                });

            $progressBar->finish();
        });
    }

    /**
     * 准备保费应付数据
     *
     * @param $premiumReceivable
     * @return array
     */
    protected function preparePremiumPaymentData($premiumReceivable)
    {
        $premiumPayment = new PremiumPayment();
        $data = Arr::only($premiumReceivable->toArray(), $premiumPayment->getFillable());
        $data['payment_platform_id'] = $data['platform_id'];

        $data = array_merge($data, [
            'order_no' => CreateFinances::getOrderNo($premiumPayment, 'PRP'),
            'premium' => $premiumReceivable['premium'],
        ]);
        unset($data['bill_id']);

        return $data;
    }

    /**
     * 准备经纪费结算数据
     *
     * @param $premiumReceivable
     * @return array
     */
    protected function preparePoundagePaymentData($premiumReceivable)
    {
        $poundagePayment = new PoundagePayment();
        $data = Arr::only($premiumReceivable->toArray(), $poundagePayment->getFillable());

        $poundageRate = $premiumReceivable['policy']['service_charge'];
        $premium = $premiumReceivable['premium'];
        if($premiumReceivable['policy']['type'] === Policy::TYPE_OFFLINE && $premiumReceivable['policy']['policyOffline']['settlement_type'] === PolicyOffline::SETTLEMENT_TYPE_UNTAX){
            $premium = bcdiv($premium, 1.06, 5);
        }

        $data = array_merge($data, [
            'order_no' => CreateFinances::getOrderNo($poundagePayment, 'POP'),
            'source_platform_id' => $premiumReceivable['user']['platform_id'],
            'poundage_rate' => $poundageRate,
            'poundage' => round(bcmul($premium, bcdiv($poundageRate, 100, 5), 5), 2),
        ]);

        unset($data['bill_id']);

        return $data;
    }

}
