<?php

namespace App\Console\Commands\Finance;

use App\Models\Admin;
use App\Models\CommissionPayment;
use App\Models\CommissionPaymentBill;
use App\Models\CommissionReceivable;
use App\Models\CommissionReceivableBill;
use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\PoundagePaymentBill;
use App\Models\PremiumPayment;
use App\Models\PremiumPaymentBill;
use App\Models\PremiumReceivable;
use App\Models\PremiumReceivableBill;
use App\Models\Settlement;
use App\Models\User;
use App\Services\Finance\HandleReceivableBills;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class FixSettlementHandleData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-settlement-handle-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '1024M');
        DB::transaction(function () {
            $progressBar = Settlement::where('operated_at', '>=', '2024-08-01 15:17:00')
                ->where('operated_at', '<=', '2024-08-05 17:35:00')
                ->where('status', Settlement::STATUS_DONE)
                ->count();
            $progressBar = $this->output->createProgressBar($progressBar);
            $this->info('START');
            (new Settlement())->where('operated_at', '>=', '2024-08-01 15:17:00')
                ->where('operated_at', '<=', '2024-08-05 17:35:00')
                ->where('status', Settlement::STATUS_DONE)
                ->each(function ($settlement) use ($progressBar) {
                    $receivableData = $this->prepareReceivableData($settlement);
                    $this->handleReceivable($receivableData, Admin::find($settlement['operated_id']));
                $progressBar->advance();
            });
            $progressBar->finish();
        });
    }

    protected function prepareReceivableData($settlement)
    {
        $receivables = $settlement['receivables'];

        return [
            'ids' => $receivables->pluck('id')->toArray(),
            'receivable' => $receivables->sum('receivable') / 100,
            'actual_receivable' => $receivables->sum('receivable') / 100,
            'company_branch_id' => $receivables[0]['company_branch_id'],
            'payee_type' => $settlement['type'],
        ];
    }

    protected function handleReceivable($attributes, $admin)
    {
        return (new HandleReceivableBills())->handle(new PremiumReceivableBill(), new PremiumReceivable(), $attributes, '', true, false, $admin);
    }
}
