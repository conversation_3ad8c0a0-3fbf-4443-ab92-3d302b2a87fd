<?php

namespace App\Console\Commands\Finance;

use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\PremiumReceivable;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class FixUserSettlementData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'finance:fix-user-settlement-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set('memory_limit', '512M');
        DB::transaction(function () {
            User::where('arrears', '>', 0)->with('policies')->each(function ($user) {
                $this->info('USER ID: ' . $user['id']);
                $count = $user['policies']->count();
                $progressBar = $this->output->createProgressBar($count);
                if ($user['is_agent'] == 1) {
                    User::where('agent_id', $user['id'])->with('policies')->each(function ($user) use ($progressBar) {
                        $this->fixData($user, $progressBar, $user['agent_id']);
                    });
                }

                $this->fixData($user, $progressBar, $user['id']);

                $progressBar->finish();
            });
        });
    }

    protected function getNo()
    {
        $sn = order_no('PRB');

        if (PremiumReceivable::where('order_no', $sn)->exists()) {
            $sn = $this->getNo($sn);
        }

        return $sn;
    }


    protected function preparePremiumReceivableData($policy, $premium, $receivablePremium, $platformId, $receivablePlatformId)
    {
        $data = Arr::only($policy->toArray(), (new PremiumReceivable())->getFillable());
        $data = array_merge($data, [
            'policy_id' => $policy['id'],
            'policy_source' => get_class($policy),
            'user_source' => get_class($policy['user']),
            'type' => 1,
            'premium' => round($premium, 2),
            'receivable' => round($receivablePremium, 2),
            'issued_at' => $policy['issued_at'],
            'platform_id' => $platformId,
            'receivable_platform_id' => $receivablePlatformId,
        ]);
        return $data;
    }

    protected function fixData($user, $progressBar, $id)
    {
        $user['policies']->each(function ($policy) use ($user, $progressBar, $id) {
            if (in_array($policy['type'], [1, 2, 3, 4]) && in_array($policy['status'], [5, 8])) {
                $policy->is_virtual = 1;
                $policy->save();
                $premiumReceivableData = $this->preparePremiumReceivableData($policy, $policy['agent_premium'], $policy['agent_premium'], $policy['platform_id'], $policy['platform_id']);
                $premiumReceivableData['user_id'] = $id;
                if ($policy['platform_id'] == $user['platform_id']) {
                    $premiumReceivableData['order_no'] = $this->getNo();
                    PremiumReceivable::updateOrCreate([
                        'platform_id' => $policy['platform_id'],
                        'receivable_platform_id' => $policy['platform_id'],
                        'type' => 1,
                        'policy_id' => $policy['id'],
                        'user_id' => $id,
                    ], $premiumReceivableData);
                    //                        }elseif(PremiumReceivable::where(['type' => 1, 'policy_id' => $policy['id'], 'user_id' => $id, 'receivable_platform_id' => -1])->exists()){
                } elseif (PremiumReceivable::where(['type' => 1, 'policy_id' => $policy['id'], 'user_id' => $id, 'receivable_platform_id' => -1])->exists()) {
                    $premiumReceivableData['platform_id'] = $premiumReceivableData['receivable_platform_id'] = $user['platform_id'];
                    $premiumReceivableData['order_no'] = $this->getNo();
                    PremiumReceivable::updateOrCreate([
                        'platform_id' => $user['platform_id'],
                        'receivable_platform_id' => $user['platform_id'],
                        'type' => 1,
                        'policy_id' => $policy['id'],
                        'user_id' => $id,
                    ], $premiumReceivableData);
                } elseif (PremiumReceivable::where(['policy_id' => $policy['id'], 'user_id' => $id])->doesntExist()) {
                    $premiumReceivableData['order_no'] = $this->getNo();
                    $premiumReceivableData['receivable_platform_id'] = -1;
                    PremiumReceivable::updateOrCreate([
                        'platform_id' => $policy['platform_id'],
                        'receivable_platform_id' => -1,
                        'type' => 1,
                        'policy_id' => $policy['id'],
                        'user_id' => $id,
                    ], $premiumReceivableData);

                    $premiumReceivableData['order_no'] = $this->getNo();
                    $premiumReceivableData['platform_id'] = $premiumReceivableData['receivable_platform_id'] = $user['platform_id'];
                    PremiumReceivable::updateOrCreate([
                        'platform_id' => $user['platform_id'],
                        'receivable_platform_id' => $user['platform_id'],
                        'type' => 1,
                        'policy_id' => $policy['id'],
                        'user_id' => $id,
                    ], $premiumReceivableData);
                }

                $progressBar->advance();
            }
        });
    }
}
