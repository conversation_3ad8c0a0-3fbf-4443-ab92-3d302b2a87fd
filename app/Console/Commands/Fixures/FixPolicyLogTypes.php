<?php

namespace App\Console\Commands\Fixures;

use App\Models\PolicyLog;
use Illuminate\Console\Command;

class FixPolicyLogTypes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixures:policy-log-type';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '补充保单日志类型';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        PolicyLog::chunkById(
            100,
            fn ($policyLogs) =>
            $policyLogs->each(
                fn ($log) =>
                $log->update(
                    ['type' => $this->guessType($log['content'])]
                )
            )
        );
    }

    /**
     * Guess the type of the policy log.
     *
     * @param   string  $message
     *
     * @return  int
     */
    protected function guessType(string $message): int
    {
        if (str_contains($message, '领取保单')) {
            return PolicyLog::TYPE_RECEIVE;
        }

        if (str_contains($message, '审核保单')) {
            return PolicyLog::TYPE_AUDIT;
        }

        if (str_contains($message, '退回保单')) {
            return PolicyLog::TYPE_SENDBACK;
        }

        if (str_contains($message, '补充资料')) {
            return PolicyLog::TYPE_SENDBACK_MISS_INFO;
        }

        if (str_contains($message, '重提')) {
            return PolicyLog::TYPE_RESUBMIT;
        }

        return -1;
    }
}
