<?php

namespace App\Console\Commands\Fixures;

use App\Models\Policy;
use Illuminate\Console\Command;

class FixPolicyPlatformCommission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixures:policy-platform-commission';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '修复保单平台佣金';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        $count = (new Policy())->count();
        $progressBar = $this->output->createProgressBar($count);

        (new Policy())->with('user')->eachById(function ($policy) use ($progressBar) {
            if ($policy['user']['platform_id'] !== $policy['platform_id']) {
                $policy->platform_commission = round($policy['platform_premium'] * ($policy['platform_commission_rate'] / 100), 0);
            } else {
                $policy->platform_commission = round($policy['platform_premium'] * ($policy['service_charge'] / 100), 0);
            }

            $policy->save();

            $progressBar->advance();
        });

        $progressBar->finish();
    }
}
