<?php

namespace App\Console\Commands\Fixures;

use App\Models\Policy;
use Illuminate\Console\Command;

class FixPolicyServiceCharge extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixures:policy-service-charge';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '填充保单服务费';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        $count = (new Policy())->count();
        $progressBar = $this->output->createProgressBar($count);

        (new Policy())->with('product.additional')->eachById(function ($policy) use ($progressBar) {
            $policy->service_charge = $policy->product->additional->service_charge;
            $policy->save();

            $progressBar->advance();
        });

        $progressBar->finish();
    }
}
