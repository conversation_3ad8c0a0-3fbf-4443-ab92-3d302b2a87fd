<?php

namespace App\Console\Commands\Fixures;

use App\Models\Currency;
use App\Models\Policy;
use App\Models\User;
use App\Models\UserProduct;
use App\Services\Finance\CreateFinances;
use App\Services\Policy\Concerns\InteractsWithInsurance;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixRecalculatePremium extends Command
{
    use InteractsWithInsurance;

    /**
     * 平台
     *
     * @var string
     */
    const TO_PLATFORM = '平台';

    /**
     * 代理
     *
     * @var string
     */
    const TO_AGENT = '代理';

    /**
     * 用户
     *
     * @var string
     */
    const TO_USER = '用户';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixures:fix-reclaculate-premium';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '重新计算保费并扣费';

    /**
     * These products will be reverted.
     *
     * @var array
     */
    protected array $productCodes = [
        'P2008040276' => 2941, // 人保广东危险
        'P2008040274' => 2940, // 人保广东易碎
        'P2008040273' => 2939, // 人保广东冷藏
        'P2008040197' => 2938, // 人保广东普货
        'P2008040705' => 2911, // 人保广东人工审核
    ];

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        $output = fopen(storage_path('logs/premium.csv'), 'a+');
        fputcsv($output, [
            '用户ID',
            '用户名称',
            '产品ID',
            '产品代码',
            '产品名称',
            '流水号',
            '保单号',
            '用户费率',
            '新用户费率',
            '原用户保费',
            '新用户保费',
            '用户最低保费',
            '新用户最低保费',
            '代理费率',
            '新代理费率',
            '原代理保费',
            '新代理保费',
            '代理最低保费',
            '新代理最低保费',
            '平台费率',
            '新平台费率',
            '原平台保费',
            '新平台保费',
            '平台最低保费',
            '新平台最低保费',
        ]);
        $updatedCount = 0;
        Policy::whereIn('product_id', function ($q) {
            $q->select('id')
                ->from('products')
                ->whereIn('code', array_keys($this->productCodes))
                ->where('is_enabled', true)
                ->whereNull('deleted_at');
        })
            ->whereIn('status', [
                Policy::STATUS_SUBMITTED,
                Policy::STATUS_IN_REVIEW,
                Policy::STATUS_ISSUED,
                Policy::STATUS_REVISION,
                Policy::STATUS_CANCELLING,
                Policy::STATUS_SEND_BACK_SUPPLEMENT_INFO,
                Policy::STATUS_SEND_BACK_SUPPLEMENT_INFO_TICKET,
            ])
            ->whereHas('policyCargo', fn($q) => $q->where('subject_id', '<>', 3))
            ->get()
            ->each(function ($policy) use (&$updatedCount, &$output) {
                $userProduct = $this->getUserProduct($policy['user_id'], $policy['product_id']);
                $agentProduct = $userProduct;
                if ($policy['user']->isAgentUser()) {
                    $agentProduct = $this->getUserProduct($policy['user']['agent_id'], $policy['product_id']);
                }
                $userRate = $this->getProductRate($userProduct);
                $agentRate = $agentProduct ? $this->getProductRate($agentProduct) : $userRate;
                $coverage = $this->realCoverage($policy);
                $userPremium = $this->calcPremium($coverage, $userRate, $userProduct['minimum_premium']);
                $agentPremium = $this->calcPremium($coverage, $agentRate, $agentProduct['minimum_premium']);
                $platformPremium = $this->calcPremium($coverage, $userProduct['platform_rate'], $userProduct['platform_minimum_premium']);

                $attributes = [
                    'user_rate' => $userRate,
                    'user_minimum_premium' => $userProduct['minimum_premium'],
                    'user_premium' => $userPremium,

                    'agent_rate' => $agentRate,
                    'agent_minimum_premium' => $agentProduct['minimum_premium'],
                    'agent_premium' => $agentPremium,

                    'platform_rate' => $userProduct['platform_rate'],
                    'platform_minimum_premium' => $userProduct['platform_minimum_premium'],
                    'platform_premium' => $platformPremium,

                    'rate' => $userProduct['rate'],
                    'minimum_premium' => $policy['product']['additional']['minimum_premium'],
                    'premium' => $this->calcPremium($coverage, $policy['product']['additional']['rate'], $policy['product']['additional']['minimum_premium']),
                ];

                if ($userProduct['is_premium_sync']) {
                    $attributes['premium'] = $attributes['platform_premium'] = $attributes['agent_premium'] = $attributes['user_premium'];
                    $attributes['rate'] = $attributes['platform_rate'] = $attributes['agent_rate'] = $attributes['user_rate'];
                }

                $attributes = array_merge($attributes, [
                    'agent_commission_rate' => $agentProduct['agent_commission_rate'],
                    'agent_commission' => $attributes['agent_premium'] * ($agentProduct['agent_commission_rate'] / 100),
                    'platform_commission_rate' => $userProduct['platform_commission_rate'],
                    'platform_commission' => $policy['product']['platform_id'] !== $policy['user']['platform_id'] ?
                        $attributes['platform_premium'] * ($userProduct['platform_commission_rate'] / 100) :
                        $attributes['platform_premium'] * ($policy['service_charge'] / 100)
                ]);

                $this->output->writeln("保单{$policy['order_no']}重新计算保费");
                $this->output->note(
                    sprintf(
                        "保额: %s, 用户保费: %s (%s) [%s] -> %s (%s) [%s]  \n 原代理保费: %s (%s) [%s] In: %s -> %s (%s) [%s] In: %s \n 原平台保费: %s (%s) [%s] In: %s -> %s (%s) [%s] In: %s",
                        $coverage,
                        $policy['user_premium'],
                        $policy['user_rate'],
                        $policy['user_minimum_premium'],
                        $attributes['user_premium'],
                        $attributes['user_rate'],
                        $attributes['user_minimum_premium'],
                        $policy['agent_premium'],
                        $policy['agent_rate'],
                        $policy['agent_minimum_premium'],
                        $policy['agent_commission'],
                        $attributes['agent_premium'],
                        $attributes['agent_rate'],
                        $attributes['agent_minimum_premium'],
                        $attributes['agent_commission'],
                        $policy['platform_premium'],
                        $policy['platform_rate'],
                        $policy['platform_minimum_premium'],
                        $policy['platform_commission'],
                        $attributes['platform_premium'],
                        $attributes['platform_rate'],
                        $attributes['platform_minimum_premium'],
                        $attributes['platform_commission']
                    )
                );
                if (
                    (int) $policy['user_premium'] !== (int) $attributes['user_premium']
                    || (int) $policy['agent_premium'] !== (int) $attributes['agent_premium']
                    || (int) $policy['platform_premium'] !== (int) $attributes['platform_premium']
                ) {

                    fputcsv($output, [
                        $policy['user_id'],
                        $policy['user']['name'],
                        $policy['product_id'],
                        $policy['product']['code'],
                        $policy['product']['name'],
                        $policy['order_no'],
                        $policy['policy_no'],
                        $policy['user_rate'],
                        $attributes['user_rate'],
                        $policy['user_premium'],
                        $attributes['user_premium'],
                        $policy['user_minimum_premium'],
                        $attributes['user_minimum_premium'],
                        $policy['agent_rate'],
                        $attributes['agent_rate'],
                        $policy['agent_premium'],
                        $attributes['agent_premium'],
                        $policy['agent_minimum_premium'],
                        $attributes['agent_minimum_premium'],
                        $policy['platform_rate'],
                        $attributes['platform_rate'],
                        $policy['platform_premium'],
                        $attributes['platform_premium'],
                        $policy['platform_minimum_premium'],
                        $attributes['platform_minimum_premium'],
                    ]);
                    $updatedCount++;
                    $this->output->error("保单{$policy['order_no']}重新计算保费", $attributes);
                }

                DB::transaction(function () use ($policy, $attributes) {
                    $this->payDiff($policy, $attributes);
                    $this->refundDiff($policy, $attributes);

                    $policy->fill($attributes)->save();
                });
            });

        $this->output->success("共重新计算保费 {$updatedCount} 个保单");
        fclose($output);
    }

    /**
     * 获取真实保额.
     *
     * @param   array  $attributes
     *
     * @return  int
     */
    protected function realCoverage(Policy $policy)
    {
        if ($policy['type'] == Policy::TYPE_INTL) {
            $currencyRate = Currency::find($policy['policyCargo']['coverage_currency_id']);

            return $policy['coverage'] * $currencyRate['rate'];
        }

        return $policy['coverage'];
    }

    /**
     * 获取产品费率.
     *
     * @param   UserProduct $userProduct
     *
     * @return  float
     */
    protected function getProductRate($userProduct)
    {
        return floatval($userProduct['override_user_rate']) > 0.00 ? $userProduct['override_user_rate'] : $userProduct['user_rate'];
    }

    /**
     * 获取保费信息.
     *
     * @param   int  $coverage
     * @param   float  $rate
     * @param   int  $minimum
     *
     * @return  int
     */
    protected function calcPremium(int $coverage, float $rate, int $minimum = 0)
    {
        $premium = bcmul($coverage, bcdiv($rate, 10000, 6), 5);

        return (int) round($premium < $minimum ? $minimum : $premium, 0);
    }

    /**
     * 获取用户费率.
     *
     * @param   int     $userId
     * @param   int     $productId
     *
     * @return  \Illuminate\Support\Collection
     */
    protected function getUserProduct(int $userId, int $productId)
    {
        return (new UserProduct())->getProduct($userId, $productId);
    }

    /**
     * 部分退款.
     *
     * @param   Policy  $policy
     * @param   array   $attributes
     *
     * @return  void
     */
    protected function refundDiff(Policy $policy, array $attributes)
    {
        $userPremiumDiff = 0;
        if ($attributes['user_premium'] < $policy['user_premium']) {
            $userPremiumDiff = $policy['user_premium'] - $attributes['user_premium'];
            $this->refund($policy, $policy['user'], $policy['user_premium'] - $attributes['user_premium']);
        }

        $agentPremiumDiff = 0;
        if ($attributes['agent_premium'] < $policy['agent_premium']) {
            $agentPremiumDiff = $policy['agent_premium'] - $attributes['agent_premium'];
            if ($policy['user']->isAgentUser()) {
                $this->refund($policy, $policy['user']['agent'], $policy['agent_premium'] - $attributes['agent_premium']);
            }
        }

        $platformPremiumDiff = 0;
        if ($attributes['platform_premium'] < $policy['platform_premium']) {
            $platformPremiumDiff = $policy['platform_premium'] - $attributes['platform_premium'];
            if ($this->isOtherPlatformProduct($policy)) {
                $this->refund($policy, $policy['user']['platform'], $policy['platform_premium'] - $attributes['platform_premium']);
            }
        }

        $premiumDiff = 0;
        if ($attributes['premium'] < $policy['premium']) {
            $premiumDiff = $policy['premium'] - $attributes['premium'];
        }

        $this->createFinances($policy, -$premiumDiff, -$platformPremiumDiff, -$agentPremiumDiff, -$userPremiumDiff, CreateFinances::TYPE_MODIFY);
    }

    /**
     * 支付差额.
     *
     * @param   Policy  $policy
     * @param   array   $attributes
     *
     * @return  void
     */
    protected function payDiff(Policy $policy, array $attributes)
    {
        $userPremiumDiff = 0;
        if ($attributes['user_premium'] > $policy['user_premium']) {
            $userPremiumDiff = $attributes['user_premium'] - $policy['user_premium'];
            $this->payDifference($policy, $policy['user'], $attributes['user_premium'] - $policy['user_premium'], self::TO_USER);
        }

        $agentPremiumDiff = 0;
        if ($attributes['agent_premium'] > $policy['agent_premium']) {
            $agentPremiumDiff = $attributes['agent_premium'] - $policy['agent_premium'];
            if ($policy['user']->isAgentUser()) {
                $this->payDifference($policy, $policy['user']['agent'], $attributes['agent_premium'] - $policy['agent_premium'], self::TO_AGENT);
            }
        }

        $platformPremiumDiff = 0;
        if ($attributes['platform_premium'] > $policy['platform_premium']) {
            $platformPremiumDiff = $attributes['platform_premium'] - $policy['platform_premium'];
            if ($this->isOtherPlatformProduct($policy)) {
                $this->payDifference($policy, $policy['user']['platform'], $attributes['platform_premium'] - $policy['platform_premium'], self::TO_PLATFORM);
            }
        }

        $premiumDiff = 0;
        if ($attributes['premium'] > $policy['premium']) {
            $premiumDiff = $attributes['premium'] - $policy['premium'];
        }

        $this->createFinances($policy, $premiumDiff, $platformPremiumDiff, $agentPremiumDiff, $userPremiumDiff, CreateFinances::TYPE_MODIFY);
    }

    /**
     * 支付差额.
     *
     * @param   Policy        $policy
     * @param   User|Platform $customer
     * @param   int           $amount
     * @param   string        $to
     *
     * @return  void
     */
    protected function payDifference(Policy $policy, $customer, int $amount, string $to)
    {
        if (!$customer->isPayable($amount)) {
            abort(400, $to . '余额不足');
        }

        $customer->pay($amount, $policy, "保单{$policy['order_no']}支付差额");
    }

    /**
     * 保单退款.
     *
     * @param   Policy         $policy
     * @param   User|Platform  $customer
     * @param   int            $amount
     *
     * @return  void
     */
    protected function refund(Policy $policy, $customer, int $amount)
    {
        $customer->refund($amount, $policy, "保单{$policy['order_no']}差额退款");
    }


    /**
     * 创建财务记录.
     *
     * @param   Policy  $policy
     * @param   int     $premium
     * @param   int     $platformPremium
     * @param   int     $agentPremium
     * @param   int     $userPremium
     * @param   int     $type
     *
     * @return  void
     */
    protected function createFinances(Policy $policy, int $premium, int $platformPremium, int $agentPremium, int $userPremium, $type = CreateFinances::TYPE_INSURE)
    {
        CreateFinances::handle($policy, $type, $premium, $platformPremium, $agentPremium, $userPremium);
    }
}
