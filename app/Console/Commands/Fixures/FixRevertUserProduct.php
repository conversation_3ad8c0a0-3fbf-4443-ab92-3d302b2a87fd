<?php

namespace App\Console\Commands\Fixures;

use App\Models\UserProduct;
use App\Services\Policy\Concerns\InteractsWithInsurance;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Collection;

class FixRevertUserProduct extends Command
{
    use InteractsWithInsurance;

    /**
     * 平台
     *
     * @var string
     */
    const TO_PLATFORM = '平台';

    /**
     * 代理
     *
     * @var string
     */
    const TO_AGENT = '代理';

    /**
     * 用户
     *
     * @var string
     */
    const TO_USER = '用户';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixures:fix-revert-user-product';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '还原用户费率';

    /**
     * These products will be reverted.
     *
     * @var array
     */
    protected array $productCodes = [
        'P2008040276' => 2941, // 人保广东危险
        'P2008040274' => 2940, // 人保广东易碎
        'P2008040273' => 2939, // 人保广东冷藏
        'P2008040197' => 2938, // 人保广东普货
        'P2008040705' => 2911, // 人保广东人工审核
    ];

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        $userRateNeedsToChangeProductCount = 0;
        $output = fopen(storage_path('logs/user_products.csv'), 'a+');
        fputcsv($output, [
            '用户ID',
            '用户名称',
            '产品代码',
            '产品名称',
            '产品ID',
            '用户费率',
            '还原用户费率',
        ]);
        $currentUserProducts = $this->getCurrentUserProducts();
        foreach ($currentUserProducts as $currentUserProduct) {
            $deletedUserProduct = $this->getDeletedUserProduct($currentUserProduct['user']['id'], $this->productCodes[$currentUserProduct['product']['code']]);

            if (!$deletedUserProduct) {
                $this->output->warning("用户 [{$currentUserProduct['user']['id']}]{$currentUserProduct['user']['name']} [{$currentUserProduct['product']['code']}]{$currentUserProduct['product']['name']} 未找到被删除的产品");
                continue;
            }

            if (
                $deletedUserProduct['user_rate'] != $currentUserProduct['user_rate']
                && $deletedUserProduct['user_rate'] >= $currentUserProduct['platform_rate']
            ) {
                $userRateNeedsToChangeProductCount++;
                $this->output->warning("用户 {$currentUserProduct['user']['name']} {$currentUserProduct['product']['name']} 用户费率从 {$currentUserProduct['user_rate']}% 还原为 {$deletedUserProduct['user_rate']}%");

                fputcsv($output, [
                    $currentUserProduct['user']['id'],
                    $currentUserProduct['user']['name'],
                    $currentUserProduct['product']['code'],
                    $currentUserProduct['product']['name'],
                    $currentUserProduct['id'],
                    $currentUserProduct['user_rate'],
                    $deletedUserProduct['user_rate'],
                ]);

                $currentUserProduct->fill(['user_rate' => $deletedUserProduct['user_rate']])->save();
            }
        }
        fclose($output);

        $this->output->success("用户费率还原完成，共还原用户费率 {$userRateNeedsToChangeProductCount} 个产品");
    }

    /**
     * Get deleted user's product.
     *
     * @param   int  $userId
     * @param   int  $productId
     *
     * @return  UserProduct|null
     */
    protected function getDeletedUserProduct(int $userId, int $productId): UserProduct|null
    {
        return UserProduct::where('product_id', $productId)
            ->where('user_id', $userId)
            ->where('is_enabled', UserProduct::STATUS_ENABLE)
            ->with([
                'product' => fn ($q) => $q->select(['id', 'code', 'name'])->withTrashed(),
                'user' => fn ($q) => $q->select(['id', 'username', 'name'])->withTrashed(),
            ])
            ->withTrashed()
            ->first();
    }

    /**
     * Get current user's products.
     *
     * @return  Collection
     */
    protected function getCurrentUserProducts(): Collection
    {
        return UserProduct::whereIn('product_id', function ($q) {
            $q->select('id')
                ->from('products')
                ->whereIn('code', array_keys($this->productCodes))
                ->where('is_enabled', true);
        })
            ->with([
                'product' => fn ($q) => $q->select(['id', 'code', 'name'])->withTrashed(),
                'user' => fn ($q) => $q->select(['id', 'username', 'name'])->withTrashed(),
            ])
            ->where('is_enabled', UserProduct::STATUS_ENABLE)
            ->get();
    }
}
