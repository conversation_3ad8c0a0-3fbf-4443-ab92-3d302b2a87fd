<?php

namespace App\Console\Commands\Fixures;

use App\Models\Currency;
use App\Models\Policy;
use App\Models\Ticket;
use App\Services\Finance\CreateFinances;
use Arr;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixTicketData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixures:ticket-data {--ids= : ID}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '修改工单数据';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        DB::transaction(function () {
            $ids = $this->option('ids');
            $ids = explode(',', $ids);
            if (!empty($ids)) {
                $count = Ticket::whereIn('id', $ids)->count();
                $progressBar = $this->output->createProgressBar($count);
                Ticket::whereIn('id', $ids)->each(function ($ticket) use ($progressBar) {
                    $premiumData = $this->getPremiumData($ticket['policy'], $ticket['revision']);
                    if ($ticket['status'] === Ticket::STATUS_DONE && $ticket['policy']['payment_method'] === Policy::PAID_TYPE_BALANCE) {
                        if ($this->isNeedPayment($premiumData)) {
                            // 余额扣款
                            if ($ticket['status'] === Ticket::STATUS_DONE) {
                                $this->payDiff($premiumData['to'], $ticket);
                                $this->agentUserPayDiff($premiumData['to'], $ticket);
                                $this->platformPayDiff($premiumData['to'], $ticket);
                                CreateFinances::handle($ticket['policy'], CreateFinances::TYPE_MODIFY, $premiumData['diff']['premium'], $premiumData['diff']['platform_premium'], $premiumData['diff']['agent_premium'], $premiumData['diff']['user_premium']);
                            }
                        }

                        if ($this->isNeedRefund($premiumData)) {
                            // 退款
                            if ($ticket['status'] === Ticket::STATUS_DONE) {
                                $this->refundDiff($premiumData['to'], $ticket);
                                CreateFinances::handle($ticket['policy'], CreateFinances::TYPE_MODIFY, $premiumData['diff']['premium'], $premiumData['diff']['platform_premium'], $premiumData['diff']['agent_premium'], $premiumData['diff']['user_premium']);
                            }
                        }
                    }

                    $ticket->premium_data = $premiumData;
                    $ticket->save();

                    $progressBar->advance();
                    $this->info('TICKET ID: ' . $ticket['id']);

                });

                $progressBar->finish();
            }

        });
    }

    protected function getPremiumData($policy, $revision)
    {
        $premiumData = [];
        $attributes = [
            'type' => $policy['type']
        ];
        // 仅修改保额
        if (Arr::exists($revision, 'coverage') && !Arr::exists($revision, 'coverage_currency_id')) {
            $attributes['coverage'] = $revision['coverage']['to'];
            $attributes['coverage_currency_id'] = $policy['policyCargo']['coverage_currency_id'];
            $premiumData = (new TicketPremium())->handle($policy, $attributes);
        }

        // 修改保额币种
        if (Arr::exists($revision, 'coverage_currency_id')) {
            $attributes['coverage_currency_id'] = $revision['coverage_currency_id']['to'];
            $attributes['coverage'] = $revision['coverage']['to'] ?? $policy['coverage'];
            $premiumData = (new TicketPremium())->handle($policy, $attributes);
        }

        $data = [];
        if (!empty($premiumData)) {
            // 保费数据存在时判断是否和原保费有差异
            $diff = array_diff_assoc(Arr::except($premiumData, ['platform_commission', 'agent_commission']), $policy->toArray());
            if (!empty($diff)) {
                $data = [
                    'from' => [
                        'premium' => $policy['premium'],
                        'platform_premium' => $policy['platform_premium'],
                        'agent_premium' => $policy['agent_premium'],
                        'user_premium' => $policy['user_premium'],
                        'platform_commission' => $policy['platform_commission'],
                        'agent_commission' => $policy['agent_commission'],
                    ],
                    'to' => $premiumData
                ];
                $data['diff'] = $this->getDiffPremiumData($data);
            }
        }
        return $data;
    }

    protected function getDiffPremiumData($premiumData)
    {
        return [
            'premium' => round(bcsub($premiumData['to']['premium'], $premiumData['from']['premium'], 2), 2),
            'platform_premium' => round(bcsub($premiumData['to']['platform_premium'], $premiumData['from']['platform_premium'], 2), 2),
            'agent_premium' => round(bcsub($premiumData['to']['agent_premium'], $premiumData['from']['agent_premium'], 2), 2),
            'user_premium' => round(bcsub($premiumData['to']['user_premium'], $premiumData['from']['user_premium'], 2), 2),
            'platform_commission' => round(bcsub($premiumData['to']['platform_commission'], $premiumData['from']['platform_commission'], 2), 2),
            'agent_commission' => round(bcsub($premiumData['to']['agent_commission'], $premiumData['from']['agent_commission'], 2), 2)
        ];
    }

    /**
     * 是否需要扣费
     *
     * @param mixed $revision
     * @return bool
     */
    protected function isNeedPayment($premiumData)
    {
        if (empty($premiumData)) {
            return false;
        }
        if (
            $premiumData['to']['premium'] > $premiumData['from']['premium'] ||
            $premiumData['to']['platform_premium'] > $premiumData['from']['platform_premium'] ||
            $premiumData['to']['agent_premium'] > $premiumData['from']['agent_premium'] ||
            $premiumData['to']['user_premium'] > $premiumData['from']['user_premium']

        ) {
            return true;
        }

        return false;
    }

    /**
     * 是否需要退款
     *
     * @param mixed $revision
     * @return bool
     */
    protected function isNeedRefund($premiumData)
    {
        if (empty($premiumData)) {
            return false;
        }
        if (
            $premiumData['to']['premium'] < $premiumData['from']['premium'] ||
            $premiumData['to']['platform_premium'] < $premiumData['from']['platform_premium'] ||
            $premiumData['to']['agent_premium'] < $premiumData['from']['agent_premium'] ||
            $premiumData['to']['user_premium'] < $premiumData['from']['user_premium']

        ) {
            return true;
        }

        return false;
    }

    /**
     * 是否是其他平台产品.
     *
     * @param   Policy  $policy
     *
     * @return  bool
     */
    protected function isOtherPlatformProduct(Policy $policy)
    {
        return $policy['user']['platform_id'] !== $policy['product']['platform_id'];
    }

    /**
     * 支付差额
     *
     * @param mixed $attributes
     * @param mixed $ticket
     * @return void
     */
    protected function payDiff($attributes, $ticket)
    {
        if ($attributes['user_premium'] > $ticket['policy']['user_premium']) {
            $this->payDifference($ticket['policy']['user'], $ticket, $attributes['user_premium'] - $ticket['policy']['user_premium'], '用户');
        }
    }

    /**
     * 代理用户支付差额
     *
     * @param mixed $attributes
     * @param mixed $ticket
     * @return void
     */
    protected function agentUserPayDiff($attributes, $ticket)
    {
        if ($attributes['agent_premium'] > $ticket['policy']['agent_premium'] && $ticket['policy']['user']->isAgentUser()) {
            $this->payDifference($ticket['policy']['user']['agent'], $ticket, $attributes['agent_premium'] - $ticket['policy']['agent_premium'], '代理');
        }
    }

    /**
     * 平台支付差额
     *
     * @param mixed $attributes
     * @param mixed $ticket
     * @return void
     */
    protected function platformPayDiff($attributes, $ticket)
    {
        if ($attributes['platform_premium'] > $ticket['policy']['platform_premium'] && $this->isOtherPlatformProduct($ticket['policy'])) {
            $this->payDifference($ticket['policy']['user']['platform'], $ticket['policy'], $attributes['platform_premium'] - $ticket['policy']['platform_premium'], '平台');
        }
    }

    /**
     * 支付差额.
     *
     * @param $customer
     * @param $ticket
     * @param int $amount
     * @param string $to
     */
    protected function payDifference($customer, $ticket, int $amount, string $to)
    {
        if (!$customer->isPayable($amount)) {
            abort(400, $to . '余额不足');
        }

        $customer->pay($amount, $ticket, "工单支付差额");
    }

    /**
     * 部分退款
     *
     * @param array $attributes
     * @param $ticket
     */
    protected function refundDiff(array $attributes, $ticket)
    {
        if ($attributes['user_premium'] < $ticket['policy']['user_premium']) {
            $this->refund($ticket['policy']['user'], $ticket, $ticket['policy']['user_premium'] - $attributes['user_premium']);
        }

        if ($attributes['agent_premium'] < $ticket['policy']['agent_premium'] && $ticket['policy']['user']->isAgentUser()) {
            $this->refund($ticket['policy']['user']['agent'], $ticket, $ticket['policy']['agent_premium'] - $attributes['agent_premium']);
        }

        if ($attributes['platform_premium'] < $ticket['policy']['platform_premium'] && $this->isOtherPlatformProduct($ticket['policy'])) {
            $this->refund($ticket['policy']['user']['platform'], $ticket['policy'], $ticket['policy']['platform_premium'] - $attributes['platform_premium']);
        }
    }

    /**
     * 工单退款
     *
     * @param $customer
     * @param $ticket
     * @param int $amount
     */
    protected function refund($customer, $ticket, int $amount)
    {
        $customer->refund($amount, $ticket, "工单差额退款");
    }
}


class TicketPremium
{

    /**
     * 计算保费、佣金。
     *
     * @param   Policy   $policy
     * @param   array    $attributes
     *
     * @return  array
     */
    public function handle($policy, array $attributes)
    {
        $coverage = $this->realCoverage($attributes);

        $attributes = [
            'premium' => $this->calcPremium($coverage, $policy['rate'], $policy['minimum_premium']),
            'platform_premium' => $this->calcPremium($coverage, $policy['platform_rate'], $policy['platform_minimum_premium']),
            'agent_premium' => $this->calcPremium($coverage, $policy['agent_rate'], $policy['agent_minimum_premium']),
            'user_premium' => $this->calcPremium($coverage, $policy['user_rate'], $policy['user_minimum_premium']),
        ];

        if ($policy['is_premium_sync']) {
            $attributes['premium'] = $attributes['platform_premium'] = $attributes['agent_premium'] = $attributes['user_premium'];
        }

        $attributes = array_merge($attributes, [
            'platform_commission' => $policy['product']['platform_id'] !== $policy['user']['platform_id'] ?
                round(bcmul($attributes['platform_premium'], bcdiv($policy['platform_commission_rate'], 100, 5), 5)) :
                round(bcmul($attributes['platform_premium'], bcdiv($policy['service_charge'], 100, 5), 5)),

            'agent_commission' => round(bcmul($attributes['agent_premium'], bcdiv($policy['agent_commission_rate'], 100, 5), 5)),
        ]);

        return $attributes;
    }

    /**
     * 获取保费信息.
     *
     * @param   int  $coverage
     * @param   float  $rate
     * @param   int  $minimum
     *
     * @return  int
     */
    protected function calcPremium(int $coverage, float $rate, int $minimum = 0)
    {
        $premium = bcmul($coverage, bcdiv($rate, 10000, 6), 5);

        return (int) round($premium < $minimum ? $minimum : $premium, 0);
    }


    /**
     * 获取真实保额.
     *
     * @param   array  $attributes
     *
     * @return  int
     */
    protected function realCoverage($attributes)
    {
        if (in_array((int) $attributes['type'], [Policy::TYPE_INTL, Policy::TYPE_CBEC])) {
            $currencyRate = Currency::find($attributes['coverage_currency_id']);

            return $attributes['coverage'] * $currencyRate['rate'];
        }

        return $attributes['coverage'];
    }
}
