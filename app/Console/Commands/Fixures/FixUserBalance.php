<?php

namespace App\Console\Commands\Fixures;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class FixUserBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fixures:user-balance {file : 导入文件路径}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '修改导入文件用户余额';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        ini_set('memory_limit', -1);

        if (!file_exists($this->argument('file'))) {
            return $this->error('请传入正确的文件路径');
        }

        HeadingRowFormatter::default('none');
        Excel::import(new class ($this) extends FixUserBalance implements ToArray, WithHeadingRow {
            /**
             * Reference to the command.
             *
             * @var  \App\Console\Import\ImportUser
             */
            protected $that;

            /**
             * Create ImportUser instance.
             *
             * @param   FixUserBalance  $that
             *
             * @return  void
             */
            public function __construct(FixUserBalance $that)
            {
                $this->that = $that;
            }

            /**
             * Specify the heading row.
             *
             * @return  int
             */
            public function headingRow(): int
            {
                return 1;
            }

            /**
             * Import data to array.
             *
             * @param   array  $array
             *
             * @return  void
             */
            public function array(array $array)
            {
                DB::transaction(function () use ($array) {
                    $progressBar = $this->that->output->createProgressBar();

                    $progressBar->start(count($array));

                    foreach ($array as $key => $row) {
                        $user = User::where('id', $row['id'])->first();

                        if (!$user) {
                            $this->that->info("{$row['name']} - 该用户查询错误 ");
                            continue;
                        }

                        if((int) $row['amount'] > 0){
                            $user->pay($row['amount'] * 100, null, '扣费错误扣款-' . $row['policy_nos']);
                        } else {
                            $user->refund(abs($row['amount']) * 100, null, '扣费错误退费-' . $row['policy_nos']);
                        }


                    }

                    $progressBar->finish();
                });
            }
        }, $this->argument('file'));
    }
}
