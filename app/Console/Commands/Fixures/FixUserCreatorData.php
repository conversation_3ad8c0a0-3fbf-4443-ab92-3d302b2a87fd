<?php

namespace App\Console\Commands\Fixures;

use App\Models\Admin;
use App\Models\Policy;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixUserCreatorData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:fix-creator-data';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '修复用户开通人数据';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new User())->count();
            $progressBar = $this->output->createProgressBar($count);

            (new User())->eachById(function ($user) use ($progressBar) {
                if ($user['agent_id'] !== -1) {
                    $user['creator_id'] = $user['agent_id'];
                    $user['creator_from'] = User::class;
                } else {
                    $user['creator_from'] = Admin::class;
                    switch ($user['platform_id']) {
                        case 1:
                            $user['creator_id'] = 2;
                            break;
                        case 2:
                            $user['creator_id'] = 7;
                            break;
                        case 3:
                            $user['creator_id'] = 14;
                            break;
                        case 4:
                            $user['creator_id'] = 13;
                            break;
                        case 5:
                            $user['creator_id'] = 15;
                            break;
                        default:
                            $user['creator_id'] = -1;
                    }
                }

                $user->save();
                $progressBar->advance();
            });

            $progressBar->finish();
        });
    }
}
