<?php

namespace App\Console\Commands\Fixures;

use App\Models\Admin;
use App\Models\Policy;
use App\Models\User;
use App\Models\UserInvoice;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixUserInvoiceDataDJ extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'users:fix-invoice-data-dj';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '添加丹晶用户发票信息';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new User())->where('agent_id', 71)->count();
            $progressBar = $this->output->createProgressBar($count);

            (new User())->where('agent_id', 71)->eachById(function ($user) use ($progressBar) {

                $data = $this->prepareInvoiceData();

                UserInvoice::updateOrCreate([
                    'user_id' => $user['id']
                ], $data);

                $progressBar->advance();
            });

            $progressBar->finish();
        });
    }

    protected function prepareInvoiceData()
    {
        return [
            'company_name' => '上海丹晶货运代理有限公司',
            'tax_no' => '91310110MA1G97HP40',
        ];
    }
}
