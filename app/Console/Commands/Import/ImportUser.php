<?php

namespace App\Console\Commands\Import;

use App\Models\Admin;
use App\Models\Platform;
use App\Models\Product;
use App\Models\User;
use App\Models\UserProduct;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class ImportUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:user {file : 导入文件路径} {platform : 目标平台 ID}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '导入用户';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        if (!file_exists($this->argument('file'))) {
            return $this->error('请传入正确的文件路径');
        }

        HeadingRowFormatter::default('none');
        Excel::import(new class($this) extends ImportUser implements ToArray, WithHeadingRow
        {
            /**
             * Reference to the command.
             *
             * @var  \App\Console\Import\ImportUser
             */
            protected $that;

            /**
             * Create ImportUser instance.
             *
             * @param   ImportUser  $that
             *
             * @return  void
             */
            public function __construct(ImportUser $that)
            {
                $this->that = $that;
            }

            /**
             * Specify the heading row.
             *
             * @return  int
             */
            public function headingRow(): int
            {
                return 2;
            }

            /**
             * Import data to array.
             *
             * @param   array  $array
             *
             * @return  void
             */
            public function array(array $array)
            {
                $progressBar = $this->that->output->createProgressBar();
                $progressBar->start(count($array));
                $platform = Platform::with('products.product')->findOrFail($this->that->argument('platform'));
                foreach ($array as $row) {
                    $attributes = $this->mapping($row);

                    if ($this->isExists($attributes['username'])) {
                        $this->that->warn("{$attributes['username']} 已存在");
                        continue;
                    }

                    $this->createUser($platform, $attributes);
                    $this->that->info("{$attributes['username']} 创建成功");
                }

                $progressBar->finish();
            }

            /**
             * 创建用户.
             *
             * @param   Platform  $platform
             * @param   array  $attributes
             *
             * @return  void
             */
            protected function createUser(Platform $platform, array $attributes = []): void
            {
                DB::transaction(function () use ($platform, $attributes) {
                    $user = User::create($attributes);

                    $platform['products']->each(function ($product) use ($user) {
                        if ($product['is_enabled']) {
                            $userProduct = new UserProduct();
                            $userProduct->create(array_merge($product->only($userProduct->getFillable()), [
                                'user_id' => $user['id'],
                                'is_enabled' => in_array($product['product']['type'], [Product::TYPE_DOMESTIC, Product::TYPE_INTL, Product::TYPE_LBT]) ? 0 : 1,
                                'is_premium_sync' => 0,
                                'is_allowed_invoice' => 0,
                            ]));
                        }
                    });
                });
            }

            /**
             * 检查用户是否存在.
             *
             * @param   string  $username
             *
             * @return  bool
             */
            protected function isExists(string $username): bool
            {
                return User::where('username', $username)->exists();
            }

            /**
             * 整理字段对应关系.
             *
             * @param   array  $array
             *
             * @return  array
             */
            protected function mapping(array $array): array
            {
                return [
                    'type' => $array['账户类型*'] === '企业用户' ? User::TYPE_ENTERPRISE : User::TYPE_PERSONAL,
                    'salesman_id' => $this->getSalesmanId($array['业务员*']),
                    'name' => $array['姓名/企业名*'],
                    'email' => $array['邮箱*'],
                    'phone_number' => $array['手机号*'],
                    'address' => $array['地址'],
                    'idcard_no' => $array['账户类型*'] === '企业用户' ? $array['统一社会信用代码*'] : $array['身份证号*'],
                    'username' => $array['用户名*'],
                    'password' => Hash::make($array['密码*']),
                    'platform_id' => $this->that->argument('platform'),
                    'is_agent' => $array['是否是代理商'] === '是' ? 1 : 0,
                    'charge_type' => $array['充值类型'] === '真实充值' ? User::TYPE_REAL_CHARGE : User::TYPE_VIRTUAL_CHARGE,
                    'is_use_insure_preset_data' => false
                ];
            }

            /**
             * 获取业务员 Id.
             *
             * @param   string  $name
             *
             * @return  int
             */
            protected function getSalesmanId(string $salesmanName): int
            {
                return Admin::where('name', $salesmanName)->first()['id'];
            }
        }, $this->argument('file'));
    }
}
