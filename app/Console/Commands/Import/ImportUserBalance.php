<?php

namespace App\Console\Commands\Import;

use App\Models\Admin;
use App\Models\Payment;
use App\Models\Platform;
use App\Models\Product;
use App\Models\User;
use App\Models\UserProduct;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class ImportUserBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:user-balance {file : 导入文件路径}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '导入用户余额财务数据及修改用户密码';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        ini_set('memory_limit', -1);

        if (!file_exists($this->argument('file'))) {
            return $this->error('请传入正确的文件路径');
        }

        HeadingRowFormatter::default('none');
        Excel::import(new class($this) extends ImportUserBalance implements ToArray, WithHeadingRow
        {
            /**
             * Reference to the command.
             *
             * @var  \App\Console\Import\ImportUser
             */
            protected $that;

            /**
             * Create ImportUser instance.
             *
             * @param   ImportUser  $that
             *
             * @return  void
             */
            public function __construct(ImportUserBalance $that)
            {
                $this->that = $that;
            }

            /**
             * Specify the heading row.
             *
             * @return  int
             */
            public function headingRow(): int
            {
                return 1;
            }

            /**
             * Import data to array.
             *
             * @param   array  $array
             *
             * @return  void
             */
            public function array(array $array)
            {
                $progressBar = $this->that->output->createProgressBar();
                $progressBar->start(count($array));
                foreach ($array as $key => $row) {
                    $user = User::where('username', $row['用户名*'])->first();
                    if (!$user) {
                        continue;
                    }
                    $attributes = $this->mapping($row, $user);
                    $attributes['password'] = Hash::make($row['密码*']);
                    $this->updateData($user, $attributes);

                    $this->that->info("{$row['用户名*']} - 数据更新成功 ");
                }

                $progressBar->finish();
            }


            /**
             * 整理字段对应关系
             *
             * @param array $array
             * @param $user
             * @return array
             */
            protected function mapping(array $array, $user): array
            {
                return [
                    'user_id' => $user['id'],
                    'platform_id' => $user['platform_id'],
                    'order_no' => $this->getPaymentOrderNo(),
                    'type' => Payment::TYPE_CHARGE,
                    'charge_type' => Payment::TYPE_VIRTUAL_CHARGE,
                    'amount' => $array['充值金额'],
                    'arrears' => bcdiv($user['arrears'], 100, 5),
                    'apply_id' => 11,
                    'apply_at' => now(),
                    'operated_id' => 11,
                    'operated_at' => now(),
                    'status' => Payment::STATUS_DONE,
                ];
            }

            /**
             * 获取唯一流水号
             *
             * @return string
             */
            protected function getPaymentOrderNo()
            {
                $orderNo = date('Ymd') . substr(implode(null, array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8);
                if (Payment::where('order_no', $orderNo)->exists()) {
                    $orderNo = $this->getPaymentOrderNo();
                }
                return $orderNo;
            }

            protected function updateData($user, $attributes)
            {
                DB::transaction(function () use ($user, $attributes) {
                    $payment = Payment::create($attributes);
                    $user->update([
                        'password' => $attributes['password']
                    ]);
                    $user->charge($payment['amount'] * 100, $payment);
                });
            }
        }, $this->argument('file'));
    }
}
