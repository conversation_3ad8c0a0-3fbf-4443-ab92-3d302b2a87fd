<?php

namespace App\Console\Commands\Import;

use App\Models\Admin;
use App\Models\Platform;
use App\Models\Product;
use App\Models\User;
use App\Models\UserProduct;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Maatwebsite\Excel\Concerns\ToArray;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Imports\HeadingRowFormatter;

class ImportUserProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:user-products {file : 导入文件路径}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '导入用户产品';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        if (!file_exists($this->argument('file'))) {
            return $this->error('请传入正确的文件路径');
        }

        HeadingRowFormatter::default('none');
        Excel::import(new class($this) extends ImportUserProducts implements ToArray, WithHeadingRow
        {
            /**
             * Reference to the command.
             *
             * @var  \App\Console\Import\ImportUser
             */
            protected $that;

            /**
             * Create ImportUser instance.
             *
             * @param   ImportUser  $that
             *
             * @return  void
             */
            public function __construct(ImportUserProducts $that)
            {
                $this->that = $that;
            }

            /**
             * Specify the heading row.
             *
             * @return  int
             */
            public function headingRow(): int
            {
                return 1;
            }

            /**
             * Import data to array.
             *
             * @param   array  $array
             *
             * @return  void
             */
            public function array(array $array)
            {
                $progressBar = $this->that->output->createProgressBar();
                $progressBar->start(count($array));
                foreach ($array as $key => $row) {
                    $product = Product::where('code', $row['产品代码'])->first();
                    if (!$product) {
                        continue;
                    }
                    $usernames = explode('，', $row['配置用户']);
                    $users = User::whereIn('username', $usernames)->get();
                    $this->that->info("{$row['产品代码']} - {$row['产品名称']} - 人数 - " . (count($usernames)));
                    foreach ($users as $user) {
                        $attributes = $this->mapping($row);
                        (new UserProduct())->where('user_id', $user['id'])
                            ->where('product_id', $product['id'])
                            ->update($attributes);
                    }

                    $this->that->info("{$row['产品代码']} - {$row['产品名称']} - 人数 - " . (count($usernames)) . " 配置成功 " . ($key + 2));
                }

                $progressBar->finish();
            }


            /**
             * 整理字段对应关系.
             *
             * @param   array  $array
             *
             * @return  array
             */
            protected function mapping(array $array): array
            {
                return [
                    'agent_commission_rate' => $array['代理佣金比例(%)'],
                    'user_rate' => $array['代理/用户费率(‱)'],
                    'minimum_premium' => $array['最小保费'] * 100,
                    'is_allowed_invoice' => $array['是否允许开票'],
                    'is_premium_sync' => $array['保费同步'],
                    'is_enabled' => 1,
                ];
            }
        }, $this->argument('file'));
    }
}
