<?php

namespace App\Console\Commands\Policy;

use App\Models\Policy;
use App\Models\PolicyFinance;
use App\Models\PremiumReceivable;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FillPolicyFinanceData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'policy:fill-finance-data';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '填充保单财务表数据';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new Policy())
                ->whereIn('status', [Policy::STATUS_ISSUED, Policy::STATUS_CANCELLED, Policy::STATUS_REVISION, Policy::STATUS_CANCELLING])
                ->whereIn('type', [1, 2, 6])
                ->whereNotIn('id', function ($q) {
                    $q->select('policy_id')->from('policy_finances')->get();
                })
                ->count();
            //            dd($count);
            $progressBar = $this->output->createProgressBar($count);

            (new Policy())
                ->with(['user', 'transaction', 'premiumReceivables', 'premiumPayments'])
                ->whereIn('status', [Policy::STATUS_ISSUED, Policy::STATUS_CANCELLED, Policy::STATUS_REVISION, Policy::STATUS_CANCELLING])
                ->whereIn('type', [1, 2, 6])
                ->whereNotIn('id', function ($q) {
                    $q->select('policy_id')->from('policy_finances')->get();
                })
                ->eachById(function ($policy) use ($progressBar) {
                    if ($policy['platform_id'] != $policy['user']['platform_id']) {
                        $policy['premiumReceivables']->where('platform_id', $policy['platform_id'])->each(function ($premiumReceivable) use ($policy) {
                            $data = $this->prepareData($policy, $premiumReceivable, $policy['platform_id']);
                            PolicyFinance::create($data);
                            $data = $this->prepareData($policy, $premiumReceivable, $policy['user']['platform_id']);
                            PolicyFinance::create($data);
                        });
                    } else {
                        if ($policy['type'] == Policy::TYPE_OFFLINE) {
                            $data = $this->prepareOfflineData($policy);
                            PolicyFinance::create($data);
                        } else {
                            if ($policy['is_virtual'] === 1) {
                                $policy['premiumReceivables']->where('platform_id', $policy['platform_id'])->each(function ($premiumReceivable) use ($policy) {
                                    $data = $this->prepareData($policy, $premiumReceivable, $policy['platform_id']);
                                    PolicyFinance::create($data);
                                });
                            } else {
                                $policy['premiumPayments']->where('platform_id', $policy['platform_id'])->each(function ($premiumPayment) use ($policy) {
                                    $data = $this->prepareData($policy, $premiumPayment, $policy['platform_id']);
                                    PolicyFinance::create($data);
                                });
                            }
                        }
                    }
                    $progressBar->advance();
                });

            $progressBar->finish();
        });
    }

    protected function prepareData($policy, $model, $platformId)
    {
        $data = [
            'coverage_currency_id' => $policy['policyCargo']['coverage_currency_id'] ?? -1,
            'policy_no' => $policy['policy_no'],
            'platform_id' => $platformId,
            'policy_id' => $policy['id'],
            'from_id' => -1,
            'coverage' => $policy['coverage'],
            'premium' => $model['cost_premium'],
            'platform_premium' => $model['platform_premium'],
            'agent_premium' => $model['agent_premium'],
            'user_premium' => $model['user_premium'],
            'platform_commission' => 0,
            'agent_commission' => 0,
            'poundage_rate' => $policy['service_charge'],
            'poundage' => round(($model['cost_premium'] * $policy['service_charge']) / 100),
        ];
        $key = get_class($model) == PremiumReceivable::class ? 'receivable_id' : 'payment_id';
        $data[$key] = $model['id'];

        return $data;
    }

    protected function prepareOfflineData($policy)
    {
        $data = [
            'coverage_currency_id' => -1,
            'policy_no' => $policy['policy_no'],
            'platform_id' => $policy['platform_id'],
            'policy_id' => $policy['id'],
            'from_id' => -1,
            'coverage' => 0,
            'premium' => $policy['premium'],
            'platform_premium' => $policy['platform_premium'],
            'agent_premium' => $policy['agent_premium'],
            'user_premium' => $policy['user_premium'],
            'platform_commission' => $policy['platform_commission'],
            'agent_commission' => $policy['agent_commission'],
            'poundage_rate' => $policy['policyOffline']['poundage_rate'],
            'poundage' => $policy['policyOffline']['poundage'],
        ];

        return $data;
    }
}
