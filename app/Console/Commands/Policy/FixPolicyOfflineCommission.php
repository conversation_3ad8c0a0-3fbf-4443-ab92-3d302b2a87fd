<?php

namespace App\Console\Commands\Policy;

use App\Models\CommissionPayment;
use App\Models\Policy;
use App\Models\PolicyFinance;
use App\Services\Finance\CreateFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class FixPolicyOfflineCommission extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'policy:fix-offline-commission';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '修复线下录入保单佣金数据';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new Policy())
                ->whereIn('id', [54407, 55159, 55519, 55551, 55552, 55553, 55984, 57870])
                ->count();
            $progressBar = $this->output->createProgressBar($count);

            (new Policy())
                ->whereIn('id', [54407, 55159, 55519, 55551, 55552, 55553, 55984, 57870])
                ->with(['policyOffline'])
                ->eachById(function ($policy) use ($progressBar) {
                    if (CommissionPayment::where('policy_id', $policy['id'])->doesntExist()) {
                        $data = $this->prepareCommissionPaymentData($policy, $policy['premium'], $policy['agent_commission_rate'], $policy['platform_id'], $policy['platform_id']);
                        if ($policy['user']->isAgentUser()) {
                            $data['user_id'] = $policy['user']['agent_id'];
                        }
                        CommissionPayment::create($data);
                    }
                    $progressBar->advance();
                });

            $progressBar->finish();
        });
    }

    protected function prepareCommissionPaymentData($policy, $premium, $commissionRate, $platformId, $paymentPlatformId)
    {
        $commissionPayment = new CommissionPayment();
        $data = Arr::only($policy->toArray(), $commissionPayment->getFillable());
        $data = array_merge($data, [
            'policy_id' => $policy['id'],
            'endorse_id' => -1,
            'agent_id' => $policy['user']['agent_id'],
            'order_no' => CreateFinances::getOrderNo($commissionPayment, 'CMP'),
            'type' => 1,
            'premium' => round($premium, 2),
            'cost_premium' => $policy['premium'],
            'platform_premium' => $policy['platform_premium'],
            'agent_premium' => $policy['agent_premium'],
            'user_premium' => $policy['user_premium'],
            'commission_rate' => $commissionRate,
            'commission' => round(bcmul($premium, bcdiv($commissionRate, 100, 5), 5), 2),
            'issued_at' => now(),
            'platform_id' => $platformId,
            'payment_platform_id' => $paymentPlatformId,
        ]);
        return $data;
    }
}
