<?php

namespace App\Console\Commands\Policy;

use App\Models\CommissionPayment;
use App\Models\Policy;
use App\Models\PolicyFinance;
use App\Services\Finance\CreateFinances;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class FixPolicyOfflineSubmittedAt extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'policy:fix-offline-submitted_at';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '修复线下录入保单投保时间数据';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        DB::transaction(function () {
            $count = (new Policy())
                ->where('type', Policy::TYPE_OFFLINE)
                ->count();
            $progressBar = $this->output->createProgressBar($count);

            (new Policy())
                ->where('type', Policy::TYPE_OFFLINE)
                ->with(['policyOffline'])
                ->eachById(function ($policy) use ($progressBar) {
                    $policy['submitted_at'] = $policy['policyOffline']['insure_date'];
                    $policy->save();
                    $progressBar->advance();
                });

            $progressBar->finish();
        });
    }
}
