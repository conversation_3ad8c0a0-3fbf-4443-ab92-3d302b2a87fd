<?php

namespace App\Console\Commands\Policy;

use App\Models\Policy;
use App\Services\Policy\ResubmitPolicy;
use Illuminate\Console\Command;

class ResubmitPoliciesInsure extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'policy:resubmit-insure {--order_no= : 流水号}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '重提保单到自动录单';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $orderNos = $this->option('order_no');
        if (empty($orderNos)) {
            $orderNos = $this->getOrderNos();
        } else {
            $orderNos = explode(',', $orderNos);
        }
        Policy::whereIn('order_no', $orderNos)->with('product')->each(function ($policy) {
            try {
                (new ResubmitPolicy)->handle($policy);
                sleep(5);
            } catch (\Exception $e) {
                $this->error($policy['order_no']);
            }
        });
    }

    protected function getOrderNos()
    {
        return Policy::where('user_id', 2540)
        ->where('type', Policy::TYPE_CBEC)
        ->whereDate('submitted_at', '2024-09-04')
        ->whereNotIn('status', [Policy::STATUS_ISSUED, Policy::STATUS_SEND_BACK, Policy::STATUS_SEND_BACK_SUPPLEMENT_INFO, Policy::STATUS_SEND_BACK_SUPPLEMENT_INFO_TICKET])
        ->pluck('order_no')
        ->toArray();
    }
}
