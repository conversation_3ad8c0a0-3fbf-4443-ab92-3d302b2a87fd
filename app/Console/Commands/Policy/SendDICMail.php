<?php

namespace App\Console\Commands\Policy;

use App\Events\PolicyIssued;
use App\Mail\SendDicPolicyMail;
use App\Models\CompanyBranch;
use App\Models\CompanyBranchAccount;
use App\Models\Policy;
use App\Services\Finance\CreateFinances;
use App\Services\Policy\SendDicMailPolicies;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;

class SendDICMail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'policy:send-dic-mail';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '发送东海保单邮件';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        DB::transaction(function () {
            CompanyBranch::with(['company'])
                ->whereHas('company', function ($q) {
                    $q->where('identifier', 'DIC');
                })->each(function ($companyBranch) {
                    $policies = Policy::with(['product', 'company'])
                        ->whereDate('submitted_at', Carbon::today())
                        ->whereHas('product', function ($q) use ($companyBranch) {
                            $q->where('company_branch_id', $companyBranch['id'])
                                ->whereHas('additional', function ($q) {
                                    $q->where('config->policy_mode', 'DIC_EMAIL')
                                        ->orWhere('config->policy_mode', 'API_DIC_LBT');
                                });
                        })
                        ->get();

                    if ($policies->isNotEmpty()) {
                        $this->processPolicies($policies);

                        $toMail = [
                            '<EMAIL>',
                            '<EMAIL>',
                        ];
                        //    $toMail = explode(';', $account['config']['report_email_recipients']);

                        $ccMail = [
                            '<EMAIL>'
                        ];

                        Mail::to($toMail)
                            ->bcc($ccMail)
                            ->send(new SendDicPolicyMail($policies, $companyBranch));
                    }
                });
        });
    }

    /**
     * 处理保单数据
     *
     * @param mixed $policies
     * @return void
     */
    protected function processPolicies($policies)
    {
        $policies->each(function ($policy) {
            // $policy->update([
            //     'policy_no' => $policy['order_no'],
            //     'status' => Policy::STATUS_ISSUED,
            //     'issued_at' => Carbon::now()
            // ]);

            // CreateFinances::handle($policy, CreateFinances::TYPE_INSURE, $policy['premium'], $policy['platform_premium'], $policy['agent_premium'], $policy['user_premium']);

            // PolicyIssued::dispatch($policy);

        });
    }
}
