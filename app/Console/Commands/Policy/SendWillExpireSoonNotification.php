<?php

namespace App\Console\Commands\Policy;

use App\Models\Policy;
use App\Notifications\PolicyWillExpireSoonNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;

class SendWillExpireSoonNotification extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'policy:send-will-expire-soon-notification';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '[保单] 发送即将到期通知';

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        ini_set("memory_limit", "1024M");
        Policy::whereIn('type', [
            Policy::TYPE_GROUP,
            Policy::TYPE_OFFLINE
        ])
            ->with(['user.platform', 'policyGroup', 'policyOffline'])
            ->where(function ($q) {
                $now = now();
                $expiredAt = now()->addDays(7)->endOfDay();
                $q->whereHas('policyGroup', fn($q) => $q->whereBetween('end_at', [$now, $expiredAt]))
                    ->orWhereHas('policyOffline', fn($q) => $q->whereBetween('end_at', [$now, $expiredAt]));
            })
            ->where('status', Policy::STATUS_ISSUED)
            ->get()
            ->groupBy('user_id')
            ->each(function ($policies, $userId) {
                if ($policies->isEmpty()) {
                    return;
                }

                $shouldSend = [];
                foreach ($policies as $policy) {
                    if (Cache::has('policy:has-sent-will-expire-soon-email:' . $policy->id)) {
                        continue;
                    }

                    Cache::put('policy:has-sent-will-expire-soon-email:' . $policy->id, true, now()->addDays(7));
                    $shouldSend[$policy->policy_no] = $policy['type'] === Policy::TYPE_GROUP
                        ? $policy['policyGroup']['end_at']
                        : $policy['policyOffline']['end_at'];
                }

                if (count($shouldSend) === 0) {
                    return;
                }

                // 发送通知
                $policies->first()
                    ->user
                    ->notify(new PolicyWillExpireSoonNotification($shouldSend));
            });
    }
}
