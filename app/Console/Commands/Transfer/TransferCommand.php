<?php

namespace App\Console\Commands\Transfer;

use App\Models\PlatformProduct;
use App\Models\User;
use App\Models\UserProduct;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Throwable;

class TransferCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transfer:run
                            {--u|user= : 需要转移的用户名}
                            {--p|platform= : 老系统 agentId:新系统 platformId，如果指定了用户此处写 platformId}';

    /**
     * The description of the console command.
     *
     * @var string
     */
    protected $description = '转移老系统数据到新系统';

    /**
     * ProgressBar
     *
     * @var \Symfony\Component\Console\Helper\ProgressBar
     */
    protected $progressBar;

    /**
     * Handle the command.
     *
     * @return  void
     */
    public function handle()
    {
        if ($username = $this->option('user')) {
            $this->progressBar = $this->output->createProgressBar(1);

            $this->transferUser($username);
        } else {
            [$agentId, $platformId] = explode(':', $this->option('platform'));

            $this->progressBar = $this->output->createProgressBar($this->db()->table('users')->where('agent_id', $agentId)->count());

            $this->transfer($agentId, $platformId);
        }

        $this->progressBar->finish();
    }

    /**
     * 获取数据库连接实例.
     *
     * @return  \Illuminate\Database\ConnectionInterface
     */
    protected function db()
    {
        return DB::connection('baoya_v1');
    }

    /**
     * Handle transfer single user.
     *
     * @param   string  $username
     *
     * @return  void
     */
    public function transferUser(string $username)
    {
        $user = $this->db()->table('users')->where('username', $username)->first();

        if (!$user) {
            $this->error("用户 {$username} 不存在");
            return;
        }

        $newUser = $this->migrate((array) $user, $this->option('platform'));

        // 用户下属用户.
        $this->db()
            ->table('users')
            ->where('agent_id', $user->id)
            ->eachById(function ($user) use ($newUser) {
                $this->migrate((array) $user, $this->option('platform'), $newUser['id']);
            });
    }

    /**
     * Handle the transfer command.
     *
     * @return  void
     */
    public function transfer(int $agentId, int $platformId)
    {
        $this->db()
            ->table('users')
            ->where('agent_id', $agentId)
            ->eachById(function ($user) use ($platformId) {
                $newUser = $this->migrate((array) $user, $platformId);

                // 用户下属用户.
                $this->db()
                    ->table('users')
                    ->where('agent_id', $user->id)
                    ->eachById(function ($user) use ($platformId, $newUser) {
                        $this->migrate((array) $user, $platformId, $newUser['id']);
                    });
            });
    }

    /**
     * 转移用户信息.
     *
     * @param   array  $user
     * @param   int  $platformId
     * @param   int  $agentId
     *
     * @return  mixed
     */
    protected function migrate(array $user, int $platformId, int $agentId = -1)
    {
        $this->progressBar->advance();

        DB::beginTransaction();
        try {
            $newUser = $this->findCurrentUser($platformId, $user['username']);
            if ($newUser) {
                $this->updateCurrentUser($newUser['id'], $user);
            } else {
                $newUser = $this->newUser($user, $agentId, $platformId);
                $this->assignProduct($newUser['id'], $platformId);
            }

            DB::commit();

            Log::info('用户转移完成', ['source' => $user, 'target' => $newUser]);

            return $newUser;
        } catch (Throwable $e) {
            DB::rollBack();

            Log::info('转移用户失败', ['error' => $e->getMessage(), 'user' => $user]);
        }
    }

    /**
     * 分配用户产品.
     *
     * @param   int  $userId
     * @param   int  $productId
     *
     * @return  void
     */
    protected function assignProduct(int $userId, int $productId)
    {
        $products = (new PlatformProduct())->getProducts($productId);

        if (!$products->isEmpty()) {
            $products->each(function ($product) use ($userId) {
                (new UserProduct())->updateOrCreate([
                    'user_id' => $userId,
                    'product_id' => $product['product_id'],
                    'platform_id' => $product['platform_id'],
                ], [
                    'rate' => $product['rate'],
                    'platform_rate' => $product['platform_rate'],
                    'platform_minimum_premium' => $product['platform_minimum_premium'],
                    'user_rate' => $product['user_rate'],
                    'platform_commission_rate' => $product['platform_commission_rate'],
                    'agent_commission_rate' => $product['agent_commission_rate'],
                    'agent_minimum_premium' => $product['agent_minimum_premium'],
                    'minimum_premium' => $product['minimum_premium'],
                    'is_allowed_invoice' => false,
                    'is_premium_sync' => false,
                    'is_enabled' => false,
                ]);
            });
        }
    }

    /**
     * 根据用户名查找现有用户.
     *
     * @param   string  $username
     *
     * @return  \App\Models\User
     */
    protected function findCurrentUser(int $platformId, string $username)
    {
        return (new User())->where('platform_id', $platformId)
            ->where('username', $username)
            ->first();
    }

    /**
     * 更新现有用户信息.
     *
     * @param   int  $userId
     * @param   array  $user
     *
     * @return  bool
     */
    protected function updateCurrentUser(int $userId, array $user)
    {
        return (new User)->where('id', $userId)
            ->update([
                'remark' => sprintf("该用户从老系统合并过来，原用户ID: %s, 迁移前余额: %.2f", $user['id'], $user['money']),
                'source_user_id' => $user['id'],
            ]);
    }

    /**
     * 创建新用户.
     *
     * @param   array  $user
     * @param   int    $agentId,
     * @param   int    $platformId
     *
     * @return  \Illuminate\Support\Collection
     */
    protected function newUser(array $user, int $agentId, int $platformId)
    {
        return User::create([
            'platform_id' => $platformId,
            'agent_id' => $agentId,
            'name' => $user['fullname'],
            'email' => $user['email'],
            'username' => $user['username'],
            'password' => $user['password'],
            'balance' => $user['money'] * 100,
            'is_agent' => $user['role'] === 'AGENT',
            'phone_number' => $user['mobile'],
            'idcard_no' => $user['id_number'],
            'address' => $user['address'],
            'is_enabled' => false,
            'remark' => sprintf("该用户从老系统转移而来，原用户ID: %s", $user['id']),
            'source_user_id' => $user['id'],
            'created_at' => $user['created_at'],
        ]);
    }
}
