<?php

namespace App\Console\Commands\Transfer;

use App\Exceptions\BusinessException;
use App\Models\Policy;
use App\Models\PolicyGroupAttachment;
use App\Models\PolicyGroupBill;
use App\Models\PolicyGroupEmployee;
use App\Models\PolicyGroupEndorse;
use App\Models\Previous\Payment;
use App\Models\Transaction;
use Illuminate\Console\Command;
use App\Models\Previous\Policy as PreviousPolicy;
use App\Models\Previous\PolicyGroupBill as PreviousPolicyGroupBill;
use App\Models\Previous\PolicyGroupEndorse as PreviousPolicyGroupEndorse;
use App\Models\Previous\PolicyGroupStaff;
use App\Models\Previous\PolicyGroupStaffData;
use App\Models\Previous\PolicyGroupStaffHistory;
use App\Models\Previous\ProductGroupSet;
use App\Models\Product;
use App\Models\ProductGroupJob;
use App\Models\ProductGroupPlan;
use App\Models\User;
use App\Models\UserProduct;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Facades\DB;
use Throwable;

class TransferGroupPolicyCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'transfer:group-policies
        {--user= : 需要迁移的用户 ID}
        {--policy= : 需要迁移的保单号}
        {--begin= : 迁移开始数据时间}
        {--end= : 迁移结束数据时间}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '迁移雇主保单数据到新系统。';

    /**
     * ProgressBar
     *
     * @var \Symfony\Component\Console\Helper\ProgressBar
     */
    protected $progressBar;

    /**
     * 数据信息 Mapping.
     *
     * @var array
     */
    protected $mappings = [
        // 保险公司
        'companies' => [
            2 => 2,
            9 => 5,
            8 => 9,
            10 => 7,
            3 => 1,
            1 => 3
        ],
        // 出单公司
        'branches' => [
            1 => 21,
            11 => 6,
            18 => 13,
            3 => 17,
            14 => 18,
            13 => 19,
            9 => 20,
            2 => 12,
            4 => 9,
            30 => 7,
            32 => 2
        ],
        // 出单渠道
        'channels' => [
            1 => 1,
            4 => 3
        ],
        // 产品
        'products' => [
            26 => 73,
            25 => 1,
            21 => 76,
            20 => 77,
            24 => 3,
            23 => 74,
            22 => 75,
            17 => 221,
            16 => 222,
            15 => 224,
            14 => 225,
            13 => 226,
            12 => 227,
            11 => 228,
            10 => 229,
            9 => 230,
            8 => 231,
            6 => 257,
            7 => 256,
            5 => 258,
            4 => 259,
            3 => 260,
            2 => 261,
            1 => 262,
        ],

        // 发票状态
        'invoiceTypes' => [
            'no' => 'none',
            'plain' => 'normal',
            'special' => 'special',
        ],
        // 批单状态
        'endorseStatus' => [
            0 => PolicyGroupEndorse::STATUS_PROCESSING,
            1 => PolicyGroupEndorse::STATUS_APPROVED,
            2 => PolicyGroupEndorse::STATUS_REJECTED,
        ],
        // 人员操作
        'staffActions' => [
            0 => PolicyGroupEmployee::ACTION_CREATE,
            1 => PolicyGroupEmployee::ACTION_DELETE,
            2 => PolicyGroupEmployee::ACTION_UPDATED
        ],
        // 人员状态
        'staffStatus' => [
            0 => PolicyGroupEmployee::STATUS_INVALID,
            1 => PolicyGroupEmployee::STATUS_UNCOMMITTED,
            2 => PolicyGroupEmployee::STATUS_COMMITTED,
            3 => PolicyGroupEmployee::STATUS_ENDORSING,
            4 => PolicyGroupEmployee::STATUS_ENABLED,
        ],
        'billStatus' => [
            0 => PolicyGroupBill::STATUS_REJECT,
            1 => -1,
            2 => PolicyGroupBill::STATUS_COMMITTED,
            3 => -1,
            4 => PolicyGroupBill::STATUS_ENABLED,
        ],
        // 支付记录状态
        'transactionStatus' => [
            1 => Transaction::STATUS_COMMITTED,
            2 => Transaction::STATUS_DONE,
            3 => Transaction::STATUS_PAID,
            4 => Transaction::STATUS_SEND_BACK
        ]
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // unlimited memory
        ini_set('memory_limit', '-1');

        $this->info('开始迁移雇主保单数据');

        $builder = $this->policyBuilder();
        $this->progressBar = $this->output->createProgressBar($builder->count('id'));

        $builder->eachById(function ($policy) {
            $this->progressBar->advance();

            if ($this->hasMigrated($policy['sys_order_no'])) {
                return $this->error('保单已经迁移过了' . $policy['sys_order_no']);
            }

            try {
                DB::transaction(function () use ($policy) {
                    $newUser = User::where('source_user_id', $policy['user_id'])->first();
                    if ($newUser) {
                        $this->migrate($policy, $newUser);
                    } else {
                        $this->error('用户不存在' . $policy['user_id']);
                    }
                });
            } catch (Throwable $e) {
                $this->error('保单迁移失败' . $policy['sys_order_no'] . 'error: ' . $e->getMessage());
            }
        });

        $this->progressBar->finish();
    }

    /**
     * 保单查询语句.
     *
     * @return  \Illuminate\Database\Query\Builder
     */
    protected function policyBuilder()
    {
        return PreviousPolicy::query()
            ->where('flag', 'GROUP')
            ->with([
                'productGroup',
                'policyGroup',
                'user'
            ])
            ->whereNotIn('status', [
                Policy::STATUS_UNSUBMITTED,
                Policy::STATUS_INVALID,
                Policy::STATUS_CANCELLING,
            ])
            ->whereIn('product_id', array_keys($this->mappings['products']))
            ->when($this->option('user'), function ($query) {
                $query->whereIn('user_id', explode(',', $this->option('user')));
            })
            ->when($this->option('policy'), function ($query) {
                $query->where('policy_no', explode(',', $this->option('policy')));
            })
            ->when($this->option('begin'), function ($query) {
                $query->whereDate('created_at', '>=', $this->option('begin'));
            })
            ->when($this->option('end'), function ($query) {
                $query->whereDate('created_at', '<=', $this->option('end'));
            });
    }

    /**
     * 通过老的保单创建新的保单数据.
     *
     * @param   PreviousPolicy $policy
     * @param   User            $newUser
     *
     * @return  Policy
     */
    protected function migrate(PreviousPolicy $policy, User $newUser)
    {
        $oldProductPlan = ProductGroupSet::findOrFail($policy['policyGroup']['set_id']);

        $newProduct = Product::with(['groupPlans'])->findOrFail($this->mappings['products'][$policy['product_id']]);
        $newProductPlan = $this->getProductPlan($newProduct['id'], $oldProductPlan['title']);

        $this->info('创建新保单' . $policy['sys_order_no']);
        $newPolicy = $this->newPolicy($policy, $newUser, $newProduct, $newProductPlan);

        $this->info('创建新保单附表信息' . $policy['sys_order_no']);
        $this->newPolicyGroup($newPolicy, $policy, $newProduct, $newProductPlan);
        $newPolicy = $newPolicy->fresh();

        $this->info('迁移保单文件' . $policy['sys_order_no']);
        $this->migratePolicyGroupAttachments($newPolicy, $policy);

        $this->info('迁移支付记录' . $policy['sys_order_no']);
        $this->migratePayments($newPolicy, $policy);

        $this->info('迁移批单记录' . $policy['sys_order_no']);
        $this->migrateEndorses($newPolicy, $policy, $newProduct, $newProductPlan);
    }

    /**
     * 迁移雇主批单数据.
     *
     * @param   Policy          $newPolicy
     * @param   PreviousPolicy  $policy
     * @param   Product         $newProduct
     * @param   ProductGroupPlan $newProductPlan
     *
     * @return  void
     */
    protected function migrateEndorses(
        Policy $newPolicy,
        PreviousPolicy $policy,
        Product $newProduct,
        ProductGroupPlan $newProductPlan
    ) {
        $migratedEndorse = [];
        PolicyGroupStaff::where('policy_id', $policy['id'])
            ->whereIn('status', [
                PolicyGroupEmployee::STATUS_COMMITTED,
                PolicyGroupEmployee::STATUS_INVALID,
                PolicyGroupEmployee::STATUS_ENDORSING,
                PolicyGroupEmployee::STATUS_ENABLED,
            ])
            ->get()
            ->groupBy('endorse_id')
            ->each(function ($endorse) use ($newPolicy, $newProduct, $newProductPlan, &$migratedEndorse) {
                $endorseId = $endorse->first()['endorse_id'];
                $migratedEndorse[] = $endorseId;
                $newEndorse = collect();
                if ($endorseId !== 0) {
                    $this->info('迁移批单' . $endorseId);
                    $newEndorse = $this->newEndorse($endorseId, $newPolicy);
                }

                $endorse->each(function ($employee) use ($newEndorse, $newPolicy, $newProduct, $newProductPlan) {
                    $this->info('迁移批单人员' . ($newEndorse['batch_no'] ?? '无') . '-' . $employee['fullname']);
                    $this->migrateEmployee($newPolicy, $employee, $newEndorse, $newProduct, $newProductPlan);
                });
            });

        $this->migrateMissingEndorse($migratedEndorse, $newPolicy, $policy, $newProduct, $newProductPlan);
    }

    /**
     * 迁移缺失的批单数据.
     *
     * @param   array           $migratedEndorse
     * @param   Policy          $newPolicy
     * @param   PreviousPolicy  $policy
     * @param   Product         $newProduct
     * @param   ProductGroupPlan  $newProductPlan
     *
     * @return  void
     */
    protected function migrateMissingEndorse(
        array $migratedEndorse,
        Policy $newPolicy,
        PreviousPolicy $policy,
        Product $newProduct,
        ProductGroupPlan $newProductPlan
    ) {
        PreviousPolicyGroupEndorse::where('policy_id', $policy['id'])
            ->whereNotIn('id', $migratedEndorse)
            ->get()
            ->each(function ($endorse) use ($newPolicy, $newProduct, $newProductPlan) {
                $this->info('迁移缺失批单记录' . $endorse['endorse_no']);
                $newEndorse = $this->newEndorse($endorse['id'], $newPolicy);
                PolicyGroupStaffData::where('endorse_id', $endorse['id'])
                    ->get()
                    ->each(function ($employee) use ($newEndorse, $newPolicy, $newProduct, $newProductPlan) {
                        $this->info('迁移缺失批单人员' . ($endorse['batch_no'] ?? $newEndorse['endorse_no']) . '-' . $employee['fullname']);
                        $action = $this->mappings['staffActions'][$employee['is_update']];
                        $status = $action === PolicyGroupEmployee::ACTION_DELETE ? PolicyGroupEmployee::STATUS_REMOVED : PolicyGroupEmployee::STATUS_REPLACED;
                        $this->migrateEmployee(
                            $newPolicy,
                            $employee,
                            $newEndorse,
                            $newProduct,
                            $newProductPlan,
                            $status
                        );
                    });
            });
    }

    /**
     * 迁移雇主人员记录.
     *
     * @param   Policy  $newPolicy
     * @param   PolicyGroupStaff|PolicyGroupStaffData  $staff
     * @param   Arrayable  $endorse
     * @param   Product  $newProduct
     * @param   ProductGroupPlan  $newProductPlan
     * @param   int  $status
     *
     * @return  void
     */
    protected function migrateEmployee(
        Policy $newPolicy,
        $staff,
        Arrayable $newEndorse,
        Product $newProduct,
        ProductGroupPlan $newProductPlan,
        int $status = null
    ) {
        $currentStatus = is_null($status) ? $this->currentEmployeeStatus($staff['is_update'], $staff['status']) : $status;
        $staffId = $staff instanceof PolicyGroupStaff ? $staff['id'] : $staff['staff_id'];
        $totalFee = $this->employeeTotalFee($staffId, $staff['is_update'], $staff['endorse_id']);
        $job = $this->getProductJob($newProductPlan, $staff['job_code']);

        $employeeId = -1;
        if (
            $this->mappings['staffActions'][$staff['is_update']] === PolicyGroupEmployee::ACTION_UPDATED
            && $currentStatus === PolicyGroupEmployee::STATUS_ENDORSING
        ) {
            $historyEmpolyee = $this->createHistoryEmployee($newPolicy, $staff, $newEndorse, $newProduct, $newProductPlan);
            $employeeId = $historyEmpolyee['id'];
        }

        $this->newEmployee($newPolicy, $newEndorse, $job, $staff, $totalFee, $employeeId, $currentStatus);
    }

    /**
     * 创建批改记录中的历史人员.
     *
     * @param   Policy  $newPolicy
     * @param   PolicyGroupStaff|PolicyGroupStaffData  $staff
     * @param   Arrayable  $newEndorse
     * @param   Product  $newProduct
     * @param   ProductGroupPlan  $newProductPlan
     *
     * @return  PolicyGroupEmployee
     */
    protected function createHistoryEmployee(
        Policy $newPolicy,
        $staff,
        PolicyGroupEndorse $newEndorse,
        Product $newProduct,
        ProductGroupPlan $newProductPlan
    ) {
        $history = PolicyGroupStaffHistory::where('id', function ($query) use ($staff) {
            $query->select('history_id')
                ->from('policy_group_endorse_staffs_data')
                ->where('endorse_id', $staff['endorse_id']);
        })
            ->first();

        $staffId = $staff instanceof PolicyGroupStaff ? $staff['id'] : $staff['staff_id'];
        $totalFee = $this->employeeTotalFee($staffId, $staff['is_update'], $staff['endorse_id']);
        $totalFee = $this->employeeTotalFee($staffId, $staff['is_update'], $history['endorse_id']);
        $job = $this->getProductJob($newProductPlan, $history['job_code']);

        return $this->newEmployee($newPolicy, $newEndorse, $job, $history, $totalFee, -1, PolicyGroupEmployee::STATUS_ENABLED);
    }

    /**
     * 创建新的雇主员工.
     *
     * @param   Policy  $newPolicy
     * @param   Arrayable  $newEndorse
     * @param   ProductGroupJob  $job
     * @param   Arrayable  $staff
     * @param   float  $totalFee
     * @param   int  $historyId
     * @param   int  $status
     *
     * @return  PolicyGroupEmployee
     */
    protected function newEmployee(
        Policy $newPolicy,
        Arrayable $newEndorse,
        ProductGroupJob $job,
        Arrayable $staff,
        float $totalFee,
        int $historyId,
        int $status
    ) {
        $employee = (new PolicyGroupEmployee())->create([
            'policy_id' => $newPolicy['id'],
            'policy_group_id' => $newPolicy['policyGroup']['id'],
            'endorse_id' => $newEndorse['id'] ?? 0,
            'employee_id' => $historyId,
            'name' => $staff['fullname'],
            'idcard_no' => $staff['id_number'],
            'mobile' => $staff['mobile'],
            'job_code' => $job['code'],
            'job_name' => $job['name'],
            'job_id' => $job['id'],
            'action' => $this->mappings['staffActions'][$staff['is_update']] ?? PolicyGroupEmployee::ACTION_UPDATED,
            'status' => $status,
            'fee' => $totalFee,
            'complete_time' => $staff['complete_time'] ?? $staff->created_at,
            'created_at' => $staff->created_at,
            'updated_at' => $staff->updated_at,
        ]);

        (new PolicyGroupBill())->newQuery()->create([
            'policy_id' => $newPolicy->id,
            'policy_group_id' => $newPolicy['policyGroup']['id'],
            'employee_id' => $employee['id'],
            'fee' => $employee['fee'],
            'status' => PolicyGroupBill::STATUS_ENABLED, // 状态管理
        ]);

        return $employee;
    }

    /**
     * 老的人员状态对应当前的人员状态.
     *
     * @param   int  $isUpdate
     * @param   int  $status
     *
     * @return  int
     */
    protected function currentEmployeeStatus(int $isUpdate, int $status)
    {
        if ($status !== PolicyGroupEmployee::STATUS_ENABLED) {
            return $status;
        }

        $action = $this->mappings['staffActions'][$isUpdate];

        // 批减状态.
        if ($action === PolicyGroupEmployee::ACTION_DELETE && $status === 0) {
            return PolicyGroupEmployee::STATUS_REMOVED;
        }

        return PolicyGroupEmployee::STATUS_ENABLED;
    }

    /**
     * 雇主人员费用.
     *
     * @param   int  $staffId
     * @param   int  $isUpdate
     * @param   int  $endorseId
     *
     * @return  float
     */
    protected function employeeTotalFee(int $staffId, int $isUpdate, int $endorseId)
    {
        // 已经替换的人员.
        if ($isUpdate === 2) {
            return PreviousPolicyGroupBill::where('staff_id', $staffId)->sum('fee');
        }

        return PreviousPolicyGroupBill::where('staff_id', $staffId)
            ->where('endorse_id', $endorseId)
            ->first()['fee'] ?? 0;
    }

    /**
     * 套餐工种信息
     *
     * @param   ProductGroupPlan  $productGroupPlan
     * @param   string  $jobCode
     * @return  \App\Models\ProductGroupJob
     * @throws  BusinessException
     */
    private function getProductJob(ProductGroupPlan $productGroupPlan, string $jobCode)
    {
        /** @var \App\Models\ProductGroupJob $job */
        $job = $productGroupPlan->jobs->where('code', $jobCode)->first();
        if (!$job) {
            throw new BusinessException("套餐工种信息未同步，数据迁移失败。");
        }

        return $job;
    }

    /**
     * 创建新的批单记录.
     *
     * @param   int  $endorseId
     * @param   Policy          $newPolicy
     *
     * @return  PolicyGroupEndorse
     */
    protected function newEndorse(int $endorseId, Policy $newPolicy)
    {
        $endorse = PreviousPolicyGroupEndorse::find($endorseId);

        return (new PolicyGroupEndorse())->create([
            'policy_id' => $newPolicy['id'],
            'batch_no' => $endorse->endorse_no,
            'policy_group_id' => $newPolicy['policyGroup']['id'],
            'transaction_id' => -1, // @todo:: 获取交易记录
            'transaction_file' => '', // @todo:: 获取交易记录
            'total_fee' => 0,
            'operator_id' => -1,
            'endorse_no' => $endorse->endorse_number,
            'endorse_file' => $this->currentMigratedFilePath($endorse->endorse_file),
            'endorse_stamp_file' => $this->currentMigratedFilePath($endorse->endorse_stamp_file),
            'content' => $endorse->content,
            'status' => $this->mappings['endorseStatus'][$endorse->status],
            'created_at' => $endorse->created_at,
            'updated_at' => $endorse->updated_at,
        ]);
    }

    /**
     * 迁移支付记录信息.
     *
     * @param   Policy $newPolicy
     * @param   PreviousPolicy $policy
     *
     * @return  void
     */
    protected function migratePayments(Policy $newPolicy, PreviousPolicy $policy)
    {
        Payment::where('policy_id', $policy['id'])
            ->eachById(function ($payment) use ($newPolicy, $policy) {
                $totalFee = $payment->amount * 100;
                Transaction::create([
                    'user_id' => $newPolicy->user_id,
                    'type' =>  $totalFee >= 0 ? Transaction::TYPE_REFUND : Transaction::TYPE_PAY,
                    'is_offline' => 1,
                    'transaction_id' => $newPolicy['policyGroup']['id'],
                    'transaction_type' => get_class($newPolicy['policyGroup']),
                    'transaction_no' => $payment->bill_no,
                    'amount' => $totalFee,
                    'proof' => $this->currentMigratedFilePath($payment['attachment']),
                    'balance' => 0,
                    'remark' => $totalFee >= 0 ? '雇主线下支付' : '批单退款',
                    'status' => $this->mappings['transactionStatus'][$payment['status']],
                    'created_at' => $payment->created_at,
                    'updated_at' => $payment->updated_at,
                ]);
            });
    }

    /**
     * 迁移保单附件.
     *
     * @param   Policy $newPolicy
     * @param   PreviousPolicy $policy
     *
     * @return  void
     */
    protected function migratePolicyGroupAttachments(Policy $newPolicy, PreviousPolicy $policy)
    {
        $attachments = [
            'policy_group_id' => $newPolicy['policyGroup']['id'],
            'staff_list_file' => $this->currentMigratedFilePath($policy['policyGroup']['personnel_list']),
            'staff_stamp_list_file' => $this->currentMigratedFilePath($policy['policyGroup']['personnel_stamp_list']),
            'application_file' => $this->currentMigratedFilePath($policy['policyGroup']['apply_file']),
            'application_stamp_file' => $this->currentMigratedFilePath($policy['policyGroup']['apply_stamp_file']),
            'business_license_file' => $this->currentMigratedFilePath($policy['policyGroup']['business_license']),
            'authorization_file' => $this->currentMigratedFilePath($policy['policyGroup']['authorize_file']),
            'extra_file' => $this->currentMigratedFilePath($policy['policyGroup']['other_file']),
        ];

        (new PolicyGroupAttachment())->newQuery()->create($attachments);
    }

    /**
     * 创建新的保单附表数据.
     *
     * @param   Policy          $newPolicy
     * @param   PreviousPolicy  $policy
     * @param   Product         $newProduct
     * @param   ProductGroupPlan  $newProductPlan
     *
     * @return void
     */
    protected function newPolicyGroup(Policy $newPolicy, PreviousPolicy $policy, Product $newProduct, ProductGroupPlan $newProductPlan)
    {
        $oldInvoiceInfo = json_decode($policy['policyGroup']->invoices_content, true);
        $invoiceInfo = [
            'type' => $this->mappings['invoiceTypes'][$oldInvoiceInfo['invoice_type'] ?? 'no'] ?? null,
            'title' => $oldInvoiceInfo['title'] ?? '',
            'tax_no' => $oldInvoiceInfo['tax_no'] ?? '',
            'bank_name' => $oldInvoiceInfo['bank'] ?? '',
            'bankcard_no' => $oldInvoiceInfo['card_no'] ?? '',
            'phone_number' => $oldInvoiceInfo['company_phone'] ?? '',
            'registered_addr' => $oldInvoiceInfo['company_address'] ?? ''
        ];

        // 中意雇主其他信息
        $extraInfo = [
            'API_GROUP_ZY' => [
                'cityCode' => $oldExtraInfo['cityCode'] ?? '',
                'cityName' => $oldExtraInfo['cityName'] ?? '',
                'insuredIdNo' => $oldExtraInfo['insuredIdNo'] ?? '',
                'insuredName' => $oldExtraInfo['insuredName'] ?? '',
                'provinceCode' => $oldExtraInfo['provinceCode'] ?? '',
                'provinceName' => $oldExtraInfo['provinceName'] ?? '',
                'activeAddress' => $oldExtraInfo['activeAddress'] ?? '',
                'applicantIdNo' => $oldExtraInfo['applicantIdNo'] ?? '',
                'applicantName' => $oldExtraInfo['applicantName'] ?? '',
                'insuredIdType' => $oldExtraInfo['insuredIdType'] ?? '',
                'insuredMobile' => $oldExtraInfo['insuredMobile'] ?? '',
                'applicantIdType' => $oldExtraInfo['applicantIdType'] ?? '',
                'applicantMobile' => $oldExtraInfo['applicantMobile'] ?? ''
            ],
            'object_address' => [
                $oldExtraInfo['provinceName'] ?? '',
                $oldExtraInfo['cityName'] ?? ''
            ],
            'insured_idcard_type' => '13',
            'object_address_detail' => $oldExtraInfo['activeAddress'] ?? '',
            'policyholder_idcard_type' => '13'
        ];

        // 保单副表
        $newPolicy->policyGroup()->create([
            'product_id' => $newProduct->id,
            'group_plan_id' => $newProductPlan->id,
            'contact_name' => $policy['policyGroup']->contact_name,
            'contact_phone' => $policy['policyGroup']->contact_phone,
            'invoice_info' => $invoiceInfo,
            'is_paid' => 1,
            'start_at' => $policy->start_time,
            'end_at' => $policy->end_time,
            'extra_info' => $extraInfo,
            'notes' => "该保单是从老系统转移过来的，原始保单流水号：{$policy->sys_order_no}, ID：{$policy->id}",
        ]);
    }

    /**
     * 创建新保单.
     *
     * @param   PreviousPolicy $policy
     * @param   User  $user
     * @param   Product  $newProduct
     * @param   ProductGroupPlan  $newProductPlan
     *
     * @return  Policy
     */
    protected function newPolicy(PreviousPolicy $policy, User $user, Product $newProduct, ProductGroupPlan $newProductPlan)
    {
        $oldProduct = $policy['productGroup'];

        $userProduct = (new UserProduct())->getProduct($user['id'], $newProduct['id']) ?? optional();
        $agentProduct = $user->isAgentUser() ? (new UserProduct())->getProduct($user['agent_id'], $newProduct['id']) : $userProduct;

        $commissions = $this->calcCommissions($policy['user_premium'] * 100, $userProduct, $agentProduct);
        $extraInfo = json_decode($policy['policyGroup']['additions'], true) ?: [];

        return (new Policy())->newQuery()->create(array_merge([
            'type' => Policy::TYPE_GROUP,
            'platform_id' => $newProduct->platform_id,
            'product_id' => $newProduct->id,
            'user_id' => $user->id,
            'plan_id' => $newProductPlan->id,
            'company_id' => $this->mappings['companies'][$oldProduct['ins_company_id']],
            'company_branch_id' => $this->mappings['branches'][$oldProduct['ins_company_branch_id']],
            'channel_id' => $this->mappings['channels'][$oldProduct['ins_channel_id']],
            'salesman_id' => -1,
            'invoice_id' => -1,
            'order_no' => $policy->sys_order_no,
            'trade_order_no' => $policy->out_order_no,
            'apply_no' => $policy->apply_no,
            'policy_no' => $policy->policy_no,
            'policy_file' => $this->currentMigratedFilePath($policy['policy_file']),
            'rate' => 0,
            'platform_rate' => 0,
            'agent_rate' => 0,
            'user_rate' => 0,
            'minimum_premium' => 0,
            'platform_minimum_premium' => 0,
            'agent_minimum_premium' => 0,
            'user_minimum_premium' => 0,
            'coverage' => 0,
            'policyholder' => $policy->applicant_name,
            'policyholder_phone_number' => $policy->applicant_phone ?? ($extraInfo['applicantMobile'] ?? ''),
            'policyholder_address' => $policy->applicant_address,
            'policyholder_idcard_no' => $policy->applicant_idnumber ?? ($extraInfo['applicantIdNo'] ?? ''),
            'insured' => $policy->insured_name,
            'insured_phone_number' => $policy->insured_phone ?? ($extraInfo['insuredMobile'] ?? ''),
            'insured_address' => $policy->insured_address,
            'insured_idcard_no' => $policy->insured_idnumber ?? ($extraInfo['insuredIdNo'] ?? ''),
            'sendback_reason' => $policy->callback,
            'surrender_reason' => '',
            'status' => $policy->status,
            'remark' => $policy->marks,
            'is_premium_sync' => $policy->is_premium_sync,
            'is_allowed_invoice' => false,
            'submitted_at' => $policy->created_at,
            'issued_at' => $policy->created_at,
            'created_at' => $policy->created_at,
            'updated_at' => $policy->updated_at,
        ], $commissions));
    }

    /**
     * 新的文件路径.
     *
     * @param   string  $oldFilePath
     *
     * @return  string
     */
    protected function currentMigratedFilePath(?string $oldFilePath = null)
    {
        if (!$oldFilePath) {
            return '';
        }

        return 'policy/previous/uploads/' . ltrim($oldFilePath, '/');
    }

    /**
     * 计算佣金信息.
     *
     * @param   int  $userPremium
     * @param   \Illuminate\Contracts\Support\Arrayable $userProduct
     * @param   \Illuminate\Contracts\Support\Arrayable $agentProduct
     *
     * @return  array
     */
    protected function calcCommissions(int $userPremium, $userProduct, $agentProduct): array
    {
        return [
            'premium' => $userPremium,
            'platform_premium' => $userPremium,
            'agent_premium' => $userPremium,
            'user_premium' => $userPremium,
            //
            'agent_commission_rate' => $agentProduct['agent_commission_rate'] ?? 0,
            'agent_commission' => $userPremium * (($agentProduct['agent_commission_rate'] ?? 0) / 100),
            'platform_commission_rate' => $userProduct['platform_commission_rate'] ?? 0,
            'platform_commission' => $userPremium * (($userProduct['platform_commission_rate'] ?? 0) / 100)
        ];
    }

    /**
     * 检查保单是否已经迁移过了.
     *
     * @param  string  $sysOrderNo
     *
     * @return  bool
     */
    protected function hasMigrated(?string $sysOrderNo = null)
    {
        return Policy::where('order_no', $sysOrderNo)->exists();
    }

    /**
     * 对应套餐标题，获取产品套餐
     *
     * @param    int  $productId
     * @param    string  $title
     *
     * @return   ProductGroupPlan
     * @throws   BusinessException
     */
    private function getProductPlan(int $productId, string $title)
    {
        $plan = ProductGroupPlan::query()
            ->where([
                'product_id' => $productId,
                'title' => trim($title)
            ])
            ->with(['jobs'])
            ->first();

        if (!$plan) {
            throw new BusinessException("老系统产品套餐信息未同步，数据迁移失败。");
        }

        return $plan;
    }
}
