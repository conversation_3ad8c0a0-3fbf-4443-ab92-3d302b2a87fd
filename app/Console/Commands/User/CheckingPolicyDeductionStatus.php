<?php

namespace App\Console\Commands\User;

use App\Models\Policy;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Console\Command;
use Maatwebsite\Excel\Facades\Excel;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Storage;

class CheckingPolicyDeductionStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:check-policy-deduction-status {--user_id= : 用户ID}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检测用户保单扣款状态';

    protected $processedPolicyNos = [
        "PYIE202344942302E27017","PYIE202344942302E27585","ASHH01124223QAA7NULQ","PYIE202344942302E27586","PYIE202344942302E27589","PYIE202344942302E27584","PYIE202344942302E27590","PYIE202344942302E26173","PYIE202344942302E26175","ASHH01124223QAA7NV1G","PYIE202344942302E26176","PYIE202344942302E26240","PYIE202344942302E26170","PYIE202344942302E26285","PYIE202344942302E24765","PYIE202344942302E27981","PYIE202344942302E24773","PYIE202344942302E24766","PYIE202344942302E24775","PYIE202344942302E24759","PYIE202344942302E24758","PYIE202344942302E24767","PYIE202344942302E24716","PYIE202344942302E24779","PYIE202344942302E24762","PYIE202344942302E24768","PYIE202344942302E24764","PYIE202344942302E24769","PYIE202344942302E24623","PYIE202344942302E27733","PYIE202344942302E24081","10219013902062784463","PYIE202344942302E26613","PYII202344942302E02073","PYII202344942302E02233","PYII202344942302E02378","PYIE202344942302E25729","PYIE202344942302E27265","AQID02T24223QAAAQZTY","PYIE202344942302E27846","PYIE202344942302E26610","PYIE202344942302E26612","PYIE202344942302E27606","PYIE202344942302E26323","PYIE202344942302E25552","PYIE202344942302E26324","PYIE202344942302E27612","PYIE202344942302E24737","PYIE202344942302E25549","PYIE202344942302E24738","PYIE202344942302E26445","PYIE202344942302E24086","PYIE202344942302E25371","PYIE202344942302E23841","PYIE202344942302E25367","PYIE202344942302E25372","PYIE202344942302E25363","PYIE202344942302E25368","PYIE202344942302E25364","PYIE202344942302E25369","PYIE202344942302E23839","PYIE202344942302E25365","PYIE202344942302E24085","PYIE202344942302E25370","PYIE202344942302E23840","PYIE202344942302E25366","PYII202344942302E02120","PYIE202344942302E27637","PYIE202344942302E27638","PYIE202344942302E27640","PYIE202344942302E26652","PYIE202344942302E27636","PYIE202344942302E24651","PYIE202344942302E27013","10219013902071101024","PYIE202344942302E27257","PYIE202344942302E27244","PYIE202344942302E24338","PYIE202344942302E26362","PYIE202344942302E26077","PYIE202344942302E27254","PYIE202344942302E24801","PYIE202344942302E27255","PYIE202344942302E27230","PYIE202344942302E25002","ASHH01124223QAA6USTD","ASHH01124223QAA72PQD","ASHH01124223QAA7Q29G","ASHH01124223QAA7521E","ASHH01124223QAA6USRM","ASHH01124223QAA7522Z","ASHH01124223QAA6USSL","10219013902061432248","PYIE202344942302E24756","PYIE202344942302E23708","PYIE202344942302E24887","PYIE202344942302E23700","PYIE202344942302E27437","PYIE202344942302E24145","PYIE202344942302E25465","PYIE202344942302E24372","PYIE202344942302E25389","PYIE202344942302E27157","PYIE202344942302E26659","10219013902076620516","PYIE202344942302E24026","PYIE202344942302E24862","PYIE202344942302E27235","PYIE202344942302E24921","PYIE202344942302E24368","PYIE202344942302E24975","PYIE202344942302E24983","AQID02T24223QAAAR0NP",'10219013901969702407','AQID02T04123QAAAA30O','PYDL202344942302E00497','PYDL202344942302E00513','PYDL202344942302E00514','PYIE202344942302E08057','PYIE202344942302E08173','PYIE202344942302E08321','PYIE202344942302E08452','PYIE202344942302E08456','PYIE202344942302E08554','PYIE202344942302E08557','PYIE202344942302E08563','PYIE202344942302E08564','PYIE202344942302E08823','PYIE202344942302E08929','PYIE202344942302E08980','PYIE202344942302E09073','PYIE202344942302E09074','PYIE202344942302E09075','PYII202344942302E00744','PYII202344942302E00784','PYII202344942302E00785','10219013901969702407','AQID02T04123QAAAA30O','PYDL202344942302E00497','PYDL202344942302E00513','PYDL202344942302E00514','PYIE202344942302E08057','PYIE202344942302E08173','PYIE202344942302E08321','PYIE202344942302E08452','PYIE202344942302E08456','PYIE202344942302E08554','PYIE202344942302E08557','PYIE202344942302E08563','PYIE202344942302E08564','PYIE202344942302E08823','PYIE202344942302E08929','PYIE202344942302E08980','PYIE202344942302E09073','PYIE202344942302E09074','PYIE202344942302E09075','PYII202344942302E00744','PYII202344942302E00784','PYII202344942302E00785',"PYIE202344942302E40843","PYIE202344942302E40844","PYIE202344942302E40846","PYIE202344942302E40847","PYIE202344942302E40850","PYIE202344942302E40851","PYIE202344942302E40852","PYIE202344942302E40853","PYIE202344942302E40854","PYIE202344942302E40856","PYIE202344942302E40857","PYIE202344942302E40858","PYIE202344942302E40859","PYIE202344942302E40566",'PYIE202344942302E08404','PYIE202344942302E08741','PYIE202344942302E08981','PYIE202344942302E08982','PYIE202344942302E08983','PYIE202344942302E08984','PYIE202344942302E08993','PYIE202344942302E08994',
        'PYIE202344942302E40881', 'PYIE202344942302E40930', 'PYIE202344942302E40574', 'PYIE202344942302E40944', 'PYIE202344942302E40788', 'PYIE202344942302E22099'
    ];

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        ini_set("memory_limit", "2048M");

        $userId = $this->option('user_id');

        // 构造查询，判断是否有user_id传入
        $policiesQuery = Policy2::with([
            'policyCargo:id,policy_id,subject_id',
            'user' => fn($q) => $q->with('transactions:id,user_id,transaction_id,amount,type'),
            'transaction:id,user_id,transaction_id,amount,type,policy_no',

        ])
            // ->where('submitted_at', '>=', '2022-12-01 00:00:00')
            // ->whereNotNull('policy_no')
            ->where('created_at', '>=', '2023-01-01')
            ->whereHas('policyCargo', function ($q) {
                $q->where('subject_id', 3);
            })
            ->whereNotIn('user_id', [539, 728, 2185])
            ->whereNotIn('policy_no', $this->processedPolicyNos);

        // 如果有user_id，则添加过滤条件
        if (!empty($userId)) {
            $policiesQuery->where('user_id', $userId);
        }

        // 获取所有符合条件的保单，并按user_id分组
        $policies = $policiesQuery->get()->groupBy('user_id');

        $chargeSum = 0;
        $premiumSum = 0;
        $differenceSum = 0;

        if (!empty($userId)) {
            $user = User::with('transactions')->find($userId);

            $balanceAmount = 0;
            $chargeSum = 0;
            $insureAmount = 0;
            $refundAmount = 0;
            $fiannceCount = 0;
            $financePremium = 0;
            $financePremiumSum = 0;
            $userTransactionIds = [];
            $policyTransactionIds = [];

            foreach ($user['transactions'] as $transaction) {
                if ($transaction['type'] == 1) {
                    $chargeSum += $transaction['amount'];
                    $balanceAmount += $transaction['amount'];
                }
                if ($transaction['type'] == 2) {
                    $userTransactionIds[] = $transaction['id'];
                    $insureAmount += $transaction['amount'];
                    $balanceAmount -= $transaction['amount'];
                }
                if ($transaction['type'] == 3) {
                    $refundAmount += $transaction['amount'];
                    $balanceAmount += $transaction['amount'];
                }
                if ($transaction['balance'] != $balanceAmount) {
                    if ($transaction['id'] == 63996) {
                        $balanceAmount = $transaction['balance'];
                        $chargeSum = $transaction['balance'];
                        $insureAmount = 0;
                        $refundAmount = 0;
                        $userTransactionIds = [];
                        // continue;
                    }
                    // dump($transaction['id']);
                    // dd($balanceAmount, $chargeSum, $insureAmount, $refundAmount);
                }
            }
            // dump(count($userTransactionIds));
            dump(
                '用户余额: ' . $balanceAmount,
                '用户充值: ' . $chargeSum,
                '用户扣费: ' . $insureAmount,
                '用户退款: ' . $refundAmount
            );


            // $chargeSum = $user->transactions()->where('type', 1)->sum('amount');
            // $insureAmount = $user->transactions()->where('type', 2)->sum('amount');
            // $refundAmount = $user->transactions()->where('type', 3)->sum('amount');

            // dump($chargeSum, $insureAmount - $refundAmount);

            // dd($chargeSum, $insureAmount, $refundAmount, $user['balance']);

            // 遍历分组后的保单数据
            $payAmountSum = 0;
            $refundAmountSum = 0;
            foreach ($policies as $userId => $userPolicies) {
                foreach ($userPolicies as $policy) {
                    // dd($userPolicies->sum('agent_premium'));
                    $payAmount = $policy->transaction->where('type', 2)->sum('amount');
                    $refundAmount = $policy->transaction->where('type', 3)->sum('amount');

                    $policyTransactionIds = array_merge($policyTransactionIds, $policy->transaction->pluck('id')->toArray());

                    $payAmountSum += $payAmount;
                    $refundAmountSum += $refundAmount;

                    $difference = $payAmount - $refundAmount;
                    $differenceSum += $difference;


                    $premium = $policy['user']['is_agent'] == 1 ? $policy['agent_premium'] : $policy['user_premium'];
                    $premiumSum += $premium;
                    if ($policy['status'] == Policy::STATUS_CANCELLED) {
                        $premiumSum -= $premium;
                    }

                    if ($premium != $difference && !($policy['status'] == Policy::STATUS_CANCELLED && $difference == 0)) {
                        dd($policy['id'], $payAmount, $refundAmount, $difference);
                    }
                }
            }
            $transactions = Transaction2::whereIn('id', $userTransactionIds)
                ->whereNotIn('id', $policyTransactionIds)
                ->whereNotIn('policy_no', ['10219013901856200312', '10219013901873708742', '10219013902019805898', '10219013902022121205', '10219013902022128119', '10219013902025510986', '10219013902028397871', '10219013902029441524', '10219013902037699921', '10219013902037702518', '10219013902038742026', '10219013902041903151', '10219013902044363825', '10219013902071372081'])
                ->get();
            dump($transactions->sum('amount'));
            $transactions->each(function ($transaction) {
                // dump($transaction->id, $transaction->user_id, $transaction->amount, $transaction->policy_no);
            });
            // dd($transactions->pluck('id')->toArray());
            // dump(count($policyTransactionIds));
            dd(
                '保单投保扣费: ' . $payAmountSum,
                '保单退款: ' . $refundAmountSum,
                '保单差额: ' . $differenceSum,
                '保费: ' . $premiumSum
            );
        }

        try {
            // 生成唯一的文件名
            $filename = '人工审核保费异常保单导出' . now()->format('YmdHis') . '.xlsx';

            // 导出 Excel 文件
            Excel::store(new PolicyTransactionExceptionExport($policies), $filename, 'public');

            // 打印文件路径
            $path = Storage::disk('public')->path($filename);

            $this->info('导出完成，文件地址：' . $path);
            $this->info('导出完成，文件名：' . $filename);

            return Command::SUCCESS;
        } catch (\Exception $exception) {
            $this->error($exception->getMessage());
            return Command::FAILURE;
        }
    }


}

class PolicyTransactionExceptionExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{
    protected $policies;

    protected $heading = ['用户ID', '平台ID', '用户名', '用户名称', '保单ID', '保单号', '交易流水金额', '保费', '应扣除余额', '出单时间', '可能重复扣款', '是否存在代理同步扣费', '代理ID', '代理流水差额', '代理保费', '应扣除代理余额'];

    public function __construct($policies)
    {
        $this->policies = $policies;
    }

    public function headings(): array
    {
        return $this->heading;
    }

    public function bindValue(Cell $cell, $value)
    {
        // 对于指定的列设置值为字符串类型
        if (in_array($cell->getColumn(), ['A', 'B', 'C', 'D', 'E', 'H'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);
            return true;
        }
        return parent::bindValue($cell, $value);
    }

    public function array(): array
    {
        $items = [];
        foreach ($this->policies as $userId => $userPolicies) {
            $differenceAmount = 0;
            $premiumAmount = 0;
            $user = User::where('id', $userId)->first();
            $policyNos = [];
            foreach ($userPolicies as $policy) {
                $isAgentDeduction = false;
                $isRepeatDeduction = false;
                $agentPayAmount = 0;
                $agentRefundAmount = 0;
                $agentDifference = 0;
                $agentPremiumSum = 0;
                $agentUserIds = [];
                // 计算交易的差额
                $payAmount = $policy->transaction->where('user_id', $userId)->where('type', 2)->sum('amount');
                $refundAmount = $policy->transaction->where('user_id', $userId)->where('type', 3)->sum('amount');

                if($policy->transaction->where('user_id', '!=', $userId)->first()){
                    $isAgentDeduction = true;
                    $agentPayAmount = $policy->transaction->where('user_id', $policy->user->agent_id)->where('type', 2)->sum('amount');
                    $agentRefundAmount = $policy->transaction->where('user_id', $policy->user->agent_id)->where('type', 3)->sum('amount');
                    $agentDifference = $agentPayAmount - $agentRefundAmount;
                    $agentPremiumSum += $policy->agent_premium;
                }

                $difference = $payAmount - $refundAmount;

                $premium = $policy->user->is_agent ? $policy->agent_premium : $policy->user_premium;

                if ($difference != $premium) {

                    if ($policy['status'] == Policy::STATUS_CANCELLED && $difference == 0) {
                        continue;
                    }

                    if ($difference != 0 && $difference % $premium == 0) {
                        $isRepeatDeduction = true;
                    }

                    $differenceAmount += $difference;
                    $premiumAmount += $premium;

                    $policyNos[] = $policy['policy_no'];

                    // 构造每一行的导出数据
                    $items[] = [
                        $userId,                          // 用户ID
                        $user['platform_id'],                          // 用户ID
                        $user['username'],                          // 用户名
                        $user['name'],                          // 用户名称
                        $policy->id,                      // 保单ID
                        $policy->policy_no,               // 保单号
                        readable_amount($difference),                      // 交易流水金额 (差额)
                        readable_amount($premium),            // 用户保费
                        readable_amount($premium - $difference),            // 应扣除余额
                        $policy->issued_at,            // 出单时间
                        $isRepeatDeduction ? '是' : '否',            // 可能重复扣费
                        $isAgentDeduction ? '是' : '否',            // 是否代理扣费
                        $isAgentDeduction ? $policy->user->agent_id : '',            // 代理用户iD
                        $isAgentDeduction ? readable_amount($agentDifference) : '',            // 代理流水差额
                        $isAgentDeduction ? readable_amount($agentPremiumSum) : '',            // 代理保费
                        $isAgentDeduction ? readable_amount($agentPremiumSum - $agentDifference) : '',            // 代理应扣除余额
                    ];
                }

            }


            if ($differenceAmount || $premiumAmount) {
                // 添加总计行
                $items[] = [
                    $userId . '-总计',                            // 用户ID
                    $user['platform_id'],
                    $user['username'],
                    $user['name'],
                    '',
                    '',
                    readable_amount($differenceAmount), // 交易流水金额 (差额)
                    readable_amount($premiumAmount),    // 用户保费
                    readable_amount($premiumAmount - $differenceAmount),    // 应扣除余额
                    '',
                    '',
                    '',
                    '',
                    '',
                    '',
                    implode(',', $policyNos),
                ];
            }
        }

        return $items;
    }
}


class Transaction2 extends Transaction
{
    protected $table = 'transactions_view';
}

class Policy2 extends Policy
{
    protected $table = 'policies';
    public function transaction()
    {
        return $this->hasMany(Transaction2::class, 'policy_no', 'policy_no');
    }
}
