<?php

namespace App\Console\Commands\Watchdog;
use App\Models\User;
use App\Notifications\UserBalanceInsufficient;
use Cache;
use Illuminate\Console\Command;
use Notification;

class CheckBalance extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'watchdog:check-balance';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check balance of all users';

    /**
     * The recipients of the notification.
     *
     * @var array
     */
    protected array $recipients = [
        '<EMAIL>',
        '<EMAIL>'
    ];

    /**
     * 1:保呀 2:运吉保,
     *
     * @var array
     */
    protected array $platforms = [
        1,
        2
    ];

    /**
     * Handle the command.
     *
     * @return void
     */
    public function handle()
    {
        $users = User::whereIn('platform_id', $this->platforms)
            ->where('api_key', '!=', '')
            ->where('is_enabled', true)
            ->whereBetween('balance', [1, 500_00])
            ->whereDate('updated_at', '>=', now()->subDays(30))
            ->get();

        $shouldNotifyUsers = [];
        foreach ($users as $user) {
            if (Cache::has("watchdog:check-balance:$user->id")) {
                return;
            }

            // 一天只发一次
            Cache::put("watchdog:check-balance:$user->id", true, 60 * 60 * 24);

            $shouldNotifyUsers[] = $user;
        }

        if (count($shouldNotifyUsers) > 0) {
            Notification::route('mail', $this->recipients)
                ->notify(new UserBalanceInsufficient($shouldNotifyUsers));
        }
    }
}
