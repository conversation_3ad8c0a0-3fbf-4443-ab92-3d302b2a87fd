<?php

namespace App\Enums;

enum InquiryLoadingMethod: int
{
    case Unknown = 0; // 待定
    case ContainerTransport = 1; // 集装箱运输
    case NonContainerTransport = 2; // 非集装箱运输
    case BoxTruck = 3; // 厢式货车
    case NonBoxTruck = 4; // 非厢式货车

    /**
     * Get the text representation of the enum value.
     *
     * @return string
     */
    public function text(): string
    {
        return match ($this) {
            self::Unknown => '待定',
            self::ContainerTransport => '集装箱运输',
            self::NonContainerTransport => '非集装箱运输',
            self::BoxTruck => '厢式货车',
            self::NonBoxTruck => '非厢式货车',
        };
    }
}
