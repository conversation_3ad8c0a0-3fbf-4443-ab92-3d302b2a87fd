<?php

namespace App\Enums;

enum InquiryReason: int
{
    case PrecisionInstruments = 1; // 精密仪器

    case Vehicles = 2; // 车辆、摩托车

    case OverLimit = 3; // 超限额

    case BlukCarrier = 4; // 散货船

    case SpecialRegion = 5; // 特殊区域

    case Offline = 7; // 线下出单

    case AirReefer = 8; // 空运冷藏

    case LithiumBattery = 9; // 锂电池

    case SpecialContainer = 10; // 特种集装箱

    case Other = 99; // 其他

    /**
     * Get the text representation of the enum value.
     *
     * @return string
     */
    public function text(): string
    {
        return match ($this) {
            self::PrecisionInstruments => '精密仪器',
            self::Vehicles => '车辆、摩托车',
            self::OverLimit => '超限额',
            self::BlukCarrier => '散货船',
            self::SpecialRegion => '特殊区域',
            self::Offline => '线下出单',
            self::AirReefer => '空运冷藏',
            self::LithiumBattery => '锂电池',
            self::SpecialContainer => '特种集装箱',
            self::Other => '其他',
        };
    }
}
