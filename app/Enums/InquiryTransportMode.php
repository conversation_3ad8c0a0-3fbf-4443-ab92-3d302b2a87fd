<?php

namespace App\Enums;

enum InquiryTransportMode: int
{
    case Air = 1; // 空运

    case Sea = 2; // 水运

    case Land = 3; // 陆运

    /**
     * Get the text representation of the enum value.
     *
     * @return string
     */
    public function text(): string
    {
        return match ($this) {
            self::Air => '空运',
            self::Sea => '水运',
            self::Land => '陆运',
        };
    }
}
