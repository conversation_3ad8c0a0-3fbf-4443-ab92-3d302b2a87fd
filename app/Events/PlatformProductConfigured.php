<?php

namespace App\Events;

use App\Models\Platform;
use App\Models\PlatformProduct;
use App\Models\Product;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PlatformProductConfigured
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The affected platform id.
     *
     * @var int
     */
    public int $platformId;

    /**
     * The affect Product.
     *
     * @var Product
     */
    public Product $product;

    /**
     * The affected platform product.
     *
     * @var PlatformProduct
     */
    public PlatformProduct $platformProduct;

    /**
     * 被禁用的产品.
     *
     * @var array
     */
    public array $disabledProductUsers;

    /**
     * Create a new event instance.
     *
     * @param  int  $platformId
     * @param  Product  $product
     * @param  PlatformProduct  $platformProduct
     * @param  array  $disabledProductUsers
     *
     * @return void
     */
    public function __construct(int $platformId, Product $product, PlatformProduct $platformProduct, array $disabledProductUsers)
    {
        $this->platformId = $platformId;
        $this->product = $product;
        $this->platformProduct = $platformProduct;
        $this->disabledProductUsers = $disabledProductUsers;
    }

    /**
     * 获取平台信息.
     *
     * @return  \App\Models\Platform
     */
    public function getPlatform()
    {
        return Platform::find($this->platformId);
    }

    /**
     * 获取产品信息.
     *
     * @return  Product
     */
    public function getProduct()
    {
        return $this->product;
    }

    /**
     * 获取受影响的平台产品配置信息.
     *
     * @return  PlatformProduct
     */
    public function getPlatformProduct()
    {
        return $this->platformProduct;
    }

    /**
     * 获取被禁用的产品.
     *
     * @return  array
     */
    public function getDisableProductUsers()
    {
        return $this->disabledProductUsers;
    }
}
