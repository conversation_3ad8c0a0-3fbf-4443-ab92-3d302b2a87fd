<?php

namespace App\Events;

use App\Models\Policy;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PolicyFailed
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The policy instance.
     *
     * @var Policy
     */
    public $policy;

    /**
     * The policy failure message.
     *
     * @var string
     */
    public $message;

    /**
     * Create a new event instance.
     *
     * @param  Policy  $policy
     * @param  string  $message
     *
     * @return void
     */
    public function __construct(Policy $policy, ?string $message = null)
    {
        $this->policy = $policy;
        $this->message = $message;
    }
}
