<?php

namespace App\Events;

use App\Models\Policy;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PolicyGroupApproved
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The policy instance.
     *
     * @var Policy
     */
    public $policy;

    /**
     * Create a new event instance.
     *
     * @param  Policy  $policy
     *
     * @return void
     */
    public function __construct(Policy $policy)
    {
        $this->policy = $policy;
    }
}
