<?php

namespace App\Events;

use App\Models\Policy;
use App\Models\PolicyGroupEndorse;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PolicyGroupEndorseApproved
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The policy instance.
     *
     * @var Policy
     */
    public $policy;

    /**
     * The policy group endorse instance.
     *
     * @var PolicyGroupEndorse
     */
    public $policyGroupEndorse;

    /**
     * Create a new event instance.
     *
     * @param  Policy  $policy
     *
     * @return void
     */
    public function __construct(Policy $policy, PolicyGroupEndorse $policyGroupEndorse)
    {
        $this->policy = $policy;
        $this->policyGroupEndorse = $policyGroupEndorse;
    }
}
