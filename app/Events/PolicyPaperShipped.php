<?php

namespace App\Events;

use App\Models\Policy;
use App\Models\PolicyPaper;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PolicyPaperShipped
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The policy instance.
     *
     * @var Policy
     */
    public $policy;

    /**
     * The policy paper instance.
     *
     * @var PolicyPaper
     */
    public $policyPaper;

    /**
     * Create a new event instance.
     *
     * @param  Policy  $policy
     * @param  PolicyPaper  $policyPaper
     *
     * @return void
     */
    public function __construct(Policy $policy, PolicyPaper $policyPaper)
    {
        $this->policy = $policy;
        $this->policyPaper = $policyPaper;
    }
}
