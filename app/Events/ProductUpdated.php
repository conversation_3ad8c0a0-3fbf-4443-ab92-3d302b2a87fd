<?php

namespace App\Events;

use App\Models\Product;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ProductUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * The originally product instance.
     *
     * @var  Product
     */
    public Product $originally;

    /**
     * The old product instance.
     *
     * @var  Product
     */
    public Product $current;

    /**
     * Create a new event instance.
     *
     * @param  Product  $originally
     * @param  Product  $current
     *
     * @return void
     */
    public function __construct(Product $originally, Product $current)
    {
        $this->originally = $originally;
        $this->current = $current;
    }
}
