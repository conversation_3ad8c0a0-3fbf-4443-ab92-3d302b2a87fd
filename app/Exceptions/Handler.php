<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Response;
use Throwable;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        BusinessException::class,
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            if ($this->isSymfonyMailerIgnorableException($e->getMessage())) {
                return;
            }

            if (app()->bound('captureException') && app()->environment('production')) {
                app('captureException')->capture($e);
            }
        });
    }

    /**
     * Determine if the exception is an instance of Symfony TransportException
     * and the message isn't important. If so, we'll ignore it.
     *
     * @param   string  $message
     *
     * @return  bool
     */
    protected function isSymfonyMailerIgnorableException(string $message): bool
    {
        return str_contains($message, 'Unable to write bytes on the wire')
            || str_contains($message, 'Expected response code "250/251/252" but got code "500", with message "500 Error: bad syntax".')
            || str_contains($message, 'Expected response code "250/251/252" but got code "554"')
            || str_contains($message, 'Transient reject by behaviour spam')
            || str_contains($message, 'does not comply with addr-spec of RFC 2822');
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param Throwable $exception
     * @return \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\Response
     * @throws Throwable
     */
    public function render($request, Throwable $exception)
    {
        if ($request->is('api/*')) {
            $result = [
                'error' => $exception->getMessage(),
                'data' => [],
                'message' => null
            ];

            if (config('app.debug')) {
                $result['trace'] = $exception->getTrace();
            }

            if ($exception instanceof \Illuminate\Database\Eloquent\ModelNotFoundException) {
                return response()->noContent(Response::HTTP_NOT_FOUND);
            }

            if ($exception instanceof \Illuminate\Validation\ValidationException) {
                $errors = [];
                foreach ($exception->errors() as $key => $value) {
                    $errors[$key] = $value[0];
                }
                $result['message'] = $errors;

                return response()->json($result, Response::HTTP_UNPROCESSABLE_ENTITY);
            }
        }

        return parent::render($request, $exception);
    }
}
