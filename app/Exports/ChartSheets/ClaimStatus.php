<?php

namespace App\Exports\ChartSheets;

use Maatwebsite\Excel\Concerns\WithCharts;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Chart\Chart;
use PhpOffice\PhpSpreadsheet\Chart\DataSeries;
use PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues;
use PhpOffice\PhpSpreadsheet\Chart\Legend;
use PhpOffice\PhpSpreadsheet\Chart\PlotArea;
use PhpOffice\PhpSpreadsheet\Chart\Title;

class ClaimStatus extends ChartSheet implements WithCharts, WithHeadings
{
    /**
     * @return string
     */
    public function title(): string
    {
        return '保司案件进展分布情况';
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            '保险',
            '已报案',
            '已受理',
            '撤案',
            '材料收集',
            '保司审核',
            '零赔付',
            '拒赔',
            '领赔款',
            '结案',
            '理赔案件总量'
        ];
    }

    /**
     * 图表数据.
     *
     * @return mixed
     */
    public function charts(): mixed
    {
        $categories = [
            new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, $this->title() . '!$B$1:$K$1', null, 12),
        ];

        $label = [];
        $values = [];
        $dataSize = count($this->array()) + 1;
        if ($dataSize <= 1) {
            return [];
        }
        for ($i = 2; $i <= $dataSize; $i++) {
            $label[] = new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, $this->title() . '!$A$' . $i, null, 1);
            $values[] = new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_NUMBER, $this->title() . "!\$B\$$i:\$K\$$i", null, 12);
        }

        $series = new DataSeries(
            DataSeries::TYPE_BARCHART,
            DataSeries::GROUPING_STACKED,
            range(0, count($values) - 1),
            $label,
            $categories,
            $values
        );

        $chart = new Chart($this->title(), new Title($this->title()), new Legend(Legend::POSITION_BOTTOM), new PlotArea(null, [$series]));
        $position = $dataSize + 2;
        $chart->setTopLeftPosition('A' . $position);
        $chart->setBottomRightPosition('L' . ($position + 20));

        return $chart;
    }


    /**
     * 表格数据.
     *
     * @return array
     */
    public function array(): array
    {
        $data = [];
        foreach ($this->data['for_chart'] as $item) {
            $data[] = array_merge([$item['name']], array_values($item['dataset']));
        }

        return $data;
    }
}
