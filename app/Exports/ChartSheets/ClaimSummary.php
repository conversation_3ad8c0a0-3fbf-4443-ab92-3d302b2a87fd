<?php

namespace App\Exports\ChartSheets;

class ClaimSummary extends ChartSheet
{
    /**
     * @return string
     */
    public function title(): string
    {
        return '理赔数据';
    }

    /**
     * 表格数据.
     *
     * @return array
     */
    public function array(): array
    {
        return [
            ['出险报案（件）', $this->data['total_cases'], '未决案件（件）', $this->data['pending_cases']],
            ['已决案件（件）', $this->data['finished_cases'], '出险率', $this->data['rate'] . '‰'],
            [
                '出险率相对值',
                $this->data['rate_relative_value'],
                '结案率',
                $this->data['total_cases'] > 0 ? round(($this->data['finished_cases'] / $this->data['total_cases']) * 100, 2) : '0.00' . '%'
            ],
            ['5万元以上理赔案件（件）', $this->data['amount_over_five_hundred_thousand'], '万元以下理赔案件（件）', $this->data['amount_less_ten_thousand']],
            ['已决金额（元）', $this->data['finished_amount'], '结案赔款（元）', $this->data['finished_payment_amount']],
            ['结案费用（元）', $this->data['finished_fee'], '未决金额（元）', $this->data['pending_amount']],
            ['未决赔款（元）', $this->data['pending_settlement_amount'], '未决费用（元）', $this->data['pending_lodging_fee']],
            ['简单赔付率', $this->data['simple_rate'] . '%', '赔付率相对值', $this->data['claim_rate_relative_value']],
            ['迟报案（件）', $this->data['delayed_cases'], '理赔时长(天）', $this->data['cost_days']],
        ];
    }
}
