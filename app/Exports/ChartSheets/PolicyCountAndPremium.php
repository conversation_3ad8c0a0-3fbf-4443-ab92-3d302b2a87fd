<?php

namespace App\Exports\ChartSheets;

use Maatwebsite\Excel\Concerns\WithCharts;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Chart\Chart;
use PhpOffice\PhpSpreadsheet\Chart\DataSeries;
use PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues;
use PhpOffice\PhpSpreadsheet\Chart\Legend;
use PhpOffice\PhpSpreadsheet\Chart\PlotArea;
use PhpOffice\PhpSpreadsheet\Chart\Title;

class PolicyCountAndPremium extends ChartSheet implements WithCharts, WithHeadings
{
    /**
     * @return string
     */
    public function title(): string
    {
        return '保费、投保单数按时间分布情况';
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            '年度',
            '1月',
            '2月',
            '3月',
            '4月',
            '5月',
            '6月',
            '7月',
            '8月',
            '9月',
            '10月',
            '11月',
            '12月'
        ];
    }

    /**
     * 图表数据.
     *
     * @return mixed
     */
    public function charts(): mixed
    {
        return [
            $this->premiumChart(),
            $this->countChart(),
        ];
    }

    /**
     * 投保单数统计图
     *
     * @return \PhpOffice\PhpSpreadsheet\Chart\Chart
     */
    protected function countChart(): Chart
    {
        $lastChartBegin = count($this->data['premium_for_chart']) + 2;
        $series = $this->dataSeries($lastChartBegin, $lastChartBegin + count($this->data['count_for_chart']) - 1);

        $chart = new Chart('按投保单分布图', new Title('按投保单分布图'), new Legend(Legend::POSITION_BOTTOM), new PlotArea(null, [$series]));

        $position = count($this->data['count_for_chart']) * 3 + 22;

        $chart->setTopLeftPosition('A' . $position);
        $chart->setBottomRightPosition('N' . ($position + 20));

        return $chart;
    }

    /**
     * 保费统计图
     *
     * @return \PhpOffice\PhpSpreadsheet\Chart\Chart
     */
    protected function premiumChart(): Chart
    {
        $series = $this->dataSeries(2, count($this->data['premium_for_chart']) + 1);

        $chart = new Chart('保费按时间分布图', new Title('保费按时间分布图'), new Legend(Legend::POSITION_BOTTOM), new PlotArea(null, [$series]));

        $position = count($this->data['count_for_chart']) * 2 + 2;
        $chart->setTopLeftPosition('A' . $position);
        $chart->setBottomRightPosition('N' . ($position + 20));

        return $chart;
    }

    /**
     * 数据系列.
     *
     * @param  int $begin
     * @param  int $end
     *
     * @return \PhpOffice\PhpSpreadsheet\Chart\DataSeries
     */
    protected function dataSeries(int $begin, int $end): DataSeries
    {
        $categories = [
            new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, $this->title() . '!$B$1:$M$1', null, 12),
        ];

        $label = [];
        $values = [];
        for ($i = $begin; $i <= $end; $i++) {
            $label[] = new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, $this->title() . '!$A$' . $i, null, 1);
            $values[] = new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_NUMBER, $this->title() . "!\$B\$$i:\$M\$$i", null, 12);
        }

        return new DataSeries(
            DataSeries::TYPE_BARCHART,
            DataSeries::GROUPING_STANDARD,
            range(0, count($values) - 1),
            $label,
            $categories,
            $values
        );
    }

    /**
     * 表格数据.
     *
     * @return array
     */
    public function array(): array
    {
        $data = [];
        foreach ($this->data['premium_for_chart'] as $item) {
            $data[] = array_merge([$item['year'] . '保费'], array_values($item['dataset']));
        }

        foreach ($this->data['count_for_chart'] as $item) {
            $data[] = array_merge([$item['year'] . '投保单数'], array_values($item['dataset']));
        }

        return $data;
    }
}
