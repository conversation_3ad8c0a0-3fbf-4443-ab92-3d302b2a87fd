<?php

namespace App\Exports\ChartSheets;

class PolicySummary extends ChartSheet
{
    /**
     * @return string
     */
    public function title(): string
    {
        return '承保数据';
    }

    /**
     * 表格数据.
     *
     * @return array
     */
    public function array(): array
    {
        return [
            [
                '有效客户数',
                $this->data['customer_count'],
                '客户年化增长率',
                $this->data['customer_increase_rate'] . '%'
            ],
            [
                '月均保费0.5万元以上客户数',
                $this->data['monthly_premium_over_five_thousand'],
                '年均保费万元以上客户数',
                $this->data['yearly_premium_avg_over_ten_thousand']
            ],
            [
                '年均保费10万元以上客户数',
                $this->data['yearly_premium_avg_over_one_hundred_thousand'],
                '投保人（数）',
                $this->data['policyholder_count']
            ],
            ['被保险人（数）', $this->data['insured_count'], '保险公司（数）', $this->data['company_count']],
            ['出单公司（数）', $this->data['company_branch_count'], '累计保额（万元）', $this->data['total_coverage'] / 10000],
            ['累计保费（元）', $this->data['total_premium'], '保费年化增长率', $this->data['yearly_premium_increase_rate'] . '%'],
            ['费率均值', $this->data['avg_rate'] . '‱', '费率均值年化增长率', $this->data['yearly_rate_increase_rate'] . '%'],
            ['投保单数', $this->data['count'], '投保单年化增长率', $this->data['yearly_policy_increase_rate'] . '%'],
            ['自助出单单数', $this->data['automated_policy_count'], '人工出单单数', $this->data['count'] - $this->data['automated_policy_count']]
        ];
    }
}
