<?php

namespace App\Exports\ChartSheets;

use Maatwebsite\Excel\Concerns\WithCharts;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Chart\Chart;
use PhpOffice\PhpSpreadsheet\Chart\DataSeries;
use PhpOffice\PhpSpreadsheet\Chart\DataSeriesValues;
use PhpOffice\PhpSpreadsheet\Chart\PlotArea;
use PhpOffice\PhpSpreadsheet\Chart\Title;

class Summary extends ChartSheet implements WithCharts, WithHeadings
{
    /**
     * @return string
     */
    public function title(): string
    {
        return '整体保费及赔付情况';
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            '累计保费（元）',
            '已决金额（元）',
            '未决金额（元）',
            '简单赔付率（%）',
        ];
    }

    /**
     * 图表数据.
     *
     * @return mixed
     */
    public function charts(): mixed
    {
        $categories = [
            new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_STRING, $this->title() . '!$A$1:$D$1', null, 4)
        ];
        $values = [
            new DataSeriesValues(DataSeriesValues::DATASERIES_TYPE_NUMBER, $this->title() . '!$A$2:$D$2', null, 4),
        ];
        $series = new DataSeries(
            DataSeries::TYPE_BARCHART,
            DataSeries::GROUPING_STANDARD,
            range(0, count($values) - 1),
            [],
            $categories,
            $values
        );

        $chart = new Chart($this->title(), new Title($this->title()), null, new PlotArea(null, [$series]));
        $chart->setTopLeftPosition('A3');
        $chart->setBottomRightPosition('E20');
        return $chart;
    }

    /**
     * 表格数据.
     *
     * @return array
     */
    public function array(): array
    {
        return [
            [
                $this->data['premium'],
                $this->data['finished_claim_amount'],
                $this->data['pending_claim_amount'],
                $this->data['simple_claim_rate'],
            ]
        ];
    }
}
