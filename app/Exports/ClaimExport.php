<?php

namespace App\Exports;

use App\Models\Claim;
use App\Models\Policy;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class ClaimExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{

    protected $heading = [
        '序号',
        '被保人',
        '投保人',
        '投保用户',
        '理赔编号',
        '保司报案号',
        '保险公司',
        '出单公司',
        '理赔员',
        '理赔员手机号',
        '保单号',
        '理赔进度',
        '标的',
        '标的类型',
        '险种',
        '买方',
        '卖方',
        '货物类别',
        '保险金额',
        '保险金额币种',
        '发票金额',
        '发票金额币种',
        '产品来源',
        '业务来源',
        '业务员',
        '报案人',
        '电话',
        '电子邮箱',
        '提交时间',
        '提/运单号',
        '包装方式',
        '装载方式',
        '装箱公司',
        '承运人',
        '运输公司',
        '搬运公司',
        '贸易类型',
        '起运国家',
        '起运地',
        '目的国家',
        '目的地',
        '运输方式',
        '运输工具',
        '起运日期',
        '投保时间',
        '出险时间',
        '报案时间',
        '出险地点',
        '事故经过',
        '出险原因',
        '报损金额',
        '立案赔款',
        '立案费用',
        '立案金额',
        '结案赔款',
        '结案费用',
        '结案金额',
//        '币种(CNY)',
        '结案时间',
        '是否有第三方责任',
        '追偿对象',
        '追偿金额',
        '风险提示',
        '残值处理',
        '残值金额',
        '是否典型案例',
        '理算报告',
    ];

    /**
     * FinanceMixedExport constructor.
     *
     *
     * @param \Illuminate\Database\Eloquent\Builder $queryBuilder
     */
    public function __construct($queryBuilder)
    {
        $this->queryBuilder = $queryBuilder;
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return $this->heading;
    }

    /**
     * 设置列单元格格式
     *
     * @param Cell $cell
     * @param mixed $value
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A', 'D', 'E', 'H', 'I', 'J', 'N', 'O', 'R', 'U', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AR'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        return parent::bindValue($cell, $value);
    }

    public function array(): array
    {
        $items = [];
        $this->queryBuilder->chunkById(500, function ($datas) use (&$items) {
            foreach ($datas as $data) {
                $items[] = [
                    $data['id'],  //序号
                    $data['insured'],    //被保人
                    $data['policyholder'],    //投保人
                    $data['policy']['user']['name'] ?? '',    //投保用户
                    $data['case_no'],    //理赔编号
                    $data['external_case_no'],    //保司报案号
                    $data['company']['name'] ?? '',    //保险公司
                    $data['companyBranch']['name'] ?? '',    //出单公司
                    $data['external_adjuster'],    //理赔员
                    $data['external_adjuster_phone_number'],    //理赔员手机号
                    $data['policy_no'],    //保单号
                    Arr::last(explode('->', Claim::$statusText[$data['status']])),    //理赔进度
                    $data['subject'],    //标的
                    $data['policy']['policyCargo']['subject']['name'] ?? '',    //标的类型
                    Policy::$types[$data['policy_type']],    //险种
                    $data['buyer'],    //买方
                    $data['seller'],    //卖方
                    $data['goodsType']['name'] ?? '',    //货物类别
                    readable_amount($data['policy_coverage']),    //保险金额
                    $data['policyCoverageCurrency']['name'],    //保险金额币种
                    readable_amount($data['invoice_amount']),    //发票金额
                    $data['invoiceAmountCurrency']['name'],    //发票金额币种
                    $data['policy']['platform']['name'] ?? '-',    //产品来源
                    $data['user']['platform']['name'] ?? '-',    //业务来源
                    $data['salesman']['name'],    //业务员
                    $data['claimant'],    //报案人
                    $data['claimant_phone_number'],    //电话
                    $data['claimant_email'],    //电子邮箱
                    $data['created_at'],    //提交时间
                    $data['waybill_no'],    //提/运单号
                    Claim::$packagingMethods[$data['packaging_method']] ?? '',    //包装方式
                    Claim::$loadingMethods[$data['loading_method']] ?? '',    //装载方式
                    $data['packing_company'],    //装箱公司
                    $data['carrier'],    //承运人
                    $data['transportation_company'],    //运输公司
                    $data['moving_company'],    //搬运公司
                    Claim::$tradeTypes[$data['trade_type']],    //贸易类型
                    $data['departure'],    //起运国家
                    $data['departure'],    //起运地
                    $data['destination'],    //目的国家
                    $data['destination'],    //目的地
                    $data['transportMethod']['name'] ?? '',    //运输方式
                    $data['transport_no'],    //运输工具
                    $data['shipping_date'],    //起运日期
                    $data['policy']['submitted_at'] ?? '',    //投保时间
                    $data['date_of_loss'],    //出险时间
                    $data['date_of_reporting'],    //报案时间
                    $data['loss_location'],    //出险地点
                    $data['loss_detail'],    //事故经过
                    $data['loss_reason'],    //出险原因
                    $this->getCNYAmount($data['loss_amount'], optional($data['lossAmountCurrency'])->rate ?? 1),    //报损金额
                    $this->getCNYAmount($data['claim_settlement_amount'], optional($data['claimSettlementAmountCurrency'])->rate ?? 1),    //立案赔款
                    $this->getCNYAmount($data['claim_lodging_fee'], optional($data['claimLodgingFeeCurrency'])->rate ?? 1),    //立案费用
                    $this->claimRecordAmount($data),    //立案金额
                    $this->getCNYAmount($data['settlement_payment_amount'], optional($data['settlementPaymentAmountCurrency'])->rate ?? 1),    //结案赔款
                    $this->getCNYAmount($data['settlement_costs'], optional($data['settlementCostsCurrency'])->rate ?? 1),    //结案费用
                    $this->claimSettleAmount($data),    //结案金额
//                    $data['settlementPaymentAmountCurrency']['name'],    //币种(CNY)
                    $data['settlement_date'],    //结案时间
                    $data['is_third_party_liability'] ? '是' : '否',    //是否有第三方责任
                    $data['recovery_target'],    //追偿对象
                    readable_amount($data['estimated_recovery_amount']),    //追偿预估金额
                    $data['has_risk_warning'],    //风险提示
                    [-1 => '无残值',
                    1 => '报废处理',
                    2 => '抵扣被保险人赔款',
                    3 => '第三方残值回收'][$data['residual_value_handing_method']],    //残值处理
                    readable_amount($data['residual_value']),    //残值金额
                    $data['is_typical_case'] ? '是' : '否',    //是否典型案例
                    $data['claims_assessment_report'],    //理算报告
                ];
            }
        });

        return $items;
    }

    protected function getCNYAmount($amount, $rate)
    {
        return readable_amount(bcmul($amount, $rate, 5));
    }

    /**
     * 立案金额
     *
     * @param $data
     * @return float
     */
    protected function claimRecordAmount($data)
    {
        return $this->getCNYAmount($data['claim_settlement_amount'], optional($data['claimSettlementAmountCurrency'])->rate ?? 1) + $this->getCNYAmount($data['claim_lodging_fee'], optional($data['claimLodgingFeeCurrency'])->rate ?? 1);
    }

    /**
     * 结案金额
     *
     * @param $data
     * @return float
     */
    protected function claimSettleAmount($data)
    {
        return $this->getCNYAmount($data['settlement_payment_amount'], optional($data['settlementPaymentAmountCurrency'])->rate ?? 1) + $this->getCNYAmount($data['settlement_costs'], optional($data['settlementCostsCurrency'])->rate ?? 1);
    }
}
