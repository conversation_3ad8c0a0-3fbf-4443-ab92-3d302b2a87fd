<?php

namespace App\Exports;

use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class DataChartExport implements WithMultipleSheets
{
    use Exportable;

    /**
     * 需要导出的 sheet
     *
     * @var array
     */
    protected array $sheets = [
        'summary' => ChartSheets\Summary::class,
        'policy_summary' => ChartSheets\PolicySummary::class,
        'claim_summary' => ChartSheets\ClaimSummary::class,
        'policy_count_and_premium' => ChartSheets\PolicyCountAndPremium::class,
        'monthly_claim' => ChartSheets\MonthlyClaim::class,
        'monthly_finished_claim' => ChartSheets\MonthlyFinishedClaim::class,
        'monthly_claim_payment_amount' => ChartSheets\MonthlyClaimPaymentAmount::class,
        'claim_status' => ChartSheets\ClaimStatus::class,
        'claim_time_duration' => ChartSheets\ClaimTimeDuration::class,
    ];

    public function __construct(
        protected array $includeSheets = [],
        protected array $data
    ) {
    }

    /**
     * 排行数据 sheet
     *
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        foreach (Arr::only($this->sheets, $this->includeSheets) as $key => $sheet) {
            $sheets[] = new $sheet($this->data[$key]);
        }

        return $sheets;
    }
}
