<?php

namespace App\Exports;

use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class DataRankingExport implements WithMultipleSheets
{
    use Exportable;

    /**
     * 需要导出的 sheet
     *
     * @var array
     */
    protected array $sheets = [
        "by_user" => RankingSheets\User::class,
        "by_premium" => RankingSheets\Premium::class,
        "by_policy_count" => RankingSheets\PolicyCount::class,
        "by_rate_avg" => RankingSheets\RateAvg::class,
        "by_claim_payment_rate" => RankingSheets\ClaimPaymentRate::class,
        "by_claim_rate" => RankingSheets\ClaimRate::class,
        "by_claim_payment_amount" => RankingSheets\ClaimPaymentAmount::class,
        "by_five_ten_thousand_claim_amount_rate" => RankingSheets\FiveTenThousandClaimAmountRate::class,
        "by_claim_finished_rate" => RankingSheets\ClaimFinishedRate::class,
    ];

    public function __construct(
        protected array $includeSheets = [],
        protected string $label,
        protected string $userLabel,
        protected array $data
    ) {
    }

    /**
     * 排行数据 sheet
     *
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];
        foreach (Arr::only($this->sheets, $this->includeSheets) as $key => $sheet) {
            $sheets[] = new $sheet(
                $sheet === RankingSheets\User::class ? $this->userLabel : $this->label,
                $this->data[$key] ?? []
            );
        }

        return $sheets;
    }
}
