<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class DicMailPolicyExport implements FromArray, WithHeadings
{

    protected $policies;

    protected $heading = ['投保人', '被保人', '起运地', '目的地', '运单号', '货物名称', '公斤数', '立方数', '件数', '承运公司', '车牌号', '投保时间'];

    /**
     * 东海邮件保单导出实例
     *
     * @param $policies
     */
    public function __construct($policies)
    {
        $this->policies = $policies;

    }


    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return $this->heading ?? [];
    }

    /**
     * Register events.
     *
     * @return  array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }


    /**
     *
     *
     * @return array
     */
    public function array(): array
    {
        return $this->fields();
    }

    protected function fields()
    {
        $items = [];
        foreach ($this->policies as $policy){
            //公斤数-立方数-件数  使用中横线隔开
            $goodsAmount = explode('-', $policy['policyCargo']['goods_amount']);
            //承运公司-车牌号  使用中横线隔开
            $transportNo = explode('-', $policy['policyCargo']['transport_no']);
            $items[] = [
                $policy['policyholder'],
                $policy['insured'],
                $policy['policyCargo']['departure'],
                $policy['policyCargo']['destination'],
                $policy['policyCargo']['waybill_no'],
                $policy['policyCargo']['goods_name'],
                $goodsAmount[0] ?? '', // 公斤数
                $goodsAmount[1] ?? '', // 立方数
                $goodsAmount[2] ?? '', // 件数
                $transportNo[0] ?? '', // 承运公司
                $transportNo[1] ?? '', // 车牌号
                $policy['submitted_at'],
            ];
        }
        return $items;
    }
}
