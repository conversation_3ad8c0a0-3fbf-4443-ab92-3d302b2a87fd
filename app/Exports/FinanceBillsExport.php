<?php

namespace App\Exports;

use App\Models\CommissionPaymentBill;
use App\Models\CommissionReceivableBill;
use App\Models\PoundagePaymentBill;
use App\Models\PremiumPaymentBill;
use App\Models\PremiumReceivableBill;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class FinanceBillsExport implements FromArray, WithHeadings
{
    /**
     * 支付记录数据
     *
     * @var
     */
    protected $bills;

    /**
     * 表头
     *
     * @var array
     */
    protected $headings = [
        PremiumReceivableBill::class => ['流水号', '出单公司', '应收保费', '实际收取', '处理人', '收取时间'],
        PremiumPaymentBill::class => ['流水号', '出单公司', '保险公司保费', '实际支付', '处理人', '支付时间'],
        PoundagePaymentBill::class => ['流水号', '出单公司', '保费总数', '经纪费应付', '经纪费实际支付', '处理人', '支付时间'],
        CommissionReceivableBill::class => ['流水号', '出单公司', '保费总数', '佣金应收', '佣金实际收取', '处理人', '收取时间'],
        CommissionPaymentBill::class => ['流水号', '出单公司', '代理人', '保费总数', '佣金应发', '佣金实际发放', '处理人', '支付时间'],
    ];

    /**
     * PremiumPaymentBillExport constructor.
     * @param $bills
     */
    public function __construct($bills)
    {
        $this->bills = $bills;
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return $this->headings[get_class($this->bills[0])];
    }

    public function array(): array
    {
        switch (get_class($this->bills[0])){
            case PremiumReceivableBill::class:
                return $this->premiumReceivableBillsFields();
            case PremiumPaymentBill::class:
                return $this->premiumPaymentBillFields();
            case PoundagePaymentBill::class:
                return $this->poundageBillFields();
            case CommissionReceivableBill::class:
                return $this->commissionReceivableBillFields();
            case CommissionPaymentBill::class:
                return $this->commissionPaymentBillFields();
            default:
                return [];
        }
    }

    /**
     * 保费应收处理记录导出字段
     */
    public function premiumReceivableBillsFields()
    {
        $items = [];
        foreach ($this->bills as $bill){
            $items[] = [
                $bill['order_no'],
                $bill['companyBranch']['name'],
                readable_amount($bill['receivable']),
                readable_amount($bill['actual_receivable']),
                $bill['operation']['name'],
                $bill['charge_at'],
            ];
        }

        return $items;
    }

    /**
     * 保费应付支付记录导出字段
     */
    public function premiumPaymentBillFields()
    {
        $items = [];
        foreach ($this->bills as $bill){
            $items[] = [
                $bill['order_no'],
                $bill['companyBranch']['name'],
                readable_amount($bill['premium']),
                readable_amount($bill['actual_premium']),
                $bill['operation']['name'],
                $bill['paid_at'],
            ];
        }

        return $items;
    }

    /**
     * 经纪费结算支付记录导出字段
     */
    protected function poundageBillFields()
    {
        $items = [];
        foreach ($this->bills as $bill){
            $items[] = [
                $bill['order_no'],
                $bill['companyBranch']['name'],
                readable_amount($bill['premium']),
                readable_amount($bill['poundage']),
                readable_amount($bill['actual_poundage']),
                $bill['operation']['name'],
                $bill['paid_at'],
            ];
        }

        return $items;
    }

    /**
     * 佣金发放支付记录导出字段
     */
    protected function commissionReceivableBillFields()
    {
        $items = [];
        foreach ($this->bills as $bill){
            $items[] = [
                $bill['order_no'],
                $bill['companyBranch']['name'],
                readable_amount($bill['premium']),
                readable_amount($bill['commission']),
                readable_amount($bill['actual_commission']),
                $bill['operation']['name'],
                $bill['charge_at'],
            ];
        }

        return $items;
    }

    /**
     * 佣金发放支付记录导出字段
     */
    protected function commissionPaymentBillFields()
    {
        $items = [];
        foreach ($this->bills as $bill){
            $items[] = [
                $bill['order_no'],
                $bill['companyBranch']['name'],
                $bill['agent']['name'],
                readable_amount($bill['premium']),
                readable_amount($bill['commission']),
                readable_amount($bill['actual_commission']),
                $bill['operation']['name'],
                $bill['paid_at'],
            ];
        }

        return $items;
    }
}
