<?php

namespace App\Exports;

use App\Models\Policy;
use App\Models\PolicyCargo;
use App\Models\PolicyOffline;
use App\Models\PremiumReceivableBill;
use App\Models\Settlement;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class FinanceMixedExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{
    /**
     * 数据 Builder.
     *
     * @var \Illuminate\Database\Eloquent\Builder
     */
    protected $queryBuilder;
    
    protected $admin;

    /**
     * 表头
     *
     * @var array<string>
     */
    protected $heading = [
        '流水号',
        '保单号',
        '险种',
        '贸易类型',
        '标的',
        '出单状态',
        '出单日期',
        '投保日期',
        '保险公司',
        '出单公司',
        '用户名',
        '投保用户',
        '投保人',
        '代理人',
        '业务员',
        '业务来源',
        '发票申请时间',
        '保费销账时间',
        '币种',
        '保险金额',
        '成本费率',
        '成本保费',
        '平台费率',
        '平台保费',
        '代理费率',
        '代理保费',
        '用户费率',
        '用户保费',
        '经纪费比例',
        '经纪费',
        '平台佣金比例',
        '平台佣金',
        '代理佣金比例',
        '代理佣金',
        '保费是否已付保险公司',
        '代理佣金是否已发',
        '经纪费是否已发',
        '保费是否同步',
        '渠道',
    ];

    /**
     * FinanceMixedExport constructor.
     *
     *
     * @param \Illuminate\Database\Eloquent\Builder $queryBuilder
     */
    public function __construct($queryBuilder, $admin)
    {
        $this->queryBuilder = $queryBuilder;
        $this->admin = $admin;
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return $this->heading;
    }

    /**
     * 设置列单元格格式
     *
     * @param Cell $cell
     * @param mixed $value
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A', 'B'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        return parent::bindValue($cell, $value);
    }

    public function array(): array
    {
        $items = [];
        $loggedUser = $this->admin;
        // $loggedUser = $loggedUser->load('platform');
        $this->queryBuilder->chunkById(500, function ($datas) use (&$items, $loggedUser) {
            foreach ($datas as $data) {
                $isSelfPlatform = $data['policy']['platform_id'] === $loggedUser['platform_id'];
                $isOffline = $data['policy']['type'] === Policy::TYPE_OFFLINE;
                $isPremiumSync = $data['policy']['is_premium_sync'] === 1;
                if ($data['policy']['platform_id'] !== $data['policy']['user']['platform_id']) {
                    if ($data['policy']['platform_id'] === $loggedUser['platform_id']) {
                        // 其他平台用户投保己方平台产品
                        $items[] = [
                            $data['policy']['order_no'], //流水号
                            $data['policy_no'], //保单号
                            $isOffline ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']], //险种
                            PolicyCargo::$insureTypesText[$data['policy']['policyCargo']['insure_type'] ?? ''] ?? '-', //贸易类型
                            $data['policy']['policyCargo']['subject']['name'] ?? '', //标的
                            Policy::$statusesText[$data['policy']['status']] ?? '', //出单状态
                            $data['created_at'] ?? '', //出单日期
                            $data['policy']['submitted_at'] ?? '', //投保日期
                            $data['company']['name'] ?? '', //保险公司
                            $data['companyBranch']['name'] ?? '', //出单公司
                            '', //用户名
                            '', //投保用户
                            $data['policyholder'], //投保人
                            '', //代理人
                            '', //业务员
                            $data['policy']['user']['platform']['name'], //业务来源
                            $data['policy']['invoice']['created_at'] ?? '', //发票申请时间
                            $this->getSettlementAt($data, $isOffline), //销账时间
                            $data['currency']['name'] ?? '', //币种
                            $isOffline ? 0 : readable_amount($data['coverage']), //保额
                            $isPremiumSync ? $data['user_rate'] : $data['rate'], //成本费率
                            $isPremiumSync ? readable_amount($data['user_premium']) : readable_amount($data['premium']), //成本保费
                            $isPremiumSync ? $data['user_rate'] : $data['platform_rate'], //平台费率
                            $isPremiumSync ? readable_amount($data['user_premium']) : readable_amount($data['platform_premium']), //平台保费
                            '', //代理费率
                            '', //代理保费
                            '', //用户费率
                            '', //用户保费
                            $data['poundage_rate'] ?? '', //经纪费比例
                            readable_amount($this->getPoundage($data, $isOffline, $isSelfPlatform, $data['premium'])), //经纪费
                            $isOffline ? $data['poundage_rate'] : $data['platform_commission_rate'], //平台佣金比例
                            readable_amount($this->getPlatformCommission($data, $isOffline, $isSelfPlatform, $data['platform_premium'])), //平台佣金
                            '', //代理佣金比例
                            '', //代理佣金
                            $this->isPremiumPayment($data, $isOffline), //保费是否已付保险公司
                            '', // 代理佣金是否已发
                            $this->isPoundagePayment($data, $data['policy']), // 经纪费是否已发
                            $isPremiumSync ? '是' : '否', // 保费是否同步
                            $data['channel']['name'] ?? '-', // 渠道
                        ];
                    } else {
                        // 自己用户投保其他平台产品
                        $items[] = [
                            $data['policy']['order_no'], //流水号
                            $data['policy_no'], //保单号
                            $isOffline ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']], //险种
                            PolicyCargo::$insureTypesText[$data['policy']['policyCargo']['insure_type'] ?? ''] ?? '-', //贸易类型
                            $data['policy']['policyCargo']['subject']['name'] ?? '', //标的
                            Policy::$statusesText[$data['policy']['status']] ?? '', //出单状态
                            $data['created_at'] ?? '', //出单日期
                            $data['policy']['submitted_at'] ?? '', //投保日期
                            $data['company']['name'] ?? '', //保险公司
                            $data['companyBranch']['name'] ?? '', //出单公司
                            $data['policy']['user']['username'], //用户名
                            $data['policy']['user']['name'], //投保用户
                            $data['policyholder'], //投保人
                            $data['policy']['user']['is_agent'] ? $data['policy']['user']['name'] : optional($data['policy']['user']['agent'])->name, //代理人
                            $data['salesman']['name'], //业务员
                            '自有业务', //业务来源
                            $data['policy']['invoice']['created_at'] ?? '', //发票申请时间
                            $this->getSettlementAt($data, $isOffline), //销账时间
                            $data['currency']['name'] ?? '', //币种
                            $isOffline ? 0 : readable_amount($data['coverage']), //保额
                            '', //成本费率
                            '', //成本保费
                            $isPremiumSync ? $data['user_rate'] : $data['platform_rate'], //平台费率
                            $isPremiumSync ? readable_amount($data['user_premium']) : readable_amount($data['platform_premium']), //平台保费
                            $isPremiumSync ? $data['user_rate'] : $data['agent_rate'], //代理费率
                            $isPremiumSync ? readable_amount($data['user_premium']) : readable_amount($data['agent_premium']), //代理保费
                            $data['user_rate'], //用户费率
                            readable_amount($data['user_premium']), //用户保费
                            '', //经纪费比例
                            '', //经纪费
                            $isOffline ? $data['poundage_rate'] : $data['platform_commission_rate'], //平台佣金比例
                            readable_amount($this->getPlatformCommission($data, $isOffline, $isSelfPlatform, $data['platform_premium'])), //平台佣金
                            ($data['policy']['user']['platform_id'] === $loggedUser['platform_id'] && ($data['policy']['user']['is_agent'] || $data['policy']['user']->isAgentUser())) ? $data['agent_commission_rate'] : '-', //代理佣金比例
                            ($data['policy']['user']['platform_id'] === $loggedUser['platform_id'] && ($data['policy']['user']['is_agent'] || $data['policy']['user']->isAgentUser())) ? readable_amount($data['agent_commission']) : '-', //代理佣金

                            '', //保费是否已付保险公司
                            ($data['policy']['user']['is_agent'] || $data['policy']['user']->isAgentUser()) ? $this->isCommissionPayment($data) : '-', // 代理佣金是否已发
                            '', // 经纪费是否已发
                            $isPremiumSync ? '是' : '否', // 保费是否同步
                            '', // 渠道
                        ];
                    }
                } else {
                    // 自己平台用户投保己方平台产品
                    $items[] = [
                        $data['policy']['order_no'], //流水号
                        $data['policy_no'], //保单号
                        $isOffline ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']], //险种
                        PolicyCargo::$insureTypesText[$data['policy']['policyCargo']['insure_type'] ?? ''] ?? '-', //贸易类型
                        $data['policy']['policyCargo']['subject']['name'] ?? '', //标的
                        Policy::$statusesText[$data['policy']['status']] ?? '', //出单状态
                        $data['created_at'] ?? '', //出单日期
                        $data['policy']['submitted_at'] ?? '', //投保日期
                        $data['company']['name'] ?? '', //保险公司
                        $data['companyBranch']['name'] ?? '', //出单公司
                        $data['policy']['user']['username'], //用户名
                        $data['policy']['user']['name'], //投保用户
                        $data['policyholder'], //投保人
                        $data['policy']['user']['is_agent'] ? $data['policy']['user']['name'] : optional($data['policy']['user']['agent'])->name, //代理人
                        $data['salesman']['name'], //业务员
                        $data['platform_id'] == $data['policy']['user']['platform_id'] ? '自有业务' : $data['policy']['user']['platform']['name'], //业务来源
                        $data['policy']['invoice']['created_at'] ?? '', //发票申请时间
                        $this->getSettlementAt($data, $isOffline), //销账时间
                        $data['currency']['name'] ?? '', //币种
                        $isOffline ? 0 : readable_amount($data['coverage']), //保额
                        $isPremiumSync ? $data['user_rate'] : $data['rate'], //成本费率
                        $isPremiumSync ? readable_amount($data['user_premium']) : readable_amount($data['premium']), //成本保费
                        $isPremiumSync ? $data['user_rate'] : $data['platform_rate'], //平台费率
                        $isPremiumSync ? readable_amount($data['user_premium']) : readable_amount($data['platform_premium']), //平台保费
                        $isPremiumSync ? $data['user_rate'] : $data['agent_rate'], //代理费率
                        $isPremiumSync ? readable_amount($data['user_premium']) : readable_amount($data['agent_premium']), //代理保费
                        $data['user_rate'], //用户费率
                        readable_amount($data['user_premium']), //用户保费
                        $data['poundage_rate'] ?? '', //经纪费比例
                        readable_amount($this->getPoundage($data, $isOffline, $isSelfPlatform, $data['premium'])), //经纪费
                        $isOffline ? $data['poundage_rate'] : $data['platform_commission_rate'], //平台佣金比例
                        readable_amount($this->getPlatformCommission($data, $isOffline, $isSelfPlatform, $data['platform_premium'])), //平台佣金
                        ($data['policy']['user']['platform_id'] === $loggedUser['platform_id'] && ($data['policy']['user']['is_agent'] || $data['policy']['user']->isAgentUser())) ? $data['agent_commission_rate'] : '-', //代理佣金比例
                        ($data['policy']['user']['platform_id'] === $loggedUser['platform_id'] && ($data['policy']['user']['is_agent'] || $data['policy']['user']->isAgentUser())) ? readable_amount($data['agent_commission']) : '-', //代理佣金

                        $this->isPremiumPayment($data, $isOffline), //保费是否已付保险公司
                        ($data['policy']['user']['is_agent'] || $data['policy']['user']->isAgentUser()) ? $this->isCommissionPayment($data) : '-', // 代理佣金是否已发
                        $this->isPoundagePayment($data, $data['policy']), // 经纪费是否已发
                        $isPremiumSync ? '是' : '否', // 保费是否同步
                        $data['channel']['name'] ?? '-', // 渠道
                    ];
                }
            }
        });

        return $items;
    }

    /**
     * 获取经纪费
     *
     * @param $finance
     * @param $isOffline
     * @param $isSelfPlatform
     * @param $premium
     * @return float|string
     */
    protected function getPoundage($finance, $isOffline, $isSelfPlatform, $premium)
    {
        if ($isOffline) {
            return $finance['poundage'];
        }

        if ($isSelfPlatform) {
            return round(($premium * $finance['poundage_rate']) / 100);
        }

        return 0;
    }

    /**
     * 获取平台佣金
     *
     * @param $finance
     * @param $isOffline
     * @param $isSelfPlatform
     * @param $platformPremium
     * @return float
     */
    protected function getPlatformCommission($finance, $isOffline, $isSelfPlatform, $platformPremium)
    {
        if ($isOffline) {
            return $finance['poundage'];
        }

//        if ($isSelfPlatform) {
//            return round(($platformPremium * $policy['service_charge']) / 100);
//        } else {
//            return round(($platformPremium * $policy['platform_commission_rate']) / 100);
//        }


        return round(($platformPremium * $finance['platform_commission_rate']) / 100);
    }

    /**
     * 保费是否已付保险公司
     *
     * @param  mixed $finance
     * @param  mixed $isOffline
     *
     * @return string
     */
    protected function isPremiumPayment($finance, $isOffline)
    {
        if ($isOffline) {
            if($finance['poundagePayment']){
                return '是';
            }
            if($finance['premiumPayment'] && $finance['premiumPayment']->bill()->exists()){
                return '是';
            }
            if($finance['premiumReceivable'] && $finance['premiumReceivable']->bill()->exists() && $finance['premiumReceivable']['bill']['payee_type'] === PremiumReceivableBill::PAYEE_TYPE_COMPANY){
                return '是';
            }
            return '否';
        }

        if ($this->isSettlement($finance) && $finance['premiumReceivable']['settlement']['type'] === Settlement::TYPE_PAID_OF_COMPANY) {
            return '是';
        }

        if (!empty($finance['premiumPayment'])) {
            return (!empty($finance['premiumPayment']['bill_id']) && $finance['premiumPayment']['bill']['status'] == 2 && !empty($finance['premiumPayment']['bill']['proof'])) ? '是' : '否';
        }

        //        $paymentsCount = $policy['premiumPayments']->whereNotNull('bill_id')->count();
        $paymentsCount = $finance['policy']['premiumPayments']->filter(function ($item) {
            return !empty($item['bill_id']) && $item['bill']['status'] == 2 && !empty($item['bill']['proof']);
        })->count();

        return $paymentsCount > 0 ? '是' : '否';
        //        return !empty($finance['payment']['bill_id']) ? '是' : '否';
    }

    /**
     * 代理佣金是否已发
     *
     * @param   Policy  $policy
     *
     * @return  bool
     */
    protected function isCommissionPayment($finance)
    {
        //        $paymentsCount = $policy['commission']->whereNotNull('bill_id')->count();

        if (!empty($finance['commissionPayment'])) {
            return (!empty($finance['commissionPayment']['bill_id']) && $finance['commissionPayment']['bill']['status'] == 2 && !empty($finance['commissionPayment']['bill']['proof'])) ? '是' : '否';
        }

        $paymentsCount = $finance['policy']['commission']->filter(function ($item) {
            return !empty($item['bill_id']) && $item['bill']['status'] == 2 && !empty($item['bill']['proof']);
        })->count();

        return $paymentsCount > 0 ? '是' : '否';
    }


    /**
     * 经纪费是否已发
     *
     * @param   Policy  $policy
     * @param   $finance
     *
     * @return  bool
     */
    protected function isPoundagePayment($finance, $policy)
    {
        //        $paymentsCount = $policy['poundage']->whereNotNull('bill_id')->count();
        if (!empty($finance['poundagePayment'])) {
            return (!empty($finance['poundagePayment']['bill_id']) && $finance['poundagePayment']['bill']['status'] == 2) ? '是' : '否';
        }

        $paymentsCount = $policy['poundage']->filter(function ($item) use ($finance) {
            return !empty($item['bill_id']) && $item['bill']['status'] == 2 && $item['premium'] == $finance['premium'] && $item['poundage'] == $finance['poundage'];
        })->count();

        return $paymentsCount > 0 ? '是' : '否';
    }

    /**
     * 是否已经销账
     *
     * @param $finance
     * @return bool
     */
    protected function isSettlement($finance)
    {
        if (empty($finance['premiumReceivable'])) {
            return false;
        }

        return optional($finance['premiumReceivable'])->settlement_id !== -1;
    }

    protected function getSettlementAt($data, $isOffline)
    {
        if($isOffline){
            if($data['premiumReceivable']){
                $date = $data['premiumReceivable']['bill']['created_at'] ?? '';
            }else{
                $date = $data['policy']['issued_at'];
            }

            return $date;
        }
        if ($data['policy']['is_virtual'] == 1) {
            $date = $data['premiumReceivable']['settlement']['operated_at'] ?? '';
            if (optional($data['premiumReceivable'])->receivable_platform_id == -1) {
                $date = $data['premiumReceivable']['bill']['created_at'] ?? '';
            }
        } else {
            $date = $data['policy']['issued_at'];
            if(in_array($data['policy']['type'], [Policy::TYPE_GROUP])){
                $date = $data['created_at'];
            }
        }

        return $date;
    }
}
