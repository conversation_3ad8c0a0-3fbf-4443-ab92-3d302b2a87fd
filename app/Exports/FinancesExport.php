<?php

namespace App\Exports;

use App\Models\CommissionPayment;
use App\Models\CommissionReceivable;
use App\Models\Memo;
use App\Models\PlatformPremium;
use App\Models\Policy;
use App\Models\PoundagePayment;
use App\Models\PremiumPayment;
use App\Models\PremiumReceivable;
use App\Models\Settlement;
use App\Services\Finance\CreateFinances;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class FinancesExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{
    protected $data;

    protected $bill;

    protected $headings = [
        PremiumPayment::class => ['流水号', '保单号', '险种', '类型', '投保人', '被保人', '投保用户', '保额币种', '保险金额', '保险公司费率', '保险公司保费', '保费是否同步', '出单公司', '生效时间'],
        PremiumReceivable::class => ['流水号', '保单号', '应收平台', '险种', '类型', '保险金额', '费率', '应收保费',  '币种', '投保人', '被保人', '业务来源', '业务员', '用户性质', '出单公司', '生效时间'],
        PoundagePayment::class => ['流水号', '保单号', '批单号', '投保人', '被保人', '投保用户', '险种', '保险金额', '保险公司保费', '币种', '费率', '经纪费比例', '经纪费应结', '出单公司', '生效时间', '保费支付时间'],
        CommissionPayment::class => ['流水号', '保单号', '投保用户', '业务员', '险种', '保费', '佣金比例', '应付佣金', '代理商', '出单公司', '生效时间', '应发平台'],
        CommissionReceivable::class => ['流水号', '保单号', '险种', '客户保费', '应收佣金', '应收平台', '出单公司', '生效时间'],
        PlatformPremium::class => ['流水号', '保单号', '险种', '业务来源', '产品来源', '平台出单费', '出单公司', '生效时间'],
        Memo::class => ['流水号', '水单金额', '录入人', '付款人', '付款时间', '收款人', '领用人', '水单类型', '状态', '备注'],
        Settlement::class => ['流水号', '申请人', '保单数', '保单总金额', '水单数', '水单总金额', '收款人', '申请时间', '处理时间', '状态'],
    ];

    public function __construct($data, $bill = null)
    {
        if (empty($data[0])) {
            abort(400, '没有可导出的数据');
        }
        $this->data = $data;

        $this->bill = $bill;
    }

    /**
     * 设置A-B列单元格格式
     *
     * @param Cell $cell
     * @param mixed $value
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A', 'B'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

    public function headings(): array
    {
        return $this->headings[get_class($this->data[0])];
    }

    public function array(): array
    {
        switch (get_class($this->data[0])) {
            case PremiumReceivable::class:
                return $this->premiumReceivableFields();
            case PremiumPayment::class:
                return $this->premiumPaymentFields();
            case PoundagePayment::class:
                return $this->poundagePaymentFields();
            case CommissionPayment::class:
                return $this->commissionPaymentFields();
            case CommissionReceivable::class:
                return $this->commissionReceivableFields();
            case PlatformPremium::class:
                return $this->platformPremiumFields();
            case Memo::class:
                return $this->memoFields();
            case Settlement::class:
                return $this->settlementFields();
            default:
                return [];
        }
    }

    /**
     * 保费应付字段
     *
     * @return array
     */
    protected function premiumPaymentFields()
    {
        $items = [];
        foreach ($this->data as $data) {
            $items[] = [
                $data['order_no'],
                $data['policy']['policy_no'],
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']],
                CreateFinances::$types[$data['type']],
                $data['policy']['policyholder'],
                $data['policy']['insured'],
                $data['policy']['user']['name'],
                $data['policy']['policyCargo']['coverageCurrency']['name'] ?? '',
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? 0 : readable_amount($data['policy']['coverage']),
                $data['policy']['rate'],
                readable_amount($data['premium']),
                $data['policy']['is_premium_sync'] === 1 ? '是' : '否',
                $data['companyBranch']['name'],
                $data['issued_at'],
            ];
        }
        return $items;
    }

    /**
     * 保费应收字段
     *
     * @return array
     */
    protected function premiumReceivableFields()
    {
        $items = [];
        foreach ($this->data as $data) {
            // '流水号', '保单号', '应收平台', '险种', '保险金额', '费率', '应收保费',  '币种', '投保人', '被保人', '业务来源', '业务员', '用户性质', '出单公司', '生效时间'
            $items[] = [
                $data['policy']['order_no'],
                $data['policy']['policy_no'],
                optional($data['receivablePlatform'])->name ?? '总平台',
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']],
                CreateFinances::$types[$data['type']],
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? 0 : readable_amount($data['policy']['coverage']),
                $this->getRate($data['policy']),
                readable_amount($data['receivable']),
                in_array($data['policy']['type'], [Policy::TYPE_GENERAL, Policy::TYPE_GROUP, Policy::TYPE_OFFLINE]) ? '人民币' : $data['policy']['policyCargo']['coverageCurrency']['name'] ?? '',
                $data['policy']['policyholder'],
                $data['policy']['insured'],
                $data['platform_id'] != $data['receivable_platform_id'] ? $data['user']['platform']['name'] : $data['user']['name'],
                $data['platform_id'] != $data['receivable_platform_id'] ? '' : $data['policy']['salesman']['name'],
                $data['platform_id'] != $data['receivable_platform_id'] ? '' : $this->getUserType($data['policy']['user']),
                $data['companyBranch']['name'],
                $data['issued_at'],
            ];
        }
        return $items;
    }

    /**
     * 经纪费结算字段
     *
     * @return array
     */
    protected function poundagePaymentFields()
    {
        $items = [];
        foreach ($this->data as $data) {
            if (empty($data['policy'])) {
                continue;
            }
            // '流水号', '保单号', '投保人', '被保人', '投保用户', '险种', '保险金额', '保险公司保费', '币种', '费率', '经纪费比例', '经纪费应结', '出单公司', '生效时间', '保费支付时间'
            $items[] = [
                $data['order_no'],
                $data['policy']['policy_no'],
                $data['policyGroupEndorse']['endorse_no'] ?? '-',
                $data['policy']['policyholder'],
                $data['policy']['insured'],
                $data['user']['name'],
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']],
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? 0 : readable_amount($data['policy']['coverage']),
                readable_amount($data['premium']),
                in_array($data['policy']['type'], [Policy::TYPE_GENERAL, Policy::TYPE_GROUP, Policy::TYPE_OFFLINE]) ? '人民币' : $data['policy']['policyCargo']['coverageCurrency']['name'] ?? '-',
                $this->getRate($data['policy']),
                $data['poundage_rate'],
                readable_amount($data['poundage']),
                $data['companyBranch']['name'],
                $data['issued_at'],
                $data['created_at'],
            ];
        }
        return $items;
    }

    /**
     * 佣金发放字段
     *
     * @return array
     */
    protected function commissionPaymentFields()
    {
        $items = [];
        foreach ($this->data as $data) {
            $items[] = [
                $data['order_no'],
                $data['policy']['policy_no'] . (optional($data['policyGroupEndorse'])->endorse_no ? '-' . optional($data['policyGroupEndorse'])->endorse_no : ''),
                $data['user']['name'],
                $data['policy']['salesman']['name'] ?? '',
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']],
                readable_amount($data['premium']),
                $data['commission_rate'],
                readable_amount($data['commission']),
                optional($data['agent'])->name,
                $data['companyBranch']['name'],
                $data['issued_at'],
                optional($data['paymentPlatform'])->name ?? '总平台',
            ];
        }
        return $items;
    }

    /**
     * 佣金应收字段
     *
     * @return array
     */
    protected function commissionReceivableFields()
    {
        $items = [];
        foreach ($this->data as $data) {
            $items[] = [
                $data['order_no'],
                $data['policy']['policy_no'],
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']],
                readable_amount($data['premium']),
                readable_amount($data['commission']),
                optional($data['receivablePlatform'])->name ?? '总平台',
                $data['companyBranch']['name'],
                $data['issued_at'],
            ];
        }
        return $items;
    }

    /**
     * 平台出单费
     *
     * @return array
     */
    protected function platformPremiumFields()
    {
        $items = [];
        foreach ($this->data as $data) {
            $items[] = [
                $data['order_no'],
                $data['policy']['policy_no'],
                $data['policy']['type'] === Policy::TYPE_OFFLINE ? $data['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$data['policy']['type']],
                $data['user']['platform']['name'] ?? '未知来源', // 业务来源
                $data['policy']['platform']['name'] ?? '未知来源', // 产品来源
                readable_amount($data['premium']),
                $data['companyBranch']['name'],
                $data['issued_at'],
            ];
        }
        return $items;
    }

    /**
     * 水单管理
     *
     * @return array
     */
    protected function memoFields()
    {
        $items = [];
        foreach ($this->data as $data) {
            $items[] = [
                $data['order_no'],
                readable_amount($data['amount']),
                $data['entry']['name'] ?? '',
                $data['payer'],
                $data['paid_at'],
                $data['payee']['name'] ?? '',
                $data['receive']['name'] ?? '',
                Memo::$typesText[$data['type']],
                Memo::$statusesText[$data['status']],
                $data['remark'] ?? '',

            ];
        }
        return $items;
    }

    /**
     * 销账管理
     *
     * @return array
     */
    protected function settlementFields()
    {
        $items = [];
        foreach ($this->data as $data) {
            $items[] = [
                $data['order_no'],
                $data['apply']['name'] ?? '',
                $data['receivables']->count(),
                readable_amount($data['policy_amount']),
                $data['memos']->count(),
                readable_amount($data['memo_amount']),
                $data['payee']['name'],
                $data['created_at'],
                $data['operated_at'],
                Settlement::$statusesText[$data['status']],
            ];
        }
        return $items;
    }

    protected function getRate($policy)
    {
        if($policy['platform_id'] != $policy['user']['platform_id']){
            return $policy['platform_rate'];
        }

        if($policy['user']->isAgentUser() || $policy['user']['is_agent'] === 1){
            return $policy['agent_rate'];
        }

        return $policy['user_rate'];
    }

    protected function getUserType($user)
    {
        return $user['is_agent'] === 1 ? '代理' : '会员';
    }
}
