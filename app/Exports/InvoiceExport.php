<?php

namespace App\Exports;

use App\Models\Invoice;
use Illuminate\Support\Arr;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class InvoiceExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder, WithTitle
{
    protected $data;

    protected $sheetTitle;

    protected $heading = ['流水号', '发票抬头', '发票类型', '纳税人识别码', '注册地址', '注册电话', '开户行', '银行账号', '发票金额', '保单数', '申请人', '申请时间', '状态', '保单号'];

    /**
     * Create invoices export instance.
     *
     * InvoiceExport constructor.
     * @param $data
     * @param string $sheetTitle
     */
    public function __construct($data, $sheetTitle = '发票数据')
    {
        $this->data = $data;

        $this->sheetTitle = $sheetTitle;
    }

    /**
     * 工作簿名称
     *
     * @return string
     */
    public function title(): string
    {
        return $this->sheetTitle;
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return $this->heading ?? [];
    }

    /**
     * Register events.
     *
     * @return  array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }

    /**
     * Bind value.
     *
     * @param   Cell  $cell
     * @param   mixed  $value
     *
     * @return  bool
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A', 'D', 'F', 'H', 'I', 'N'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

    /**
     *
     *
     * @return array
     */
    public function array(): array
    {
        return $this->invoiceFields();
    }

    protected function invoiceFields()
    {
        $items = [];
        foreach ($this->data as $data){
            $items[] = [
                $data['order_no'],
                $data['company_name'],
                Invoice::$typesText[$data['type']],
                $data['tax_no'],
                $data['registered_addr'],
                $data['registered_phone_number'],
                $data['bank_name'],
                $data['bankcard_no'],
                readable_amount($data['amount']),
                $data['policies']->groupBy('id')->count(),
                optional($data['apply'])->name,
                $data['created_at'],
                Invoice::$statusesText[$data['status']],
                Arr::first($data['policies']->pluck('policy_no')->toArray()) . "\r\n" . implode("\r\n", $data['policyGroupEndorses']->pluck('endorse_no')->toArray()),
            ];
        }
        return $items;
    }
}
