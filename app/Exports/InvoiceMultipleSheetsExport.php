<?php

namespace App\Exports;


use App\Models\Policy;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class InvoiceMultipleSheetsExport implements WithMultipleSheets
{

    protected $invoices;


    /**
     * Create invoices export instance.
     *
     * InvoiceExport constructor.
     * @param $data
     */
    public function __construct($data)
    {
        $this->invoices = $data;
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        $sheets[0] = new InvoiceExport($this->invoices);

        foreach ($this->invoices as $invoice){
            $sheets[] = new InvoicePolicyExport($invoice, $invoice['order_no']);
            foreach ($invoice['policies']->unique('id')->all() as $policy){
                if($policy['type'] == Policy::TYPE_GROUP){
                    $sheets[] = new PolicyGroupEndorseExport($invoice['policyGroupEndorses'], $policy['policy_no']);
                }
            }
        }

        return $sheets;
    }
}
