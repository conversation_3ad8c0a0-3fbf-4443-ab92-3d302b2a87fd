<?php

namespace App\Exports;

use App\Models\Invoice;
use App\Models\Policy;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;

class InvoicePolicyExport implements FromArray, WithHeadings, WithTitle
{
    /**
     * 需要导出的发票.
     *
     * @var Invoice
     */
    protected $invoice;


    protected $title;

    /**
     * 导出字段.
     *
     * @var string[]
     */
    protected $heading = ['流水号', '保单号', '险种', '发票类型', '出单公司', '投保人', '被保险人', '保险金额', '保额币种', '保费', '投保时间'];

    /**
     * 导出发票保单
     *
     * InvoicePolicyExport constructor.
     * @param Invoice $invoice
     * @param string $title
     */
    public function __construct(Invoice $invoice, $title = '发票保单导出')
    {
        $this->invoice = $invoice;

        $this->title = $title;
    }

    /**
     * 导出头
     *
     * @return array
     */
    public function headings(): array
    {
        return $this->heading ?? [];
    }

    /**
     * Register events.
     *
     * @return  array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return $this->title;
    }

    /**
     * @return array|\Illuminate\Support\Collection
     */
    public function array(): array
    {
        $policies = $this->invoice['policies']->unique('id')->all();
        $cellData = [];
        foreach ($policies as $policy) {
            $cellData[] = [
                $policy->order_no,
                $policy->policy_no,
                Policy::$types[$policy['type']],
                Invoice::$typesText[$this->invoice['type']],
                $policy->companyBranch->name,
                $policy->policyholder,
                $policy->insured,
                $policy->type == Policy::TYPE_GROUP ? '/' : readable_amount($policy->coverage),
                $policy->type == Policy::TYPE_GROUP ? '/' : $policy->policyCargo->coverageCurrency->name ?? '',
                $policy->type == Policy::TYPE_GROUP
                    ? $this->groupPolicyInvoiceAmount($policy)
                    : readable_amount($policy->user_premium),
                $policy->submitted_at,
            ];
        }

        return $cellData;
    }

    /**
     * 雇主发票金额.
     *
     * @param   Policy  $policy
     *
     * @return  float
     */
    protected function groupPolicyInvoiceAmount(Policy $policy)
    {
        $policyGroupEndorses = $this->invoice['policyGroupEndorses']->where('policy_id', $policy->id);

        $amount = 0;
        foreach ($policyGroupEndorses as $endorse) {
            $amount += $endorse->total_fee;
        }

        return sprintf('%.2f', $amount);
    }
}
