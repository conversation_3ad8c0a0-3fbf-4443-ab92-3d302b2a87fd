<?php

namespace App\Exports;

use App\Models\Policy;
use App\Models\PolicyCargo;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class MixedPolicyExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{
    /**
     * 保单数据.
     *
     * @var \Illuminate\Database\Eloquent\Builder
     */
    protected $queryBuilder;

    /**
     * Create policies export instance.
     *
     * @param \Illuminate\Database\Eloquent\Builder $queryBuilder
     *
     * @return  void
     */
    public function __construct($queryBuilder)
    {
        $this->queryBuilder = $queryBuilder;
    }

    /**
     * Bind value.
     *
     * @param Cell $cell
     * @param mixed $value
     *
     * @return  bool
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A', 'B', 'Q', 'U', 'V'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return [
            '流水号',
            '保单号',
            '险种',
            '贸易类型',
            '标的',
            '人工审核原因',
            '出单状态',
            '出单日期',
            '投保时间',
            '保险公司',
            '出单公司',
            '用户名',
            '投保用户',
            '投保人',
            '被保人',
            '代理人',
            '业务员',
            '币种',
            '保险金额',
            '起运日期',
            '起运地',
            '目的地',
            '提运单号',
            '发票号',
            '船名航次',
            '运输方式',
            '平台费率(‱)',
            '平台保费',
            '代理费率(‱)',
            '代理保费',
            '用户费率(‱)',
            '用户保费',
            '保费是否同步',
            '工作编号',
            '操作人',
            '其他信息',
        ];
    }

    /**
     * Register events.
     *
     * @return  array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }

    /**
     * 数据.
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        $this->queryBuilder->chunkById(500, function ($policies) use (&$items) {
            foreach ($policies as $policy) {
                //                $isSelfPlatform = $policy['platform_id'] === Auth::user()['platform_id'];
                if ($this->isCargoPolicy($policy)) {
                    $items[] = $this->cargoItem($policy);
                    continue;
                }

                if ($this->isGroupPolicy($policy)) {
                    $items[] = $this->groupItem($policy);
                    continue;
                }

                if ($policy['type'] === Policy::TYPE_GENERAL) {
                    $items[] = $this->generalItem($policy);
                    continue;
                }

                if ($policy['type'] === Policy::TYPE_OFFLINE) {
                    $items[] = $this->offlineItem($policy);
                    continue;
                }
            }
        }, 'id');

        return $items;
    }

    /**
     * 雇主责任险.
     *
     * @param Policy $policy
     *
     * @return array[]
     */
    protected function groupItem(Policy $policy)
    {
        return [
            $policy['order_no'],
            $policy['policy_no'],
            Policy::$types[$policy['type']],
            '-',
            '-',
            '',
            Policy::$statusesText[$policy['status']],
            optional($policy['issued_at'])->format('Y-m-d'),
            optional($policy['submitted_at'])->format('Y-m-d'),
            $policy['company']['name'],
            $policy['companyBranch']['name'],
            $policy['user']['username'],
            $policy['user']['name'],
            $policy['policyholder'],
            $policy['insured'],
            $policy['user']->isAgentUser() && $policy['platform_id'] === $policy['user']['platform_id'] ? $policy['user']['agent']['name'] : '',
            $policy['salesman']['name'],
            '人民币',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            0,
            readable_amount($policy['user_premium']),
            0,
            $policy['user']->isAgentUser() ? readable_amount($policy['user_premium']) : '-',
            0,
            readable_amount($policy['user_premium']),
            '',
            $policy['sticky_note'],
            '-',
        ];
    }

    /**
     * 支出佣金.
     *
     * @param  Policy $policy
     * @param  int $premium
     * @return  string
     */
    protected function groupCost(Policy $policy, int $premium)
    {
        $cost = 0;
        if ($policy['user']['is_agent'] || $policy['user']['agent_id'] !== -1) {
            $cost = round(bcmul($premium, bcdiv($policy['agent_commission_rate'], 100, 2), 2), 2);
        }

        return readable_amount($cost);
    }

    /**
     * 收入.
     *
     * @param  Policy $policy
     * @param  float $premium
     * @return string
     */
    protected function groupIncome(Policy $policy, float $premium = 0)
    {
        $income = 0;
        if ($policy['platform_id'] !== $policy['user']['platform_id']) {
            $income = round(bcmul($premium, bcdiv($policy['platform_commission_rate'], 100, 2), 2), 2);
        } else {
            $income = round(bcmul($premium, bcdiv($policy['service_charge'], 100, 2), 3), 2);
        }

        return readable_amount($income);
    }

    /**
     * 通用财产险.
     *
     * @param Policy $policy
     *
     * @return  string[]
     */
    protected function generalItem(Policy $policy)
    {
        return [
            $policy['order_no'],
            $policy['policy_no'],
            Policy::$types[$policy['type']],
            '-',
            '-',
            '',
            Policy::$statusesText[$policy['status']],
            optional($policy['issued_at'])->format('Y-m-d'),
            optional($policy['submitted_at'])->format('Y-m-d'),
            $policy['company']['name'],
            $policy['companyBranch']['name'],
            $policy['user']['username'],
            $policy['user']['name'],
            $policy['policyholder'],
            $policy['insured'],
            $policy['user']->isAgentUser() && $policy['platform_id'] === $policy['user']['platform_id'] ? $policy['user']['agent']['name'] : '',
            $policy['salesman']['name'],
            '人民币',
            readable_amount($policy['coverage']),
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            0,
            readable_amount($policy['platform_premium']),
            0,
            $policy['user']->isAgentUser() ? readable_amount($policy['agent_premium']) : '-',
            0,
            readable_amount($policy['user_premium']),
            '',
            $policy['sticky_note'],
            '-'
        ];
    }

    /**
     * 货运险导出字段.
     *
     * @param Policy $policy
     *
     * @return string[]
     */
    protected function cargoItem(Policy $policy)
    {
        if ($policy->policyFinances->isNotEmpty()) {
            $data = [];
            $policyFinancesData = $policy->policyFinances->where('platform_id', Auth::user()['platform_id']);
            if ($policyFinancesData->count() == 0) {
                $policyFinancesData = $policy->policyFinances;
            }
            foreach ($policyFinancesData as $finance) {
                $data[] = $this->cargoItemRow($policy, $finance['policy_no'], $finance['platform_premium'], $finance['agent_premium'], $finance['user_premium'], $finance['created_at']);
            }
            return $data;
        }

        return $this->cargoItemRow($policy, $policy['policy_no'], $policy['platform_premium'], $policy['agent_premium'], $policy['user_premium'], $policy['issued_at']);
    }

    /**
     * 获取货运险字段
     *
     * @param $policy
     * @param $policyNo
     * @param $platformPremium
     * @param $agentPremium
     * @param $userPremium
     * @param $issuedAt
     * @return array
     */
    protected function cargoItemRow($policy, $policyNo, $platformPremium, $agentPremium, $userPremium, $issuedAt): array
    {
        return [
            $policy['order_no'],
            $policyNo,
            Policy::$types[$policy['type']],
            PolicyCargo::$insureTypesText[$policy['policyCargo']['insure_type'] ?? ''] ?? '-',
            $policy['policyCargo']['subject']['name'] ?? '', //标的
            $policy['policyCargo']['subjectCategories']->isNotEmpty() ? $policy['policyCargo']['subjectCategories']->implode('name', ',') : '',
            $policy['policy_no'] == $policyNo ? Policy::$statusesText[$policy['status']] : '已退保',
            optional($issuedAt)->format('Y-m-d'),
            optional($policy['submitted_at'])->format('Y-m-d'),
            $policy['company']['name'],
            $policy['companyBranch']['name'],
            $policy['user']['username'],
            $policy['user']['name'],
            $policy['policyholder'],
            $policy['insured'],
            $policy['user']->isAgentUser() ? $policy['user']['agent']['name'] : '',
            $policy['salesman']['name'],
            $policy['policyCargo']['coverageCurrency']['name'],
            readable_amount($policy['coverage']),
            $policy['policyCargo']['shipping_date'],
            $policy['policyCargo']['departure'],
            $policy['policyCargo']['destination'],
            $policy['policyCargo']['waybill_no'],
            $policy['policyCargo']['invoice_no'],
            $policy['policyCargo']['transport_no'],
            $policy['policyCargo']['transportMethod']['name'] ?? '',
            $policy['is_premium_sync'] ? $policy['user_rate'] : $policy['platform_rate'],
            $policy['is_premium_sync'] ? readable_amount($userPremium) : readable_amount($platformPremium),
            $policy['user']->isAgentUser() ? ($policy['is_premium_sync'] ? $policy['user_rate'] : $policy['agent_rate']) : '-',
            $policy['user']->isAgentUser() ? ($policy['is_premium_sync'] ? readable_amount($userPremium) : readable_amount($agentPremium)) : '-',
            $policy['user_rate'],
            readable_amount($userPremium),
            $policy['is_premium_sync'] === 1 ? '是' : '否',
            $policy['sticky_note'],
            $policy['policyLogs'][0]['admin']['name'] ?? '-',
        ];
    }

    /**
     * 线下录入保单字段
     *
     * @param Policy $policy
     * @return array
     */
    protected function offlineItem(Policy $policy)
    {
        return [
            $policy['order_no'],
            $policy['policy_no'],
            $policy['policyOffline']['category']['name'] . '-线下录入' ?? '线下录入保单',
            '-',
            $policy['policyOffline']['subject'] ?? '-',
            '',
            Policy::$statusesText[$policy['status']],
            optional($policy['issued_at'])->format('Y-m-d'),
            optional($policy['submitted_at'])->format('Y-m-d'),
            $policy['company']['name'],
            $policy['companyBranch']['name'],
            $policy['user']['username'],
            $policy['user']['name'],
            $policy['policyholder'],
            $policy['insured'],
            $policy['user']->isAgentUser() && $policy['platform_id'] === $policy['user']['platform_id'] ? $policy['user']['agent']['name'] : '',
            $policy['salesman']['name'],
            '人民币',
            readable_amount($policy['coverage']),
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            0,
            readable_amount($policy['platform_premium']),
            0,
            $policy['user']->isAgentUser() ? readable_amount($policy['agent_premium']) : '-',
            0,
            readable_amount($policy['user_premium']),
            $policy['is_premium_sync'] === 1 ? '是' : '否',
            $policy['sticky_note'],
            '-',
            $this->getOfflineCustomData($policy['policyOffline']['custom_columns']),
        ];
    }

    /**
     * 是否是货运险保单.
     *
     * @param Policy $policy
     *
     * @return  bool
     */
    protected function isCargoPolicy(Policy $policy)
    {
        return in_array($policy['type'], [
            Policy::TYPE_DOMESTIC,
            Policy::TYPE_INTL,
            Policy::TYPE_CBEC,
            Policy::TYPE_LIABILITY
        ]);
    }

    /**
     * 是否是雇主责任险.
     *
     * @param  Policy $policy
     * @return bool
     */
    protected function isGroupPolicy(Policy $policy)
    {
        return $policy['type'] === Policy::TYPE_GROUP;
    }

    /**
     * 支出比例.
     *
     * @param  Policy $policy
     * @return float
     */
    protected function costRate(Policy $policy)
    {
        return ($policy['user']['is_agent'] || $policy['user']['agent_id'] !== -1) ? $policy['agent_commission_rate'] : 0;
    }

    /**
     * 支出佣金.
     *
     * @param  Policy $policy
     * @return string
     */
    protected function cost(Policy $policy)
    {
        return readable_amount(($policy['user']['is_agent'] || $policy['user']['agent_id'] !== -1 || $policy['type'] === Policy::TYPE_OFFLINE) ? $policy['agent_commission'] : 0);
    }

    /**
     * 收入.
     *
     * @param  Policy $policy
     * @return string
     */
    protected function income(Policy $policy)
    {
        if ($policy['platform_id'] !== $policy['user']['platform_id']) {
            return readable_amount($policy['platform_commission']);
        }

        return readable_amount(round(bcmul($policy['premium'], bcdiv($policy['service_charge'], 100, 2), 3), 2));
    }

    /**
     * 收入比例.
     *
     * @param Policy $policy
     *
     * @return string
     */
    protected function incomeRate(Policy $policy)
    {
        if ($policy['platform_id'] !== $policy['user']['platform_id']) {
            return $policy['platform_commission_rate'];
        }

        return $policy['service_charge'];
    }

    /**
     * 获取线下录入保单自定义字段数据
     *
     * @param mixed $columns
     * @return string
     */
    protected function getOfflineCustomData($columns)
    {
        $columns = json_decode($columns, true);
        if (empty($columns)) {
            return '-';
        }
        $data = '';
        foreach ($columns as $column) {
            $data .= $column['name'] . ':' . $column['value'] . "\n";
        }

        return $data;
    }
}
