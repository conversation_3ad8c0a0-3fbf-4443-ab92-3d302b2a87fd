<?php

namespace App\Exports;

use App\Models\CommissionPaymentBill;
use App\Models\PoundagePaymentBill;
use App\Models\PremiumPaymentBill;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;

class PaymentBillExport implements FromArray, WithHeadings
{
    /**
     * 支付记录数据
     *
     * @var
     */
    protected $bills;

    /**
     * 表头
     *
     * @var array
     */
    protected $headings = [
        PremiumPaymentBill::class => ['流水号', '保险公司保费', '实际支付', '处理人', '支付时间'],
        PoundagePaymentBill::class => ['流水号', '单据数', '保费总数', '经纪费应付', '经纪费实际支付', '处理人', '支付时间', '状态', '备注'],
        CommissionPaymentBill::class => ['流水号', '代理人', '保费总数', '佣金应发', '佣金实际发放', '处理人', '支付时间'],
    ];

    /**
     * PremiumPaymentBillExport constructor.
     * @param $bills
     */
    public function __construct($bills)
    {
        $this->bills = $bills;
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return $this->headings[get_class($this->bills[0])];
    }

    public function array(): array
    {
        switch (get_class($this->bills[0])){
            case PremiumPaymentBill::class:
                return $this->premiumBillFields();
            case PoundagePaymentBill::class:
                return $this->poundageBillFields();
            case CommissionPaymentBill::class:
                return $this->commissionBillFields();
            default:
                return [];
        }
    }

    /**
     * 保费应付支付记录导出字段
     */
    public function premiumBillFields()
    {
        $items = [];
        foreach ($this->bills as $bill){
            $items[] = [
                $bill['order_no'],
                readable_amount($bill['premium']),
                readable_amount($bill['actual_premium']),
                $bill['operation']['name'] ?? '',
                $bill['paid_at'],
            ];
        }

        return $items;
    }

    /**
     * 经纪费结算支付记录导出字段
     */
    protected function poundageBillFields()
    {
        $items = [];
        foreach ($this->bills as $bill){
            $items[] = [
                $bill['order_no'],
                $bill['payment_count'],
                readable_amount($bill['premium']),
                readable_amount($bill['poundage']),
                readable_amount($bill['actual_poundage']),
                $bill['operation']['name'] ?? '',
                $bill['paid_at'],
                $this->getPoundageBillStatus($bill),
                $bill['remark'],
            ];
        }

        return $items;
    }

    /**
     * 佣金发放支付记录导出字段
     */
    protected function commissionBillFields()
    {
        $items = [];
        foreach ($this->bills as $bill){
            $items[] = [
                $bill['order_no'],
                optional($bill['agent'])->name,
                readable_amount($bill['premium']),
                readable_amount($bill['commission']),
                readable_amount($bill['actual_commission']),
                $bill['operation']['name'] ?? '',
                $bill['paid_at'],
            ];
        }

        return $items;
    }

    protected function getPoundageBillStatus($bill)
    {
        switch($bill['status']){
            case 0:
                return '未提交';
            case 1:
                if(in_array($bill['operation_id'], [0, -1])){
                    return '已提交';
                }
                return '审核中';
            case 2:
                return '已完成';
            default:
                return '';
        }
    }
}
