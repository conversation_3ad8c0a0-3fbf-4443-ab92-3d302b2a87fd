<?php

namespace App\Exports;

use App\Models\Payment;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;

class PaymentExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{
    protected $payment;

    protected $heading = ['流水号', '充值用户', '类型', '方式', '充值金额', '申请人', '处理人' , '处理时间', '状态', '申请时间'];

    public function __construct($data)
    {
        $this->payment = $data;
    }

    /**
     * 设置A列单元格格式
     *
     * @param Cell $cell
     * @param mixed $value
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }
        return parent::bindValue($cell, $value);
    }

    public function headings():array
    {
        return $this->heading;
    }

    public function array():array
    {
        return $this->paymentFields();
    }

    /**
     * 充值审核导出字段
     *
     * @return array
     */
    protected function paymentFields()
    {
        $items = [];
        foreach ($this->payment as $data){
            $items[] = [
                $data['order_no'],
                $data['user']['name'] ?? '',
                Payment::$typeNames[$data['type']],
                Payment::$chargeTypeNames[$data['charge_type']],
                $data['amount'],
                $data['apply']['name'] ?? '',
                $data['operated']['name'] ?? '',
                $data['operated_at'],
                Payment::$statuses[$data['status']],
                $data['created_at'],
            ];
        }
        return $items;
    }

}
