<?php

namespace App\Exports;

use App\Models\Policy;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class PolicyExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{
    protected $headings = [
        Policy::TYPE_DOMESTIC => [
            '系统流水号',
            '保单号',
            '投保用户',
            '代理人',
            '投保人',
            '被保险人',
            '保险公司',
            '出单公司',
            '标的',
            '货物名称',
            '人工审核原因',
            '保额',
            '成本费率(‱)',
            '成本保费',
            '代理费率(‱)',
            '代理保费',
            '用户费率(‱)',
            '用户保费',
            '佣金比例(%)',
            '佣金',
            '投保时间',
            '状态',
            '业务员',
        ],
        Policy::TYPE_GROUP => [
            '系统流水号',
            '保单号',
            '保险公司',
            '出单公司',
            '保险产品',
            '投保人',
            '被保人',
            '投保用户',
            '在保人数',
            '投保时间',
            '保费',
            '状态',
            '地区信息',
        ],
        Policy::TYPE_GENERAL => [
            '系统流水号',
            '保单号',
            '保险公司',
            '出单公司',
            '投保人',
            '被保险人',
            '保费',
            '状态',
            '投保时间',
            '起保日期',
            '终保日期',
            '其他信息',
        ],
        Policy::TYPE_OFFLINE => [
            '系统流水号',
            '保单号',
            '保险公司',
            '出单公司',
            '投保渠道',
            '险类',
            '险种',
            '投保人',
            '被保险人',
            '保费',
            '经纪费比例',
            '保费支付情况',
            '结算方式',
            '保费支付类型',
            '业务类型',
            '客户名称',
            '佣金比例',
            '状态',
            '投保时间',
            '起保日期',
            '终保日期',
        ],
    ];

    /**
     * 保单类型.
     *
     * @var int
     */
    protected $type;

    /**
     * 保单数据.
     *
     * @var \Illuminate\Support\Collection
     */
    protected $policies;

    /**
     * Create policies export instance.
     *
     * @param \Illuminate\Database\Eloquent\Collection $policies
     * @param int $type
     *
     * @return  void
     */
    public function __construct($policies, int $type)
    {
        $this->policies = $policies;
        $this->type = $type;
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        $this->headings[Policy::TYPE_INTL] = $this->headings[Policy::TYPE_DOMESTIC];
        $this->headings[Policy::TYPE_CBEC] = $this->headings[Policy::TYPE_DOMESTIC];
        $this->headings[Policy::TYPE_LIABILITY] = $this->headings[Policy::TYPE_DOMESTIC];

        return $this->headings[$this->type] ?? [];
    }

    /**
     * Register events.
     *
     * @return  array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }

    /**
     * Bind value.
     *
     * @param   Cell  $cell
     * @param   mixed  $value
     *
     * @return  bool
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A', 'B'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function array(): array
    {
        switch ($this->type) {
            case Policy::TYPE_DOMESTIC:
            case Policy::TYPE_INTL:
            case Policy::TYPE_LIABILITY:
            case Policy::TYPE_CBEC:
                return $this->cargo();
            case Policy::TYPE_GROUP:
                return $this->group();
            case Policy::TYPE_GENERAL:
                return $this->general();
            case Policy::TYPE_OFFLINE:
                return $this->offline();
            default:
                return [];
        }
    }

    /**
     * 货运险导出.
     *
     * @return  array
     */
    protected function cargo(): array
    {
        $items = [];
        foreach ($this->policies as $policy) {
            $items[] = [
                $policy['order_no'],
                $policy['policy_no'],
                $this->isCorssPlatform($policy) ? $policy['user']['platform']['name'] : $policy['user']['name'],
                $this->isCorssPlatform($policy) || !$policy['user']->isAgentUser() ? '-' : $policy['user']['agent']['name'],
                $policy['policyholder'],
                $policy['insured'],
                $policy['company']['name'],
                $policy['companyBranch']['name'],
                $policy['policyCargo']['subject']['name'] ?? '-',
                $policy['policyCargo']['goods_name'] ?? '',
                $policy['policyCargo']['subjectCategories']->isNotEmpty() ? $policy['policyCargo']['subjectCategories']->implode('name', ',') : '',
                readable_amount($policy['coverage']),
                $policy['is_premium_sync'] ? $policy['user_rate'] : $policy['rate'],
                $policy['is_premium_sync'] ? readable_amount($policy['user_premium']) : readable_amount($policy['premium']),
                $policy['is_premium_sync'] ? $policy['user_rate'] : ($this->isCorssPlatform($policy) ? $policy['platform_rate'] : $policy['agent_rate']),
                $policy['is_premium_sync'] ? readable_amount($policy['user_premium']) : ($this->isCorssPlatform($policy) ? readable_amount($policy['platform_premium']) : readable_amount($policy['agent_premium'])),
                $this->isCorssPlatform($policy) ? '' : ($this->isDirectUserOrAgentOrAgentUser($policy) ? $policy['agent_rate'] : $policy['user_rate']),
                $this->isCorssPlatform($policy) ? '' : readable_amount($this->isDirectUserOrAgentOrAgentUser($policy) ? $policy['agent_premium'] : $policy['user_premium']),
                $this->isCorssPlatform($policy) ? $policy['platform_commission_rate'] : $policy['agent_commission_rate'],
                $this->isCorssPlatform($policy) ? readable_amount($policy['platform_commission']) : readable_amount($policy['agent_commission']),
                optional($policy['submitted_at'])->format('Y-m-d'),
                Policy::$statusesText[$policy['status']],
                $this->isCorssPlatform($policy) ? '' : $policy['salesman']['name'],
            ];
        }

        return $items;
    }

    /**
     * 是否是跨平台保单.
     *
     * @param   Policy  $policy
     *
     * @return  bool
     */
    protected function isCorssPlatform(Policy $policy): bool
    {
        return $policy['user']['platform_id'] !== $policy['platform_id'];
    }

    /**
     * 是否是直属用户或者直属用户下级代理.
     *
     * @param   Policy  $policy
     *
     * @return  bool
     */
    protected function isDirectUserOrAgentOrAgentUser(Policy $policy)
    {
        return $policy['user']['agent_id'] === -1 || $policy['user']['is_agent'] || $policy['user']->isAgentUser();
    }

    /**
     * 雇主责任险导出.
     *
     * @return  array
     */
    private function group()
    {
        $items = [];
        foreach ($this->policies as $policy) {
            $items[] = [
                $policy['order_no'],
                $policy['policy_no'],
                $policy['company']['name'],
                $policy['companyBranch']['name'],
                $policy['product']['name'],
                $policy['policyholder'],
                $policy['insured'],
                $this->isCorssPlatform($policy) ? $policy['user']['platform']['name'] : $policy['user']['name'],
                $policy['policyGroup']['insured_employee_count'] ?? 0,
                optional($policy['submitted_at'])->format('Y-m-d'),
                readable_amount($policy['user_premium']),
                Policy::$statusesText[$policy['status']],
                $policy['company_branch_id'] == 7 ? $this->getGroupZYAreaData($policy['policyGroup']) : '',
            ];
        }

        return $items;
    }

    /**
     * 通用险.
     *
     * @return  array
     */
    protected function general()
    {
        $items = [];
        // '系统流水号', '保单号', '保险公司', '出单公司', '投保人', '被保险人', '保费' ,'投保时间', '起保日期', '终保日期', '其他信息'
        foreach ($this->policies as $policy) {
            $items[] = [
                $policy['order_no'],
                $policy['policy_no'],
                $policy['company']['name'],
                $policy['companyBranch']['name'],
                $policy['policyholder'],
                $policy['insured'],
                readable_amount($policy['user_premium']),
                Policy::$statusesText[$policy['status']],
                $policy['created_at']->format('Y-m-d H:i:s'),
                $policy['policyGeneral']['start_at'],
                $policy['policyGeneral']['end_at'],
                $this->prepareGeneralCustomFields($policy['policyGeneral']['addition']),
            ];
        }

        return $items;
    }

    /**
     * 线下录入保单.
     *
     * @return  array
     */
    protected function offline()
    {
        $items = [];
        foreach ($this->policies as $policy) {
            $items[] = [
                $policy['order_no'],
                $policy['policy_no'],
                $policy['company']['name'],
                $policy['companyBranch']['name'],
                $policy['channel']['name'] ?? '',
                $policy['policyOffline']['category']['name'],
                $policy['policyOffline']['insurance']['name'],
                $policy['policyholder'],
                $policy['insured'],
                readable_amount($policy['user_premium']),
                $policy['policyOffline']['poundage_rate'],
                $policy['policyOffline']['premium_pay_type'] === 1 ? '保费已付' : '保费未付',
                $policy['policyOffline']['settlement_type'] === 1 ? '含税保费' : '不含税保费',
                $policy['policyOffline']['premium_pay_type'] === 1 ? $policy['policyOffline']['payee_type'] === 1 ? '客户支付保险公司' : '客户支付平台' : '',
                $policy['policyOffline']['business_type'] === 1 ? '会员业务' : '代理业务',
                $policy['user']['name'],
                $policy['agent_commission_rate'],
                Policy::$statusesText[$policy['status']],
                $policy['policyOffline']['insure_date']->format('Y-m-d'),
                $policy['policyOffline']['start_at'],
                $policy['policyOffline']['end_at'],
            ];
        }

        return $items;
    }

    /**
     * 准备其他险种自定义字段数据
     *
     * @param $fields
     * @return false|string
     */
    protected function prepareGeneralCustomFields($fields)
    {
        $data = [];
        foreach (json_decode($fields, true) as $field) {
            if (in_array($field['type'], ['file', '_file'])) {
                $field['value'] = url(Storage::url($field['value']));
            }
            $data[$field['title']] = $field['value'];
        }

        return json_encode($data, 320);
    }

    /**
     * 获取中意雇主区域信息
     *
     * @param mixed $policyGroup
     */
    protected function getGroupZYAreaData($policyGroup)
    {
        return $policyGroup['extra_info']['API_GROUP_ZY']['provinceName'] ?? '';
    }
}
