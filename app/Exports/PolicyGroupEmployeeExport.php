<?php

namespace App\Exports;

use App\Models\PolicyGroup;
use App\Models\PolicyGroupEmployee;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Events\AfterSheet;

class PolicyGroupEmployeeExport implements FromCollection, WithHeadings, WithMapping, WithColumnWidths, WithEvents
{
    /** @var PolicyGroup */
    protected $policyGroup;
    /** @var int */
    protected $policyGroupId;
    /** @var int */
    protected $count;

    public function __construct(int $policyGroupId)
    {
        $this->policyGroupId = $policyGroupId;
        $this->count = 0;
        $this->policyGroup = PolicyGroup::query()->with('policy')->find($policyGroupId);
    }

    /**
     * @return array
     */
    public function registerEvents(): array
    {
        $rowCount = $this->collection()->count() + 1;

        return [
            AfterSheet::class => function (AfterSheet $event) use ($rowCount) {
                $cellRange = "A1:H{$rowCount}";
                $event->sheet->getDelegate()->getStyle('A1:H1')->getFont()->setSize(12);
                for ($i = 0; $i <= $rowCount; ++$i) {
                    $event->sheet->getDelegate()->getRowDimension($i)->setRowHeight(20);
                }
                $event->sheet->getDelegate()
                    ->getStyle($cellRange)
                    ->getAlignment()
                    ->setVertical('center');
                $event->sheet->getDelegate()
                    ->getStyle("A2:H{$rowCount}")
                    ->applyFromArray([
                        'borders' => [
                            'allBorders' => [
                                'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,
                                'color' => ['argb' => '4A708B'],
                            ],
                        ]
                    ]);

                $event->sheet->getDelegate()
                    ->insertNewRowBefore(1, 1);
                $event->sheet->getDelegate()->mergeCells('A1:H1');
                $event->sheet->getDelegate()
                    ->setCellValue('A1', "被保险人：{$this->policyGroup->policy->insured}");

                $event->sheet->getDelegate()->getStyle('A1:H1')
                    ->getAlignment()
                    ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT)
                    ->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
            },
        ];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return PolicyGroupEmployee::query()
            ->whereIn('status', [
                PolicyGroupEmployee::STATUS_ENABLED,
                PolicyGroupEmployee::STATUS_COMMITTED,
                PolicyGroupEmployee::STATUS_ENDORSING,
            ])
            ->where('policy_group_id', $this->policyGroupId)
            ->get();
    }

    /**
     * @param PolicyGroupEmployee $row
     * @return array
     */
    public function map($row): array
    {
        return [
           ++$this->count,
            $row->name,
            '`' . $row->idcard_no,
            $row->job_name,
            $row->complete_time,
            PolicyGroupEmployee::ACTION_EXPLAINS[$row->action] ?? '-',
            PolicyGroupEmployee::STATUS_EXPLAINS[$row->status] ?? '-',
            optional($row->bill)->fee ?? '-'
        ];
    }

    /**
     * @inheritDoc
     */
    public function headings(): array
    {
        return [
            '序号',
            '姓名',
            '身份证号',
            '工种',
            '生效日期',
            '批改类型',
            '状态',
            '保费'
        ];
    }

    /**
     * @return array
     */
    public function columnWidths(): array
    {
        return [
            'A' => 6,
            'B' => 14,
            'C' => 25,
            'D' => 10,
            'E' => 17,
            'F' => 10,
            'G' => 10,
            'H' => 12,
        ];
    }
}
