<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Events\AfterSheet;

class PolicyGroupEndorseExport implements FromArray, WithTitle, WithHeadings
{
    protected $endorses;

    protected $title;

    /**
     * 导出字段.
     *
     * @var string[]
     */
    protected $heading = ['流水号', '批单号'];

    /**
     * 导出头
     *
     * @return array
     */
    public function headings(): array
    {
        return $this->heading ?? [];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return '批单数据-'.$this->title;
    }

    /**
     * PolicyGroupEndorseExport constructor.
     * @param $endorses
     * @param $title
     */
    public function __construct($endorses, $title)
    {
        $this->endorses = $endorses;

        $this->title = $title;
    }

    /**
     * Register events.
     *
     * @return  array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }

    public function array(): array
    {
        $cellData = [];
        foreach ($this->endorses as $endorse) {
            $cellData[] = [
                $endorse->batch_no,
                $endorse->endorse_no,
            ];
        }

        return $cellData;
    }
}
