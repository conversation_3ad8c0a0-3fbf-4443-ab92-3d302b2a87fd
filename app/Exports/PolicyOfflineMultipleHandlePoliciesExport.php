<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class PolicyOfflineMultipleHandlePoliciesExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{

    protected $policyNos;

    protected $heading = ['保单号'];

    public function __construct($policyNos)
    {
        $this->policyNos = $policyNos;
    }

    /**
     * 设置A-B列单元格格式
     *
     * @param Cell $cell
     * @param mixed $value
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A', 'B'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return $this->heading ?? [];
    }


    /**
     * Summary of array
     *
     * @return array
     */
    public function array(): array
    {
        $data = [];
        foreach ($this->policyNos as $policyNo) {
            $data[] = [$policyNo];
        }
        return $data;
    }
}
