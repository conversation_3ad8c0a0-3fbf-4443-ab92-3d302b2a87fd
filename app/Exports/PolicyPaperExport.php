<?php

namespace App\Exports;

use App\Models\PolicyPaper;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;

class PolicyPaperExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{
    /**
     * 纸质保单数据.
     *
     * @var \Illuminate\Database\Eloquent\Collection
     */
    protected $applies;

    /**
     * 导出实例.
     *
     * @param  \Illuminate\Database\Eloquent\Collection  $applies
     *
     * @return  void
     */
    public function __construct($applies)
    {
        $this->applies = $applies;
    }

    /**
     * 导出头
     *
     * @return array
     */
    public function headings():array
    {
        return ['系统流水号', '保单号', '投保人', '出单公司', '快递公司', '快递单号', '单证流水号', '快递类型', '接收人', '接收人电话', '接收人地址', '申请时间', '打印时间', '状态'];
    }

    /**
     * Register events.
     *
     * @return  array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }

    /**
     * Bind value.
     *
     * @param   Cell  $cell
     * @param   mixed  $value
     *
     * @return  bool
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['B', 'F', 'G', 'J'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

    /**
     * 导出数据.
     *
     * @return array
     */
    public function array(): array
    {
        $cellData = [];
        foreach ($this->applies as $value) {
            // '系统流水号', '保单号', '投保人', '出单公司', '快递公司', '快递单号', '单证流水号', '快递类型', '接收人', '接收人电话', '接收人地址'	, '申请时间', '打印时间', '状态'
            $cellData[] = [
                $value['policy']['order_no'],
                $value['policy']['policy_no'],
                $value['policy']['policyholder'],
                $value['policy']['companyBranch']['name'] ?? '',
                $value['express_company'],
                $value['express_no'],
                $value['order_no'],
                PolicyPaper::$types[$value['type']],
                $value['recipient'],
                $value['phone'],
                $value['address'],
                $value['created_at'],
                $value['operator_at'],
                PolicyPaper::$status[$value['status']],
            ];
        }
        return $cellData;
    }
}
