<?php

namespace App\Exports\RankingSheets;

class ClaimFinishedRate extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '按结案率排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '结案率(%)',
            '出险案件数',
            '理赔时长(天)'
        ];
    }

    /**
     * sheet 数据
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['claim_finished_rate'],
                $item['claims_count'],
                $item['claim_days']
            ];
        }

        return $items;
    }
}
