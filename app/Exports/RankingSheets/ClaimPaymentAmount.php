<?php

namespace App\Exports\RankingSheets;

class ClaimPaymentAmount extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '按赔款排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '已决赔款(元)',
            '未决赔款(元)',
            '赔款金额(元)'
        ];
    }

    /**
     * sheet 数据
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['claim_finished_settlement_payment_amount'],
                $item['claim_pending_settlement_amount'],
                $item['claim_payment_amount']
            ];
        }

        return $items;
    }
}
