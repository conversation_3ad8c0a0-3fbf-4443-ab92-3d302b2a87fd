<?php

namespace App\Exports\RankingSheets;

class ClaimPaymentRate extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '按赔付率排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '赔付率(%)',
            '出险率(‰)',
            '赔款金额(元)'
        ];
    }

    /**
     * sheet 数据
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['claim_payment_rate'],
                $item['claim_rate'],
                $item['claim_payment_amount']
            ];
        }

        return $items;
    }
}
