<?php

namespace App\Exports\RankingSheets;

class ClaimRate extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '按出险率排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '出险率(‰)',
            '赔付率(%)',
            '累计保费(元)'
        ];
    }

    /**
     * sheet 数据
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['claim_rate'],
                $item['claim_payment_rate'],
                $item['premium']
            ];
        }

        return $items;
    }
}
