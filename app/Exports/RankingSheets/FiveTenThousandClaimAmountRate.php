<?php

namespace App\Exports\RankingSheets;

class FiveTenThousandClaimAmountRate extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '5万元以上理赔案件排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '5万元以上理赔案件数',
            '出险案件数',
            '占比(%)'
        ];
    }

    /**
     * sheet 数据
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['claim_five_ten_thousand_amount_count'],
                $item['claims_count'],
                $item['claim_five_ten_thousand_amount_rate']
            ];
        }

        return $items;
    }
}
