<?php

namespace App\Exports\RankingSheets;

class PolicyCount extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '按投保单数排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '投保单数',
            '出险案件(件)',
            '出险率(‰)'
        ];
    }

    /**
     * sheet 数据
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['policies_count'],
                $item['claims_count'],
                $item['claim_rate']
            ];
        }

        return $items;
    }
}
