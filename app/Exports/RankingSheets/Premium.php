<?php

namespace App\Exports\RankingSheets;

use Illuminate\Support\Arr;

class Premium extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '按保费排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '累计保费(元)',
            '费率均值(‱)',
            '赔付率(%)'
        ];
    }


    /**
     * sheet 数据
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['premium'],
                $item['rate_avg'],
                $item['claim_payment_rate']
            ];
        }

        return $items;
    }
}
