<?php

namespace App\Exports\RankingSheets;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStrictNullComparison;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

abstract class RankingSheet extends DefaultValueBinder implements
    WithTitle,
    WithHeadings,
    WithCustomValueBinder,
    WithStrictNullComparison,
    FromArray,
    ShouldAutoSize
{
    public function __construct(protected string $label, protected array $data)
    {
    }

    /**
     * 设置值格式
     *
     * @param  Cell  $cell
     * @param  mixed  $value
     *
     * @return bool
     */
    public function bindValue(Cell $cell, $value)
    {
        if ($cell->getColumn() !== 'A' && is_numeric($value)) {
            $cell->setValueExplicit($value, DataType::TYPE_NUMERIC);

            return true;
        }

        return parent::bindValue($cell, $value);
    }
}
