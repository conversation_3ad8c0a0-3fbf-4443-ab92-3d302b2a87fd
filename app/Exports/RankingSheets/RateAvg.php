<?php

namespace App\Exports\RankingSheets;

class RateAvg extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '按费率均值排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '费率均值(‱)',
            '费率均值年化增长率(%)',
            '赔付率(%)'
        ];
    }


    /**
     * sheet 数据
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['rate_avg'],
                $item['rate_yearly_increase_rate'],
                $item['claim_payment_rate']
            ];
        }

        return $items;
    }
}
