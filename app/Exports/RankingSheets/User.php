<?php

namespace App\Exports\RankingSheets;

class User extends RankingSheet
{
    /**
     * Sheet 名称
     *
     * @return string
     */
    public function title(): string
    {
        return '按客户数排名';
    }

    /**
     * 表头
     *
     * @return array
     */
    public function headings(): array
    {
        return [
            $this->label,
            '客户数',
            '有效客户数',
            '年均保费万元以上客户数'
        ];
    }


    /**
     * sheet 数据
     *
     * @return array
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->data as $item) {
            $items[] = [
                $item['name'],
                $item['users_count'],
                $item['valid_users_count'],
                $item['ten_thousand_premium_count']
            ];
        }

        return $items;
    }
}
