<?php

namespace App\Exports;

use App\Models\Settlement;
use Maatwebsite\Excel\Concerns\FromQuery;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class SettlementExport extends DefaultValueBinder implements FromQuery, WithCustomValueBinder, WithHeadings, WithTitle, WithMapping
{

    protected $settlements;

    protected $sheetTitle;

    protected $heading = ['流水号', '申请人', '保单数', '保单总金额', '水单数', '水单总金额', '付款人', '收款人', '申请时间', '处理时间', '状态'];

    public function __construct($settlements, $sheetTitle = '销账数据')
    {
        if ($settlements instanceof Collection) {
            $settlements = $settlements->toArray();
        }

        $this->settlements = $settlements;

        $this->sheetTitle = $sheetTitle;
    }

    public function title(): string
    {
        return $this->sheetTitle;
    }

    public function headings(): array
    {
        return $this->heading;
    }

    public function query()
    {
        return Settlement::query()
            ->whereIn('id', array_column($this->settlements, 'id'))
            ->withCount('receivables')
            ->with([
                'memos:id,payer'
            ]);
    }

    /**
     * 设置列单元格格式
     *
     * @param Cell $cell
     * @param mixed $value
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A', 'C', 'D', 'E', 'F'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        return parent::bindValue($cell, $value);
    }

    public function map($data): array
    {
        return [
            $data['order_no'],
            $data['apply']['name'] ?? '',
            $data['receivables_count'],
            readable_amount($data['policy_amount']),
            $data['memos']->count(),
            readable_amount($data['memo_amount']),
            $data['memos'][0]['payer'] ?? '',
            $data['payee']['name'],
            $data['created_at'],
            $data['operated_at'],
            Settlement::$statusesText[$data['status']],
        ];
    }
}
