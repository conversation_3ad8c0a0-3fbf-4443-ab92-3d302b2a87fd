<?php

namespace App\Exports;

use App\Models\Policy;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class SettlementMemosExport extends  DefaultValueBinder implements FromArray, WithCustomValueBinder, WithHeadings, WithTitle
{

    protected $data;

    protected $sheetTitle;

    protected $heading = ['水单号', '水单金额', '录入人', '付款时间', '付款人', '收款人', '备注'];

    public function __construct($data, $sheetTitle = '销账水单数据')
    {
        $this->data = $data;

        $this->sheetTitle = $sheetTitle;
    }

    public function title(): string
    {
        return $this->sheetTitle;
    }

    public function headings(): array
    {
        return $this->heading;
    }

    /**
     * 设置列单元格格式
     *
     * @param Cell $cell
     * @param mixed $value
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        return parent::bindValue($cell, $value);
    }

    public function array(): array
    {
        return $this->memosFields();
    }

    /**
     * 水单数据
     *
     * @return array
     */
    protected function memosFields()
    {
        $items = [];
        foreach ($this->data as $data) { // '水单号', '水单金额', '录入人', '付款时间', '付款人', '收款人', '备注'
            $items[] = [
                $data['order_no'],
                readable_amount($data['amount']),
                $data['entry']['name'] ?? '',
                $data['paid_at'] ?? '',
                $data['payer'] ?? '',
                $data['payee']['name'] ?? '',
                $data['remark'] ?? '',
            ];
        }
        return $items;
    }
}


