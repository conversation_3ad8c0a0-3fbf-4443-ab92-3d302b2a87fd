<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class SettlementMultipleSheelsExport implements WithMultipleSheets
{
    protected $settlements;


    /**
     * Create settlements export instance.
     *
     * InvoiceExport constructor.
     * @param $data
     */
    public function __construct($data)
    {
        $this->settlements = $data;
    }

    public function sheets(): array
    {
        $sheets = [];

        $sheets[0] = new SettlementExport($this->settlements);

//        foreach ($this->settlements as $settlement){
//            $sheets[] = new SettlementPoliciesExport($settlement['receivables'], $settlement['order_no']);
//        }

        return $sheets;
    }
}
