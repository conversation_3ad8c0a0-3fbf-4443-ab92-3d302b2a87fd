<?php

namespace App\Exports;

use App\Models\Policy;
use App\Models\PremiumReceivable;
use App\Services\Finance\CreateFinances;
use Illuminate\Database\Eloquent\Collection;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\FromQuery;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class SettlementPoliciesExport extends DefaultValueBinder implements FromQuery, WithCustomValueBinder, WithHeadings, WithTitle, WithMapping, WithChunkReading
{

    protected $receivables;

    protected $settlementId;

    protected $sheetTitle;

    protected $heading = ['保单号', '出单公司', '险种', '保险金额', '用户保费', '币种', '投保人', '被保人', '提/运单号', '保单状态', '投保用户', '投保时间', '出单日期'];

    public function __construct($receivables = null, $sheetTitle = '销账保单数据', $settlementId = null)
    {
        if ($receivables instanceof Collection) {
            $receivables = $receivables->toArray();
        }
        $this->settlementId = $settlementId;

        $this->receivables = $receivables;

        $this->sheetTitle = $sheetTitle;
    }

    public function title(): string
    {
        return $this->sheetTitle;
    }

     // 添加分块大小配置
    public function chunkSize(): int
    {
        return 1000; // 根据服务器内存调整
    }

    public function headings(): array
    {
        return $this->heading;
    }

    /**
     * 设置列单元格格式
     *
     * @param Cell $cell
     * @param mixed $value
     * @return bool
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['A'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        return parent::bindValue($cell, $value);
    }

    public function query()
    {
        return PremiumReceivable::query()
            ->when($this->receivables, function ($q) {
                $q->whereIn('id', array_column($this->receivables, 'id'));
            })
            ->when($this->settlementId, function ($q) {
                $q->where('settlement_id', $this->settlementId);
            })
            ->select('id', 'type', 'policy_id', 'user_id', 'user_source', 'company_branch_id', 'user_premium', 'agent_premium', 'receivable')
            ->with([
                'policy' => fn($q) => $q->with([
                    'policyCargo' => fn($q) => $q->select('id', 'policy_id', 'coverage_currency_id', 'waybill_no')->with(['coverageCurrency:id,name']),
                    'policyOffline' => fn($q) => $q->select('id', 'policy_id', 'category_id')->with('category:id,name'),
                ]),
                'user:id,agent_id,name',
                'companyBranch:id,name'
            ]);
    }


    public function map($row): array
    {
        return [
            $row['policy']['policy_no'],
            $row['companyBranch']['name'] ?? '',
            $row['policy']['type'] === Policy::TYPE_OFFLINE ? $row['policy']['policyOffline']['category']['name'] . '-线下录入' ?? '' : Policy::$types[$row['policy']['type']],
            readable_amount($row['policy']['coverage']) ?? '',
            readable_amount($this->getSettlementPremium($row['policy'], $row)),
            in_array($row['policy']['type'], [Policy::TYPE_DOMESTIC, Policy::TYPE_INTL, Policy::TYPE_CBEC]) ? $row['policy']['policyCargo']['coverageCurrency']['name'] : '人民币',
            $row['policy']['policyholder'],
            $row['policy']['insured'],
            $row['policy']['type'] !== Policy::TYPE_OFFLINE ? $row['policy']['policyCargo']['waybill_no'] : '-',
            Policy::$statusesText[$row['policy']['status']],
            $row['user']['name'] ?? '-',
            $row['policy']['submitted_at'],
            $row['policy']['issued_at'] ?? '',
        ];
    }


    protected function getSettlementPremium($policy, $receivable)
    {
        $settlementPremium = $receivable['user_premium'];
        if ($policy['user']['agent_id'] !== -1) {
            $settlementPremium = $receivable['agent_premium'];
        }
        if ($policy['is_premium_sync'] === 1) {
            $settlementPremium = $receivable['user_premium'];
        }
        if ($receivable['type'] == CreateFinances::TYPE_CANCEL && $settlementPremium > 0) {
            $settlementPremium = -$settlementPremium;
        }
        if (in_array($receivable['type'], [CreateFinances::TYPE_MODIFY])) {
            $settlementPremium = $receivable['receivable'];
        }
        return $settlementPremium;
    }
}
