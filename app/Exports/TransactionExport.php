<?php

namespace App\Exports;

use App\Models\PlatformTransaction;
use App\Models\Policy;
use App\Models\PolicyGroup;
use App\Models\Product;
use App\Models\Ticket;
use App\Models\Transaction;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Events\AfterSheet;

class TransactionExport implements FromArray, WithHeadings
{
    /**
     * 保单数据.
     *
     * @var \Illuminate\Support\Collection
     */
    protected $transactions;

    /**
     * Create transactions export instance.
     *
     * @param   mixed  $transactions
     *
     * @return  void
     */
    public function __construct($transactions)
    {
        $this->transactions = $transactions;
    }

    /**
     * 导出头部
     *
     * @return  array
     */
    public function headings(): array
    {
        return [
            '支出保单',
            '支出用户',
            '类型',
            '金额',
            '余额',
            '备注',
            '时间',
        ];
    }

    /**
     * Register events.
     *
     * @return  array
     */
    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function (AfterSheet $event) {
                $event->sheet->getDelegate()->freezePane('A2');
            },
        ];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function array(): array
    {
        $items = [];
        foreach ($this->transactions as $item) {

            $items[] = [
                $item['policy']['policy_no'] ?? '',
                $item['policy']['user']['name'] ?? '',
                $this->type($item),
                readable_amount($item['amount']),
                readable_amount($item['balance']),
                $item['remark'],
                $item['created_at']->format('Y-m-d H:i:s')
            ];
        }

        return $items;
    }

    /**
     * 交易类型
     *
     * @param   PlatformTransaction  $item
     *
     * @return  string
     */
    protected function type(PlatformTransaction $item)
    {
        switch ($item['type']) {
            case PlatformTransaction::TYPE_CHARGE:
                return '充值';
            case PlatformTransaction::TYPE_PAY:
                return '扣费';
            case PlatformTransaction::TYPE_REFUND:
                return '退款';
            default:
                break;
        }
    }

}
