<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithCustomValueBinder;
use Maatwebsite\Excel\Concerns\WithHeadings;
use PhpOffice\PhpSpreadsheet\Cell\Cell;
use PhpOffice\PhpSpreadsheet\Cell\DataType;
use PhpOffice\PhpSpreadsheet\Cell\DefaultValueBinder;

class UserExport extends DefaultValueBinder implements FromArray, WithHeadings, WithCustomValueBinder
{

    protected $users;

    protected $heading = ['客户名称', '用户名', '联系方式', '邮箱', '证件号', '角色', '余额', '欠款', '是否启用', '开通人', '业务员', '创建时间'];

    public function headings():array
    {
        return $this->heading;
    }

    /**
     * Bind value.
     *
     * @param   Cell  $cell
     * @param   mixed  $value
     *
     * @return  bool
     */
    public function bindValue(Cell $cell, $value)
    {
        if (in_array($cell->getColumn(), ['C', 'E'])) {
            $cell->setValueExplicit($value, DataType::TYPE_STRING);

            return true;
        }

        // else return default behavior
        return parent::bindValue($cell, $value);
    }

    public function __construct($data)
    {
        $this->users = $data;
    }

    public function array():array
    {
        return $this->collection();
    }

    /**
     * @return array
     */
    public function collection()
    {
        $items = [];
        foreach ($this->users as $data){
            $items[] = [
                $data['name'],
                $data['username'],
                $data['phone_number'],
                $data['email'],
                $data['idcard_no'],
                $data['is_agent'] ? '代理商' : '会员',
                readable_amount($data['balance']),
                $data['arrears'] > 0 ? readable_amount($data['arrears']) : '0',
                $data['is_enabled'] ? '启用' : '禁用',
                $data['agent_id'] == -1 ? $data['salesman']['name'] : $data['agent']['name'],
                $data['salesman']['name'] ?? '',
                $data['created_at'],
            ];
        }
        return $items;
    }
}
