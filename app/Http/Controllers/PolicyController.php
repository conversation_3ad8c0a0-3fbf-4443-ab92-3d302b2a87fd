<?php

namespace App\Http\Controllers;

use App\Exports\MixedPolicyExport;
use App\Exports\PolicyExport;
use App\Http\Resources\Invoice\CanInvoicePoliciesResource;
use App\Http\Resources\Invoice\CanInvoicePolicyGroupEndorseResource;
use App\Http\Resources\Policy\HistoryPoliciesResource;
use App\Http\Resources\Policy\HistoryPolicyResource;
use App\Http\Resources\Policy\MixedPoliciesResource;
use App\Http\Resources\Policy\PoliciesResource;
use App\Http\Resources\Policy\PolicyResource;
use App\Http\Resources\Policy\PolicySearchingResource;
use App\Models\Policy;
use App\Models\PolicyGroupEndorse;
use App\Models\PolicyLog;
use App\Services\Policy\Audit;
use App\Services\Policy\CreateInquiry;
use App\Services\Policy\Download;
use App\Services\Policy\ExportCpicWord;
use App\Services\Policy\ResubmitPolicy;
use App\Services\Policy\SendBack;
use App\Support\AutoInsure;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\Previous\Policy as PreviousPolicy;
use App\Services\Policy\DownloadZip;
use Illuminate\Validation\Rule;

class PolicyController extends Controller
{
    /**
     * 保单数据.
     *
     * @param   Policy  $policy
     *
     * @return  \Illuminate\Http\Resources\Json\JsonResource
     */
    public function index(Policy $policy)
    {
        $policies = $policy->getPolicies();

        return new PoliciesResource($policies);
    }

    /**
     * 保单综合查询.
     *
     * @param   Policy  $policy
     *
     * @return  \Illuminate\Http\Resources\Json\JsonResource
     */
    public function mixed(Policy $policy)
    {
        $policies = $policy->getMixedPolicies();

        return new MixedPoliciesResource($policies);
    }

    /**
     * 保单综合查询导出.
     *
     * @param   Policy  $policy
     *
     * @return  \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function mixedExport(Policy $policy)
    {
        $policies = $policy->getMixedPolicies(false, true);

        return Excel::download(new MixedPolicyExport($policies), '保单综合查询导出 - ' . date('Y-m-d H:i:s') . '.xlsx');
    }

    /**
     * 导出保单.
     *
     * @param   Policy  $policy
     *
     * @return  \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Policy $policy)
    {
        $policies = $policy->getPolicies(null, null, false);

        return Excel::download(
            new PolicyExport($policies, request('filter.type')),
                $policy::$types[request('filter.type')] . '保单导出 - ' . date('Y-m-d H:i:s') . '.xlsx'
        );
    }

    /**
     * 导出保单.
     *
     * @param   Policy  $policy
     * @param   Request  $request
     * @param   DownloadZip  $downloadZip
     *
     * @return  \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadZip(Policy $policy, Request $request, DownloadZip $downloadZip)
    {
        if ($request->input('from', null) === 'mixed') {
            $policies = $policy->getMixedPolicies(false);
        } else {
            $policies = $policy->getPolicies(null, null, false);
        }

        return $downloadZip->handle($policies);
    }

    /**
     * 保单状态.
     *
     * @param   Policy  $policy
     *
     * @return  \Illuminate\http\Resources\Json\JsonResource
     */
    public function statuses(Policy $policy)
    {
        $statuses = $policy->countStatuses();

        return new JsonResource($statuses->keyBy('status'));
    }

    /**
     * 获取保单详情.
     *
     * @param   Policy  $policy
     * @param   int     $id
     *
     * @return  \Illuminate\Http\Resources\Json\JsonResource
     */
    public function show(Policy $policy, int $id)
    {
        $policy = $policy->getPolicy($id);

        return new PolicyResource($policy);
    }

    /**
     * 下载保单.
     *
     * @param   Policy    $policy
     * @param   Download  $download
     * @param   int       $id
     *
     * @return  \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function download(Policy $policy, Download $download, int $id)
    {
        $policy = $policy->where(function ($q) {
            $q->ofPlatform()->ofMe();
        })->findOrFail($id);

        return $download->handle($policy);
    }

    /**
     * 绑定工单到审核员.
     *
     * @param   Policy  $policy
     * @param   int     $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function associateAuditor(Policy $policy, int $id)
    {
        $policy = $policy->where(function ($q) {
            $q->ofPlatform()->ofMe();
        })->findOrFail($id);

        if ($policy['auditor_id'] === -1) {
            $policy->fill([
                'auditor_id' => Auth::id(),
                'status' => Policy::STATUS_IN_REVIEW,
            ])->save();

            $policy->policyLogs()->create([
                'type' => PolicyLog::TYPE_RECEIVE,
                'admin_id' => Auth::id(),
                'content' => '领取保单'
            ]);
        }

        return response()->noContent();
    }

    /**
     * 保单退回.
     *
     * @param Request $request
     * @param Policy $policy
     * @param SendBack $sendBack
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function sendBack(Request $request, Policy $policy, SendBack $sendBack, int $id)
    {
        $attributes = $request->validate([
            'sendback_reason' => 'required|min:1|max:255',
        ]);
        $policy = $policy->where(function ($q) {
            $q->ofPlatform()->ofMe();
        })->findOrFail($id);

        $sendBack->handle($policy, $attributes['sendback_reason']);

        $policy->policyLogs()->create([
            'admin_id' => Auth::id(),
            'type' => PolicyLog::TYPE_SENDBACK,
            'content' => '退回保单: ' . $attributes['sendback_reason'],
            'payload' => $request->all(),
        ]);

        return response()->noContent();
    }

    /**
     * 保单退回(补充资料).
     *
     * @param Request $request
     * @param Policy $policy
     * @param SendBack $sendBack
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function sendBackSupplementInfo(Request $request, Policy $policy, SendBack $sendBack, int $id)
    {
        $attributes = $request->validate([
            'sendback_reason' => 'required|min:1|max:255',
        ]);
        $policy = $policy->where(function ($q) {
            $q->ofPlatform()->ofMe();
        })->findOrFail($id);

        $sendBack->handle($policy, $attributes['sendback_reason'], true);

        $policy->policyLogs()->create([
            'admin_id' => Auth::id(),
            'type' => PolicyLog::TYPE_SENDBACK_MISS_INFO,
            'content' => '退回保单(补充资料): ' . $attributes['sendback_reason'],
            'payload' => $request->all(),
        ]);

        return response()->noContent();
    }

    /**
     * 未支付退回
     *
     * @param \Illuminate\Http\Request $request
     * @param \App\Models\Policy $policy
     * @param \App\Services\Policy\SendBack $sendBack
     * @param int $id
     * @return mixed|\Illuminate\Http\Response
     */
    public function sendBackUnpaid(Request $request, Policy $policy, SendBack $sendBack, int $id)
    {
        $attributes = $request->validate([
            'sendback_reason' => 'required|min:1|max:255',
        ]);
        $policy = $policy->where(function ($q) {
            $q->ofPlatform()->ofMe();
        })->findOrFail($id);

        $sendBack->handle($policy, $attributes['sendback_reason'], false, true);

        $policy->policyLogs()->create([
            'admin_id' => Auth::id(),
            'type' => PolicyLog::TYPE_SENDBACK,
            'content' => '退回未支付单票: ' . $attributes['sendback_reason'],
            'payload' => $request->all(),
        ]);

        return response()->noContent();
    }

    /**
     * 重新提交.
     *
     * @param   Policy  $policy
     * @param   ResubmitPolicy  $resubmit
     * @param   int     $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function resubmit(Policy $policy, ResubmitPolicy $resubmit, int $id)
    {
        $policy = $policy->with(['policyCargo', 'product'])->findOrfail($id);

        $resubmit->handle($policy);

        $policy->policyLogs()->create([
            'admin_id' => Auth::id(),
            'type' => PolicyLog::TYPE_RESUBMIT,
            'content' => '重提保单到自动录单'
        ]);

        return response()->noContent();
    }

    /**
     * 审核保单.
     *
     * @param   Request  $request
     * @param   Audit    $audit
     * @param   int      $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function audit(Request $request, Audit $aduit, int $id)
    {
        $attributes = $request->validate([
            'is_automatically' => 'nullable|boolean',
            'option' => 'required_if:is_automatically,1',
            'policy_no' => ['nullable', 'required_if:is_automatically,0', Rule::unique('policies', 'policy_no')->ignore($id)],
            'policy_file' => 'nullable|required_if:is_automatically,0|file',
            'product_type' => 'nullable',
            'rate' => 'nullable|numeric',
            'premium' => 'nullable|numeric',
            'platform_rate' => 'nullable|numeric',
            'platform_premium' => 'nullable|numeric',
            'agent_rate' => 'nullable|numeric',
            'agent_premium' => 'nullable|numeric',
            'user_rate' => 'nullable|numeric',
            'user_premium' => 'nullable|numeric',
            'service_charge' => 'nullable|numeric',
            'platform_commission_rate' => 'nullable|numeric',
            'agent_commission_rate' => 'nullable|numeric',
            'special' => 'nullable|string',
            'deductible' => 'nullable|string',
            'custom_deductible' => 'nullable|string'
        ]);

        $policy = $aduit->handle($id, $attributes);

        $policy->policyLogs()->create([
            'admin_id' => Auth::id(),
            'type' => PolicyLog::TYPE_AUDIT,
            'content' => $attributes['is_automatically'] ? '审核保单，并提交自动录单' : '审核保单，保单号：' . $policy['policy_no'],
            'payload' => $request->all(),
        ]);

        return response()->noContent();
    }

    /**
     * 获取可开发票的保单
     *
     * @param Request $request
     * @param Policy $policy
     * @param PolicyGroupEndorse $groupEndorse
     * @return CanInvoicePoliciesResource|CanInvoicePolicyGroupEndorseResource
     */
    public function getCanInvoicePolicies(Request $request, Policy $policy, PolicyGroupEndorse $groupEndorse)
    {
        $isGroupPolicy = (int) $request->input('filter.type', -1) === Policy::TYPE_GROUP;

        if ($isGroupPolicy) {
            $endorses = $groupEndorse->getCanInvoiceData();

            return new CanInvoicePolicyGroupEndorseResource($endorses);
        } else {
            $policies = $policy->getCanInvoicePolicies($isGroupPolicy);

            return new CanInvoicePoliciesResource($policies);
        }
    }

    /**
     * 历史保单
     *
     * @param  PreviousPolicy $policy
     *
     * @return HistoryPoliciesResource
     */
    public function histories(PreviousPolicy $policy)
    {
        $policies = $policy->getPolicies();

        return new HistoryPoliciesResource($policies);
    }

    /**
     * 历史保单.
     *
     * @param   PreviousPolicy   $policy
     * @param   int   $id

     * @return  \Illuminate\Http\Response
     */
    public function historyDetail(PreviousPolicy $policy, int $id)
    {
        $policy = $policy->getPolicy($id);

        return new HistoryPolicyResource($policy);
    }

    /**
     * 保单号查询保单
     *
     * @param   Request  $request
     * @param   Policy   $policy
     *
     * @return  \Illuminate\Http\Resources\Json\JsonResource
     */
    public function searching(Request $request, Policy $policy)
    {
        $attributes = $request->validate([
            'policy_no' => 'required|string'
        ]);

        $policy = $policy->findByPolicyNo($attributes['policy_no']);

        return new PolicySearchingResource($policy);
    }

    /**
     * 导出太保word投保单
     *
     * @param \App\Services\Policy\ExportCpicWord $exportCpicWord
     * @param \App\Models\Policy $policy
     * @param mixed $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportCpicWord(ExportCpicWord $exportCpicWord, Policy $policy, $id)
    {
        $policy = $policy->findOrFail($id);

        $path = $exportCpicWord->handle($policy);

        $fileName = $policy['order_no'] . '.docx';

        return response()
            ->download($path, $fileName, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            ])
            ->deleteFileAfterSend(true);
    }

    /**
     * 创建暂存单
     *
     * @param  CreateInquiry $creator
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function createInquiry(CreateInquiry $creator, int $id)
    {
        $creator->handle($id);

        return response()->noContent(201);
    }
}
