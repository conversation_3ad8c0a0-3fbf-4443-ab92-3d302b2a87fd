<?php

namespace App\Http\Controllers;

use App\Exports\GroupStaff;
use App\Exports\PolicyGroupEmployeeExport;
use App\Http\Resources\PolicyGroup\PolicyGroupEmployeesResource;
use App\Models\PolicyGroupEmployee;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PolicyGroupEmployeeController extends Controller
{
    /** @var PolicyGroupEmployee  */
    protected $policyGroupEmployee;

    public function __construct(PolicyGroupEmployee $policyGroupEmployee)
    {
        $this->policyGroupEmployee = $policyGroupEmployee;
    }

    /**
     * 在保/审批人员列表
     *
     * @param int $policyGroupId
     * @return PolicyGroupEmployeesResource
     */
    public function index(int $policyGroupId)
    {
        $employees = $this->policyGroupEmployee->getListByPolicyGroupId($policyGroupId);

        return new PolicyGroupEmployeesResource($employees);
    }

    /**
     * 在保人员导出
     *
     * @param int $policyGroupId
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportList(int $policyGroupId)
    {
        return Excel::download(new PolicyGroupEmployeeExport($policyGroupId), "雇主【". date('YmdHis') ."】 人员清单.xlsx");
    }
}
