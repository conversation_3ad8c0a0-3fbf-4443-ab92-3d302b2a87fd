<?php

namespace App\Http\Controllers;

use App\Http\Resources\PolicyGroup\EndorseEmployeesResource;
use App\Http\Resources\PolicyGroup\PolicyGroupEndorsesResource;
use App\Models\PolicyGroupEmployee;
use App\Models\PolicyGroupEndorse;
use App\Services\PolicyGroup\EndorseDownload;
use App\Services\PolicyGroup\ProcessPolicyGroupEndorse;
use Illuminate\Http\Request;

class PolicyGroupEndorseController extends Controller
{
    /**
     * @var PolicyGroupEndorse
     */
    protected $policyGroupEndorse;

    public function __construct(PolicyGroupEndorse $policyGroupEndorse)
    {
        $this->policyGroupEndorse = $policyGroupEndorse;
    }

    /**
     * 批单列表
     *
     * @param int $policyGroupId
     * @return PolicyGroupEndorsesResource
     */
    public function index(int $policyGroupId)
    {
        $list = $this->policyGroupEndorse->getListByPolicyGroupId($policyGroupId);

        return new PolicyGroupEndorsesResource($list);
    }

    /**
     * 批单详情
     *
     * @param int $endorseId
     * @return PolicyGroupEndorsesResource
     */
    public function show(int $endorseId)
    {
        $endorse = $this->policyGroupEndorse->newQuery()->findOrFail($endorseId);

        return new PolicyGroupEndorsesResource($endorse);
    }

    /**
     * 批单审批
     *
     * @param Request $request
     * @param ProcessPolicyGroupEndorse $processPolicyGroupEndorse
     * @param int $endorseId
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProcessPolicyGroupEndorse $processPolicyGroupEndorse, int $endorseId)
    {
        $attributes = $request->validate([
            'status' => 'required|in:' . implode(',', [PolicyGroupEndorse::STATUS_APPROVED, PolicyGroupEndorse::STATUS_REJECTED]),
            'content' => ['nullable', 'min:1', 'max:250'],
            'endorse_file' => 'nullable|file',
            'endorse_no' => 'nullable'
        ]);

        $endorse = $this->policyGroupEndorse->newQuery()->findOrFail($endorseId);
        $processPolicyGroupEndorse->handle($endorse, $attributes);

        return response()->noContent();
    }

    /**
     * 批单人员列表
     *
     * @param PolicyGroupEmployee $policyGroupEmployee
     * @param int $endorseId
     * @return EndorseEmployeesResource
     */
    public function employees(PolicyGroupEmployee $policyGroupEmployee, int $endorseId)
    {
        $employees = $policyGroupEmployee->getListByEndorseId($endorseId);

        return new EndorseEmployeesResource($employees);
    }

    /**
     * 下载批单文件.
     *
     * @param   Request  $request
     * @param   EndorseDownload  $endorseDownload
     * @param   int  $endorseId
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function download(Request $request, EndorseDownload $endorseDownload, int $endorseId)
    {
        $attributes = $request->validate([
            'file' => 'required|string|in:endorse_file,transaction_file',
        ]);

        return $endorseDownload->handle($endorseId, $attributes['file']);
    }
}
