<?php

namespace App\Http\Controllers;

use App\Http\Resources\PolicyGroup\PolicyGroupTransactionsResource;
use App\Models\PolicyGroup;
use App\Models\Transaction;
use Illuminate\Http\Resources\Json\JsonResource;

class PolicyGroupTransactionController extends Controller
{
    /**
     * 雇主保单支付记录
     *
     * @param Transaction $transaction
     * @param int $policyGroupId
     * @return JsonResource
     */
    public function getGroupRecords(Transaction $transaction, int $policyGroupId)
    {
        $list = $transaction->newQuery()
            ->where([
                'transaction_id' => $policyGroupId,
                'transaction_type' => PolicyGroup::class,
            ])
            ->latest()
            ->paginate();

        return new PolicyGroupTransactionsResource($list);
    }
}
