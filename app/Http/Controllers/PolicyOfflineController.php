<?php

namespace App\Http\Controllers;

use App\Exports\PolicyExport;
use App\Http\Resources\Policy\PoliciesResource;
use App\Http\Resources\Policy\PolicyOfflineFormResource;
use App\Http\Resources\Policy\PolicyOfflineResource;
use App\Http\Resources\Product\ProductsResource;
use App\Imports\PolicyOfflineImport;
use App\Models\Policy;
use App\Models\Product;
use App\Models\ProductOfflineCategory;
use App\Models\ProductOfflineTemplate;
use App\Services\Policy\Offline\ImportPolicyOffline;
use App\Services\Policy\Offline\ImportTempateGenerator\TemplatePath;
use App\Services\Policy\Offline\MultipleHandlePolicyOffline;
use App\Services\Policy\Offline\SurrenderPolicyOffline;
use App\Services\Policy\Offline\CreatePolicyOffline;
use App\Services\Policy\Offline\IssuePolicyOffline;
use App\Services\Policy\Offline\UpdatePolicyOffline;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class PolicyOfflineController extends Controller
{
    public function index(Policy $policy)
    {
        $ids = request()->input('ids');

        $data = $policy->getPolicies(Policy::TYPE_OFFLINE, null, true, $ids);

        return new PoliciesResource($data);
    }

    public function show($id)
    {
        $policy = Policy::findOrFail($id);

        return new PolicyOfflineResource($policy);
    }

    /**
     * 获取表单格式详情
     *
     * @param $id
     * @return PolicyOfflineFormResource
     */
    public function formData($id)
    {
        $policy = Policy::findOrFail($id);

        return new PolicyOfflineFormResource($policy);
    }

    /**
     * 可投保产品列表
     *
     * @param Product $product
     * @return ProductsResource
     */
    public function productList(Product $product)
    {
        $data = $product->getOfflineInsureProducts();

        return new ProductsResource($data);
    }

    /**
     * 添加线下录入保单
     *
     * @param Request $request
     * @param CreatePolicyOffline $createPolicyOffline
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, CreatePolicyOffline $createPolicyOffline)
    {
        $attributes = $request->validate([
            'salesman_id' => 'required',
            'company_id' => 'required',
            'company_branch_id' => 'required',
            'channel_id' => 'required',
            'category_id' => 'required',
            'user_id' => 'nullable',
            'policy_no' => 'required|unique:policies,policy_no,NULL,id,deleted_at,NULL',
            'policy_file' => 'nullable|file|mimes:pdf,jpg,xlsx,xls,zip',
            'policyholder' => 'required',
            'insured' => 'required',
            'subject' => 'required',
            'insure_date' => 'required',
            'coverage' => 'required',
            'coverage_currency_id' => 'required',
            'product_from' => 'required|in:1,2',
            'start_at' => 'required',
            'end_at' => 'required',
            'premium' => 'required',
            // 'premium_pay_type' => 'required|in:2',
            'settlement_type' => 'required|in:1,2',
            'business_type' => 'required|in:1,2,3',
            'commission_rate' => 'required_if:business_type,2',
            'business_from' => 'required_if:business_type,3',
            'poundage_rate' => 'required',
            // 'payee_type' => 'required_if:premium_pay_type,1',
            // 'memo_file' => 'required_if:premium_pay_type,1',
        ]);
        $category = ProductOfflineCategory::findOrFail($attributes['category_id']);

        $validate = $category->getValidate($category['id']);

        $attributes = array_merge($attributes, $request->validate($validate));

        $createPolicyOffline->handle($attributes, $category);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 线下录入保单更新
     *
     * @param Request $request
     * @param UpdatePolicyOffline $updatePolicyOffline
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, UpdatePolicyOffline $updatePolicyOffline, $id)
    {
        $attributes = $request->validate([
            'salesman_id' => 'nullable',
            'user_id' => 'nullable',
            'category_id' => 'required',
            'company_id' => 'required',
            'company_branch_id' => 'required',
            'channel_id' => 'required',
            'policy_no' => 'required|unique:policies,policy_no,' . $id . ',id,deleted_at,NULL',
            'policy_file' => 'nullable',
            'policyholder' => 'required',
            'insured' => 'required',
            'subject' => 'required',
            'insure_date' => 'required',
            'coverage' => 'required',
            'coverage_currency_id' => 'required',
            'product_from' => 'required|in:1,2',
            'start_at' => 'required',
            'end_at' => 'required',
            'premium' => 'required',
            // 'premium_pay_type' => 'required|in:2',
            'settlement_type' => 'required|in:1,2',
            'business_type' => 'required|in:1,2,3',
            'commission_rate' => 'required_if:business_type,2',
            'business_from' => 'required_if:business_type,3',
            'poundage_rate' => 'required',
            // 'payee_type' => 'required_if:premium_pay_type,1',
            // 'memo_file' => 'required_if:premium_pay_type,1',
        ]);
        $category = ProductOfflineCategory::findOrFail($attributes['category_id']);

        $validate = $category->getValidate($category['id']);

        $attributes = array_merge($attributes, $request->validate($validate));

        $policy = Policy::findOrFail($id);

        $updatePolicyOffline->handle($attributes, $policy, $category);

        return response()->noContent();
    }

    /**
     * 确认出单
     *
     * @param IssuePolicyOffline $issuePolicyOffline
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function identifyIssue(IssuePolicyOffline $issuePolicyOffline, $id)
    {
        $policy = Policy::findOrFail($id);

        $issuePolicyOffline->handle($policy);

        return response()->noContent();
    }

    /**
     * 退保
     *
     * @param Request $request
     * @param SurrenderPolicyOffline $surrenderPolicyOffline
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function cancel(Request $request, SurrenderPolicyOffline $surrenderPolicyOffline, $id)
    {
        $attributes = $request->validate([
            'surrender_reason' => 'required'
        ]);

        $policy = Policy::findOrFail($id);

        $surrenderPolicyOffline->handle($policy, $attributes);

        return response()->noContent();
    }

    /**
     * 删除未生效保单
     *
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function delete($id)
    {
        Policy::where('id', $id)->ofPlatform()->delete();

        return response()->noContent();
    }

    /**
     * 导出
     *
     * @param Policy $policy
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Policy $policy)
    {
        $policies = $policy->getPolicies(Policy::TYPE_OFFLINE, null, false);

        return Excel::download(
            new PolicyExport($policies, Policy::TYPE_OFFLINE),
            Policy::$types[Policy::TYPE_OFFLINE] . '保单导出 - ' . date('Y-m-d H:i:s') . '.xlsx'
        );
    }

    /**
     * 获取线下录入导入模板
     *
     * @param $categoryId
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function importTemplateExcel($categoryId)
    {
        $path = (new TemplatePath($categoryId))->handle();

        return response()
            ->download($path, '线下录入保单导入模板.xlsx', [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            ])
            ->deleteFileAfterSend(true);
    }

    /**
     * 保单导入
     *
     * @param Request $request
     * @param ImportPolicyOffline $importPolicyOffline
     * @param $categoryId
     * @return \Illuminate\Http\Response
     */
    public function import(Request $request, ImportPolicyOffline $importPolicyOffline, $categoryId)
    {
        $attributes = $request->validate([
            'file' => 'required|file|mimes:xls,xlsx',
        ]);

        $importPolicyOffline->handle($attributes, $categoryId);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 批量处理
     *
     * @param Request $request
     * @param MultipleHandlePolicyOffline $multipleHandlePolicyOffline
     * @return \Illuminate\Http\Response
     */
    public function multipleHandle(Request $request, MultipleHandlePolicyOffline $multipleHandlePolicyOffline, Policy $policy)
    {
        $attributes = $request->validate([
            'ids' => 'nullable',
            'type' => 'required|in:1,2',
        ]);

        if (empty($attributes['ids'])) {
            $data = $request->except(['type', 'all_checkout', 'x-platform-id', 'x-request-id']);

            $request->query->add(['filter' => $data]);

            $attributes['ids'] = $policy->getOfflinePolicyIds(Policy::TYPE_OFFLINE)->pluck('id')->toArray();
        }

        $multipleHandlePolicyOffline->handle($attributes);

        return response()->noContent();
    }
}
