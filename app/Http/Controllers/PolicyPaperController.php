<?php

namespace App\Http\Controllers;

use App\Exports\PolicyPaperExport;
use App\Http\Resources\PolicyPaper\PapersResource;
use App\Models\PolicyPaper;
use App\Services\PolicyPaper\Apply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class PolicyPaperController extends Controller
{
    /**
     * 纸质保单申请列表
     *
     * @param PolicyPaper $PolicyPaper
     * @return PapersResource
     */
    public function index(PolicyPaper $PolicyPaper)
    {
        $data = $PolicyPaper->getApplies();

        return new PapersResource($data);
    }

    /**
     * 纸质保单申请处理
     *
     * @param   Request $request
     * @param   Apply $apply
     * @param   int $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function handle(Request $request, Apply $apply, int $id)
    {
        $attributes = $request->validate([
            'express_company' => 'required',
            'express_no' => 'required',
            'order_no' => 'required',
        ]);

        $policyPaper = PolicyPaper::ofPlatform()
            ->ofSalesmanByPolicy()
            ->findOrFail($id);

        $apply->handle($policyPaper, $attributes);

        return response()->noContent();
    }

    public function sendBack(Request $request, $id)
    {
        $attributes = $request->validate([
            'reason' => 'required',
        ]);

        $policyPaper = PolicyPaper::ofPlatform()
            ->ofSalesmanByPolicy()
            ->findOrFail($id);

        DB::transaction(function () use ($policyPaper, $attributes){
            $policyPaper->update(array_merge($attributes, [
                'operator_id' => Auth::id(),
                'operator_at' => now(),
                'status' => PolicyPaper::STATUS_SEND_BACK
            ]));
            $policyPaper->policy()->update([
                'is_can_policy_paper' => true
            ]);
        });



        return response()->noContent();
    }

    /**
     * 纸质保单申请记录导出
     *
     * @param PolicyPaper $PolicyPaper
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(PolicyPaper $PolicyPaper)
    {
        $data = $PolicyPaper->getApplies();

        return Excel::download(new PolicyPaperExport($data), '纸质保单申请数据导出'.date('Y-m-d H:i:s').'.xlsx');
    }

}
