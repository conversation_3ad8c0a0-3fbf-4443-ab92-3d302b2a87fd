<?php

namespace App\Http\Controllers;

use App\Services\PolicyPaymentCallback\OnlineCallback;
use App\Services\PolicyPaymentCallback\ZhongyiCallback;
use Illuminate\Http\Request;
use Log;

class PolicyPaymentCallbackController extends Controller
{
    /**
     * 中意支付回调处理.
     *
     * @param   Request  $request
     * @param   ZhongyiCallback  $zhongyiCallback
     *
     * @return  void
     */
    public function zhongyipay(Request $request, ZhongyiCallback $zhongyiCallback)
    {
        $payload = $request->all();

        if (isset($payload[0]) && is_array($payload[0])) {
            $payload = $payload[0];
        }

        $zhongyiCallback->handle($payload);
    }

    /**
     * 在线支付回调
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \App\Services\PolicyPaymentCallback\OnlineCallback $onlineCallback
     * @return void
     */
    public function online(Request $request, OnlineCallback $onlineCallback)
    {
        $payload = $request->except(['x-request-id', 'x-platform-id']);

        Log::info('[Online Callback] - ', [$payload]);
        Log::info('[Online Callback X-Signatures] - ' . $request->header('X-Signatures') ?? '');

        $onlineCallback->handle($payload);
    }
}
