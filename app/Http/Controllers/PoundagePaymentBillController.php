<?php

namespace App\Http\Controllers;

use App\Exports\PaymentBillExport;
use App\Http\Resources\PoundagePayment\PoundagePaymentBillResource;
use App\Http\Resources\PoundagePayment\PoundagePaymentResource;
use App\Models\PoundagePayment;
use App\Models\PoundagePaymentBill;
use App\Services\Finance\CreatePaymentBills;
use App\Services\Finance\HandleDraftPaymentBills;
use App\Services\Finance\HandlePaymentBills;
use App\Services\Finance\SendBackBills;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class PoundagePaymentBillController extends Controller
{
    /**
     * 经纪费结算支付记录列表
     *
     * @param PoundagePaymentBill $poundagePaymentBill
     * @return PoundagePaymentBillResource
     */
    public function index(PoundagePaymentBill $poundagePaymentBill)
    {
        $bills = $poundagePaymentBill->getBills();

        return new PoundagePaymentBillResource($bills);
    }


    /**
     * 添加支付记录
     *
     * @param Request $request
     * @param CreatePaymentBills $createPaymentBills
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, CreatePaymentBills $createPaymentBills)
    {
        $attributes = $request->validate([
            'ids' => 'required',
            'proof' => 'required|file',
            'premium' => 'required',
            'poundage' => 'required',
            'actual_poundage' => 'required',
            'paid_at' => 'required',
            'remark' => 'nullable',
        ]);

        $createPaymentBills->handle(new PoundagePaymentBill(), new PoundagePayment(), $attributes, 'finance/poundage_payment_bills/');

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 绑定操作员
     *
     * @param PoundagePaymentBill $poundagePaymentBill
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function associateAuditor(PoundagePaymentBill $poundagePaymentBill, $id)
    {
        $bill = $poundagePaymentBill->ofPlatform()->findOrFail($id);

        $bill->update([
            'operation_id' => Auth::id(),
            'operation_at' => now(),
        ]);

        return response()->noContent();
    }

    /**
     * 处理应付记录
     *
     * @param PoundagePaymentBill $poundagePaymentBill
     * @param HandlePaymentBills $handlePaymentBills
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function handle(PoundagePaymentBill $poundagePaymentBill, HandlePaymentBills $handlePaymentBills, $id)
    {
        $bill = $poundagePaymentBill->ofPlatform()->findOrFail($id);

        $handlePaymentBills->handle($bill);

        return response()->noContent();
    }

    /**
     * 退回应付记录
     *
     * @param PoundagePaymentBill $poundagePaymentBill
     * @param SendBackBills $sendBackPaymentBills
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function sendBack(PoundagePaymentBill $poundagePaymentBill, SendBackBills $sendBackPaymentBills, $id)
    {
        $bill = $poundagePaymentBill->ofPlatform()->findOrFail($id);

        $sendBackPaymentBills->handle($bill, new PoundagePayment());

        return response()->noContent();
    }

    /**
     * 支付记录导出
     *
     * @param PoundagePaymentBill $poundagePaymentBill
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(PoundagePaymentBill $poundagePaymentBill)
    {
        $bills = $poundagePaymentBill->getBills($pageable = false);

        return Excel::download(new PaymentBillExport($bills), '经纪费结算支付记录导出-' . date('Y-m-d H:i:s').'.xlsx');
    }

    /**
     * 支付记录对应处理数据
     *
     * @param PoundagePaymentBill $poundagePaymentBill
     * @param $billId
     * @return PoundagePaymentResource
     */
    public function payments(PoundagePaymentBill $poundagePaymentBill, PoundagePayment $poundagePayment, $billId)
    {
        $bill = $poundagePaymentBill->ofPlatform()->with('payment')->findOrFail($billId);

        $payments = $poundagePayment->getBillPayments(true, $bill['payment']->pluck('id'));

        return new PoundagePaymentResource($payments);
    }

    /**
     * 暂存应付记录
     *
     * @param Request $request
     * @param CreatePaymentBills $createPaymentBills
     * @return \Illuminate\Http\Response
     */
    public function draft(Request $request, CreatePaymentBills $createPaymentBills)
    {
        $attributes = $request->validate([
            'ids' => 'required',
            'premium' => 'required',
            'paid_at' => 'nullable',
            'poundage' => 'nullable',
            'actual_poundage' => 'nullable',
            'proof' => 'nullable|file',
            'remark' => 'nullable',
        ]);

        $createPaymentBills->handle(new PoundagePaymentBill(), new PoundagePayment(), $attributes, 'finance/poundage_payment_bills/', true);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 暂存记录支付处理
     *
     * @param Request $request
     * @param HandleDraftPaymentBills $handleDraftBills
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function draftPayment(Request $request, HandleDraftPaymentBills $handleDraftBills, $id)
    {
        $attributes = $request->validate([
            'proof' => 'required',
            'paid_at' => 'required',
            'actual_poundage' => 'required',
            'remark' => 'nullable',
        ]);

        $bill = PoundagePaymentBill::ofPlatform()->findOrFail($id);

        $handleDraftBills->handle($bill, $attributes, 'finance/poundage_payment_bills/');

        return response()->noContent();
    }
}
