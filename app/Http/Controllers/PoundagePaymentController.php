<?php

namespace App\Http\Controllers;

use App\Exports\FinancesExport;
use App\Http\Resources\PoundagePayment\PoundagePaymentResource;
use App\Models\PoundagePayment;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PoundagePaymentController extends Controller
{
    /**
     * 经纪费结算列表
     *
     * @param PoundagePayment $poundagePayment
     * @return PoundagePaymentResource
     */
    public function index(PoundagePayment $poundagePayment)
    {
        $ids = request()->input('ids');
        $data = $poundagePayment->getPoundagePayments($pageable = true, $ids);

        return new PoundagePaymentResource($data);
    }

    /**
     * 经纪费结算数据导出
     *
     * @param PoundagePayment $poundagePayment
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(PoundagePayment $poundagePayment)
    {
        $billId = request()->input('bill_id');

        $data = $poundagePayment->getPoundagePayments($pageable = false, $ids = null, $billId);

        return Excel::download(new FinancesExport($data), '经纪费结算数据导出-'.date('Y-m-d H:i:s').'.xlsx');
    }

}
