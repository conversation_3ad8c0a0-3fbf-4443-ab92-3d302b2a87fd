<?php

namespace App\Http\Controllers;

use App\Exports\PaymentBillExport;
use App\Http\Resources\PremiumPayment\PremiumPaymentBillsResource;
use App\Http\Resources\PremiumPayment\PremiumPaymentsResource;
use App\Models\Policy;
use App\Models\PremiumPayment;
use App\Models\PremiumPaymentBill;
use App\Services\Finance\CreatePaymentBills;
use App\Services\Finance\HandleDraftPaymentBills;
use App\Services\Finance\HandlePaymentBills;
use App\Services\Finance\SendBackBills;
use App\Services\Finance\UploadProof;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class PremiumPaymentBillController extends Controller
{
    /**
     * 支付记录列表
     *
     * @param PremiumPaymentBill $premiumPaymentBill
     * @return PremiumPaymentBillsResource
     */
    public function index(PremiumPaymentBill $premiumPaymentBill)
    {
        $bills = $premiumPaymentBill->getBills();

        return new PremiumPaymentBillsResource($bills);
    }

    /**
     * 创建支付记录
     *
     * @param Request $request
     * @param CreatePaymentBills $createPaymentBills
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, CreatePaymentBills $createPaymentBills)
    {
        $attributes = $request->validate([
            'ids' => 'required',
            'proof' => 'nullable|file',
            'premium' => 'required',
            'actual_premium' => 'required',
            'remark' => 'nullable',
        ]);

        $createPaymentBills->handle(new PremiumPaymentBill(), new PremiumPayment(), $attributes, 'finance/premium_payment_bills/');

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 绑定操作员
     *
     * @param PremiumPaymentBill $premiumPaymentBill
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function associateAuditor(PremiumPaymentBill $premiumPaymentBill, $id)
    {
        $bill = $premiumPaymentBill->ofPlatform()->findOrFail($id);

        $bill->update([
            'operation_id' => Auth::id(),
            'operation_at' => now(),
        ]);

        return response()->noContent();
    }

    /**
     * 处理应付记录
     *
     * @param PremiumPaymentBill $premiumPaymentBill
     * @param HandlePaymentBills $handlePaymentBills
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function handle(PremiumPaymentBill $premiumPaymentBill, HandlePaymentBills $handlePaymentBills, $id)
    {
        $bill = $premiumPaymentBill->ofPlatform()->findOrFail($id);

        $handlePaymentBills->handle($bill);

        return response()->noContent();
    }

    /**
     * 退回应付记录
     *
     * @param PremiumPaymentBill $premiumPaymentBill
     * @param SendBackBills $sendBackPaymentBills
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function sendBack(PremiumPaymentBill $premiumPaymentBill, SendBackBills $sendBackPaymentBills, $id)
    {
        $bill = $premiumPaymentBill->ofPlatform()->findOrFail($id);

        $sendBackPaymentBills->handle($bill, new PremiumPayment());

        return response()->noContent();
    }

    /**
     * 支付记录导出
     *
     * @param PremiumPaymentBill $premiumPaymentBill
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(PremiumPaymentBill $premiumPaymentBill)
    {
        $bills = $premiumPaymentBill->getBills($pageable = false);

        return Excel::download(new PaymentBillExport($bills), '保费应付支付记录导出-' . date('Y-m-d H:i:s').'.xlsx');
    }

    /**
     * 支付记录对应保费应付数据
     *
     * @param PremiumPaymentBill $premiumPaymentBill
     * @param $billId
     * @return PremiumPaymentsResource
     */
    public function payments(PremiumPaymentBill $premiumPaymentBill, PremiumPayment $premiumPayment, $billId)
    {
        $bill = $premiumPaymentBill->ofPlatform()->with('payment')->findOrFail($billId);

        $payments = $premiumPayment->getBillPayments(true, $bill['payment']->pluck('id'));

        return new PremiumPaymentsResource($payments);
    }

    /**
     * 暂存应付记录
     *
     * @param Request $request
     * @param CreatePaymentBills $createPaymentBills
     * @return \Illuminate\Http\Response
     */
    public function draft(Request $request, CreatePaymentBills $createPaymentBills)
    {
        $attributes = $request->validate([
            'ids' => 'required',
            'proof' => 'nullable|file',
            'premium' => 'nullable',
            'actual_premium' => 'nullable',
            'remark' => 'nullable',
        ]);

        $createPaymentBills->handle(new PremiumPaymentBill(), new PremiumPayment(), $attributes, 'finance/premium_payment_bills/', true);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 暂存记录支付处理
     *
     * @param Request $request
     * @param HandleDraftPaymentBills $handleDraftBills
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function draftPayment(Request $request, HandleDraftPaymentBills $handleDraftBills, $id)
    {
        $attributes = $request->validate([
            'proof' => 'nullable',
            'actual_premium' => 'required',
            'remark' => 'nullable',
        ]);

        $bill = PremiumPaymentBill::ofPlatform()->findOrFail($id);

        $handleDraftBills->handle($bill, $attributes, 'finance/premium_payment_bills/');

        return response()->noContent();
    }

    /**
     * 上传凭证
     *
     * @param Request $request
     * @param UploadProof $uploadProof
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function uploadProof(Request $request, UploadProof $uploadProof, $id)
    {
        $attributes = $request->validate([
            'paid_at' => 'required',
            'proof' => 'required',
        ]);

        $bill = PremiumPaymentBill::ofPlatform()->findOrFail($id);

        $uploadProof->handle($bill, $attributes, 'finance/premium_payment_bills/');

        return response()->noContent();
    }
}
