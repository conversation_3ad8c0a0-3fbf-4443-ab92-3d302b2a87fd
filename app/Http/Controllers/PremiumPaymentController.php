<?php

namespace App\Http\Controllers;

use App\Exports\FinancesExport;
use App\Http\Resources\PremiumPayment\PremiumPaymentsResource;
use App\Models\PremiumPayment;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class PremiumPaymentController extends Controller
{
    /**
     * 保费应付列表
     *
     * @param PremiumPayment $premiumPayment
     * @return PremiumPaymentsResource
     */
    public function index(PremiumPayment $premiumPayment)
    {
        $ids = request()->input('ids');
        $data = $premiumPayment->getPremiumPayments($pageable = true, $ids);

        return new PremiumPaymentsResource($data);
    }

    /**
     * 保费应付数据导出
     *
     * @param PremiumPayment $premiumPayment
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(PremiumPayment $premiumPayment)
    {
        $billId = request()->input('bill_id');

        $data = $premiumPayment->getPremiumPayments($pageable = false, $ids = null, $billId);

        return Excel::download(new FinancesExport($data), '保费应付数据导出-'.date('Y-m-d H:i:s').'.xlsx');
    }
}
