<?php

namespace App\Http\Controllers;

use App\Exports\FinanceBillsExport;
use App\Http\Resources\PremiumReceivable\PremiumReceivableBillsResource;
use App\Http\Resources\PremiumReceivable\PremiumReceivablesResource;
use App\Models\PremiumReceivable;
use App\Models\PremiumReceivableBill;
use App\Services\Finance\HandleDraftReceivableBills;
use App\Services\Finance\HandleReceivableBills;
use App\Services\Finance\SendBackBills;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class PremiumReceivableBillController extends Controller
{
    /**
     * 应收处理记录列表
     *
     * @param PremiumReceivableBill $premiumReceivableBill
     * @return PremiumReceivableBillsResource
     */
    public function index(PremiumReceivableBill $premiumReceivableBill)
    {
        $bills = $premiumReceivableBill->getBills();

        return new PremiumReceivableBillsResource($bills);
    }

    /**
     * 添加保费应收处理记录
     *
     * @param Request $request
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, HandleReceivableBills $handleReceivableBills)
    {
        $attributes = $request->validate([
            'ids' => 'required',
            'proof' => 'required|file',
            'receivable' => 'required',
            'payee_type' => 'required|in:1,2',
            'actual_receivable' => 'required',
            'remark' => 'nullable',
        ]);

        $handleReceivableBills->handle(new PremiumReceivableBill(), new PremiumReceivable(), $attributes, 'finance/premium_receivable_bills/');

        return response()->noContent(Response::HTTP_CREATED);

    }

    /**
     * 保费应收处理记录导出
     *
     * @param PremiumReceivableBill $premiumReceivableBill
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(PremiumReceivableBill $premiumReceivableBill)
    {
        $bills = $premiumReceivableBill->getBills($pageable = false);

        return Excel::download(new FinanceBillsExport($bills), '保费应收处理记录导出-' . date('Y-m-d H:i:s').'.xlsx');
    }

    /**
     * 处理记录对应保费应收数据
     *
     * @param PremiumReceivableBill $premiumReceivableBill
     * @param $billId
     * @return PremiumReceivablesResource
     */
    public function receivables(PremiumReceivableBill $premiumReceivableBill, PremiumReceivable $premiumReceivable, $billId)
    {
        $bill = $premiumReceivableBill->ofPlatform()->with('receivables')->findOrFail($billId);

        $receivables = $premiumReceivable->getBillReceivables(true, $bill['receivables']->pluck('id'));

        return new PremiumReceivablesResource($receivables);
    }

    /**
     * 暂存应收记录
     *
     * @param Request $request
     * @param HandleReceivableBills $handleReceivableBills
     * @return \Illuminate\Http\Response
     */
    public function draft(Request $request, HandleReceivableBills $handleReceivableBills)
    {
        $attributes = $request->validate([
            'ids' => 'required',
            'receivable' => 'nullable',
            'payee_type' => 'nullable|in:1,2',
            'actual_receivable' => 'nullable',
            'proof' => 'nullable|file',
            'remark' => 'nullable',
        ]);

        $handleReceivableBills->handle(new PremiumReceivableBill(), new PremiumReceivable(), $attributes, 'finance/premium_receivable_bills/', false, true);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 暂存记录支付处理
     *
     * @param Request $request
     * @param HandleDraftReceivableBills $handleDraftReceivableBills
     * @param $id
     *
     * @return \Illuminate\Http\Response
     */
    public function draftReceivable(Request $request, HandleDraftReceivableBills $handleDraftReceivableBills, $id)
    {
        $attributes = $request->validate([
            'payee_type' => 'nullable|in:1,2',
            'actual_receivable' => 'nullable',
            'proof' => 'required',
            'remark' => 'nullable',
        ]);

        $bill = PremiumReceivableBill::ofPlatform()->findOrFail($id);

        $handleDraftReceivableBills->handle($bill, $attributes, 'finance/premium_receivable_bills/');

        return response()->noContent();
    }

    /**
     * 退回
     *
     * @param PremiumReceivableBill $premiumReceivableBill
     * @param SendBackBills $sendBackPaymentBills
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function sendBack(PremiumReceivableBill $premiumReceivableBill, SendBackBills $sendBackPaymentBills, $id)
    {
        $bill = $premiumReceivableBill->ofPlatform()->findOrFail($id);

        $sendBackPaymentBills->handle($bill, new PremiumReceivable(), true);

        return response()->noContent();
    }
}
