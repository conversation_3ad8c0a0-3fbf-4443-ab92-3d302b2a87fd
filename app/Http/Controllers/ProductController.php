<?php

namespace App\Http\Controllers;

use App\Http\Requests\Product\CreateRequest;
use App\Http\Requests\Product\UpdateRequest;
use App\Http\Resources\Product\EnabledProductsResource;
use App\Http\Resources\Product\ProductResource;
use App\Http\Resources\Product\ProductsResource;
use App\Models\PlatformProduct;
use App\Models\Product;
use App\Models\ProductAdditional;
use App\Models\User;
use App\Models\UserProduct;
use App\Services\Product\CreateProduct;
use App\Services\Product\UpdateProduct;
use App\Services\User\GetEnabledProducts;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class ProductController extends Controller
{
    /**
     * 获取产品列表.
     *
     * @param Product $product
     * @return ProductsResource
     */

    public function index(Product $product)
    {
        $products = $product->getProducts();

        return new ProductsResource($products);
    }

    /**
     * 显示产品详情.
     *
     * @param Product $product
     * @param int $id
     * @return ProductResource
     */
    public function show(Product $product, int $id)
    {
        $product = $product->with([
            'additional',
            'clauses',
            'transportMethods',
            'loadingMethods',
            'packingMethods',
            'goodsTypes',
        ])->findOrFail($id);

        return new ProductResource($product);
    }

    /**
     * 创建产品.
     *
     * @param   CreateRequest  $request
     * @param   CreateProduct  $createProduct
     *
     * @return  \Illuminate\Http\Response
     */
    public function create(CreateRequest $request, CreateProduct $createProduct)
    {
        $createProduct->handle($request->validated());

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新产品.
     *
     * @param   UpdateRequest  $request
     * @param   UpdateProduct  $updateProduct
     * @param   int            $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function update(UpdateRequest $request, UpdateProduct $updateProduct, int $id)
    {
        $updateProduct->handle($id, $request->validated());

        return response()->noContent();
    }

    /**
     * 更新产品状态.
     *
     * @param   Product  $product
     * @param   int      $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function updateEnabled(Product $product, int $id)
    {
        $product = $product->findOrFail($id);

        DB::transaction(function () use ($product) {
            $isEnabled = !$product->is_enabled;

            $product->forceFill(['is_enabled' => $isEnabled])->save();

            if (!$isEnabled) {
                $product->userProducts()->update(['is_enabled' => $isEnabled]);
                $product->platformProducts()->update(['is_enabled' => $isEnabled]);
            }
        });

        return response()->noContent();
    }

    /**
     * 技术配置.
     *
     * @param  Request $request
     * @param  ProductAdditional  $product
     * @param  int  $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function developerConfigure(Request $request, ProductAdditional $product, int $id)
    {
        $attributes = $request->validate([
            'config' => 'required|array',
        ]);

        $product->where('product_id', $id)->update([
            'config' => $attributes['config']
        ]);

        return response()->noContent();
    }

    /**
     * 删除产品.
     *
     * @param   Product  $product
     * @param   int      $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function delete(Product $product, int $id)
    {
        DB::transaction(function () use ($product, $id) {
            $product->where('id', $id)->delete();

            PlatformProduct::where('product_id', $id)->delete();
            UserProduct::where('product_id', $id)->delete();
        });

        return response()->noContent();
    }

    /**
     * 获取平台/用户代理可用产品
     *
     * @param GetEnabledProducts $getEnabledProducts
     * @param $userId
     * @return EnabledProductsResource
     */
    public function getEnabledProducts(GetEnabledProducts $getEnabledProducts, $userId)
    {
        $user = User::findOrFail($userId);

        $data = $getEnabledProducts->handle($user);

        return new EnabledProductsResource($data);
    }
}
