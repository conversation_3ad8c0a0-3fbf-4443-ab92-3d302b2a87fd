<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductGeneralCategory;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Symfony\Component\HttpFoundation\Response;

class ProductGeneralCategoryController extends Controller
{
    /**
     * 其他险种分类列表
     *
     * @param ProductGeneralCategory $productGeneralCategory
     * @return JsonResource
     */
    public function index(ProductGeneralCategory $productGeneralCategory)
    {
        $categories = $productGeneralCategory->getCategories();

        return new JsonResource($categories);
    }

    /**
     * 添加分类
     *
     * @param Request $request
     * @param ProductGeneralCategory $productGeneralCategory
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, ProductGeneralCategory $productGeneralCategory)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'is_enabled' => 'nullable',
        ]);
        $attributes['platform_id'] = auth()->user()->platform_id;

        $productGeneralCategory->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新分类
     *
     * @param Request $request
     * @param ProductGeneralCategory $productGeneralCategory
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductGeneralCategory $productGeneralCategory, $id)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'is_enabled' => 'nullable',
        ]);

        $productGeneralCategory->where('id', $id)->update($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 删除分类
     *
     * @param Product $product
     * @param ProductGeneralCategory $productGeneralCategory
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function delete(Product $product, ProductGeneralCategory $productGeneralCategory, $id)
    {
        $product = $product->where(['type' => Product::TYPE_GENERAL, 'is_enabled' => true])
            ->whereHas('additional', function ($q) use ($id){
                $q->where('category_id', $id);
            })
            ->first();
        if ($product){
            abort(400, '当前分类下还有正在使用的产品,无法删除');
        }
        $productGeneralCategory->where('id', $id)->delete();

        return response()->noContent();
    }
}
