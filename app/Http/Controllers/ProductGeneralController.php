<?php

namespace App\Http\Controllers;

use App\Http\Resources\ProductGeneral\ProductGeneralsResource;
use App\Http\Resources\ProductGeneral\ProductGeneralResource;
use App\Models\Product;
use App\Services\Product\General\UpdateBenefit;
use App\Services\Product\General\UpdateRelatedFile;
use Illuminate\Http\Request;

class ProductGeneralController extends Controller
{

    /**
     * 其他险种产品列表
     *
     * @param Product $product
     * @return ProductGeneralsResource
     */
    public function index(Product $product)
    {
        $products = $product->getProducts(Product::TYPE_GENERAL);

        return new ProductGeneralsResource($products);
    }

    /**
     * 其他险种产品详情
     *
     * @param Product $product
     * @param $id
     * @return ProductGeneralResource
     */
    public function show(Product $product, $id)
    {
        $product = $product->findOrFail($id);

        return new ProductGeneralResource($product);
    }

    /**
     * 其他险种产品标签修改
     * 
     * @param Request $request
     * @param Product $product
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function label(Request $request, Product $product, $id)
    {
        $attributes = $request->validate([
            'label' => 'nullable',
        ]);

        $product->findOrFail($id)
            ->additional()
            ->update($attributes);

        return response()->noContent();
    }

    /**
     * 其他险种产品投保须知修改
     * 
     * @param Request $request
     * @param Product $product
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function notice(Request $request, Product $product, $id)
    {
        $attributes = $request->validate([
            'notice' => 'nullable',
        ]);

        $product->findOrFail($id)
            ->additional()
            ->update($attributes);

        return response()->noContent();
    }

    /**
     * 其他险种产品相关文件修改
     * 
     * @param Request $request
     * @param UpdateRelatedFile $updateRelatedFile
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function related_file(Request $request, UpdateRelatedFile $updateRelatedFile, $id)
    {
        $attributes = $request->validate([
            'type' => 'required',
            'id' => 'required_if:type,update,del',
            'name' => 'required_if:type,add',
            'file' => 'required_if:type,add'
        ]);

        $updateRelatedFile->handle(Product::findOrFail($id), $attributes);

        return response()->noContent();
    }

    /**
     * 其他险种产品保障权益修改
     *
     * @param Request $request
     * @param UpdateBenefit $updateBenefit
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function benefit(Request $request, UpdateBenefit $updateBenefit, $id)
    {
        $attributes = $request->validate([
            'type' => 'required',
            'id' => 'required_if:type,update,del',
            'name' => 'required_if:type,add',
            'amount' => 'required_if:type,add',
            'content' => 'required_if:type,add',
        ]);

        $updateBenefit->handle(Product::findOrFail($id), $attributes);

        return response()->noContent();
    }



}
