<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\ProductGeneralModel;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Symfony\Component\HttpFoundation\Response;

class ProductGeneralModelController extends Controller
{
    /**
     * 其他险种产品模型列表
     *
     * @param ProductGeneralModel $productGeneralModel
     * @return JsonResource
     */
    public function index(ProductGeneralModel $productGeneralModel)
    {
        $categories = $productGeneralModel->getModels();

        return new JsonResource($categories);
    }

    /**
     * 添加其他险种产品模型
     *
     * @param Request $request
     * @param ProductGeneralModel $productGeneralModel
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, ProductGeneralModel $productGeneralModel)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'flag' => 'required',
            'marks' => 'nullable',
            'is_enabled' => 'nullable',
        ]);
        $attributes['platform_id'] = auth()->user()->platform_id;

        $productGeneralModel->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新其他险种产品模型
     *
     * @param Request $request
     * @param ProductGeneralModel $productGeneralModel
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductGeneralModel $productGeneralModel, $id)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'flag' => 'required',
            'marks' => 'nullable',
            'is_enabled' => 'nullable',
        ]);

        $productGeneralModel->where('id', $id)->update($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 删除其他险种产品模型
     *
     * @param Product $product
     * @param ProductGeneralModel $productGeneralModel
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function delete(Product $product, ProductGeneralModel $productGeneralModel, $id)
    {
        $product = $product->where(['type' => Product::TYPE_GENERAL, 'is_enabled' => true])
            ->whereHas('additional', function ($q) use ($id){
                $q->where('model_id', $id);
            })
            ->first();
        if ($product){
            abort(400, '当前模型下还有正在使用的产品,无法删除');
        }
        $productGeneralModel->where('id', $id)->delete();

        return response()->noContent();
    }
}
