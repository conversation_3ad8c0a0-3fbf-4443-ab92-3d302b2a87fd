<?php

namespace App\Http\Controllers;

use App\Http\Resources\ProductGeneral\ProductGeneralModelFieldsResource;
use App\Models\ProductGeneralModelField;
use App\Services\Product\General\UpdateModelFields;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpFoundation\Response;

class ProductGeneralModelFieldController extends Controller
{
    /**
     * 其他险种产品模型字段列表
     *
     * @param ProductGeneralModelField $generalModelField
     * @param $modelId
     * @return JsonResource
     */
    public function index(ProductGeneralModelField $generalModelField, $modelId)
    {
        $fields = $generalModelField->getFields($modelId);

        return new ProductGeneralModelFieldsResource($fields);
    }

    /**
     * 添加模型字段
     *
     * @param Request $request
     * @param ProductGeneralModelField $generalModelField
     * @param $modelId
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, ProductGeneralModelField $generalModelField, $modelId)
    {
        $attributes = $request->validate([
            'title' => 'required',
            'name' => 'required|unique:product_general_model_fields,name,NULL,id,model_id,'.$modelId.',deleted_at,NULL',
            'type' => 'required',
            'function' => 'nullable',
            'tips' => 'nullable',
            'order' => 'nullable|numeric|min:0|max:120',
            'options' => 'required_if:type,select',
            'file' => 'required_if:type,_file|file',
        ]);

        $attributes['model_id'] = $modelId;

        if($request->hasFile('file')){
            $attributes['file'] = $request->file('file')->storePublicly('product/general/model_fields', ['disk' => 'public']);
        }

        $generalModelField->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新模型字段
     *
     * @param Request $request
     * @param UpdateModelFields $updateModelFields
     * @param $modelId
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, UpdateModelFields $updateModelFields, $modelId, $id)
    {
        $attributes = $request->validate([
            'title' => 'required',
            'name' => 'required|unique:product_general_model_fields,name,'.$id.',id,model_id,'.$modelId.',deleted_at,NULL',
//            'name' => 'required|'.Rule::unique('product_general_model_fields')->where(function ($query) use($modelId, $id) {
//                    return $query->where('model_id', $modelId)->whereNull('deleted_at');
//                })->ignore($id),
            'type' => 'required',
            'function' => 'nullable',
            'tips' => 'nullable',
            'order' => 'nullable|numeric|min:0|max:120',
            'options' => 'required_if:type,select',
            'file' => 'nullable',
        ]);

        $updateModelFields->handle(ProductGeneralModelField::findOrFail($id), $attributes);

        return response()->noContent();
    }

    /**
     * 删除模型字段
     *
     * @param ProductGeneralModelField $generalModelField
     * @param $modelId
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function delete(ProductGeneralModelField $generalModelField, $modelId, $id)
    {
        $generalModelField->where('id', $id)->delete();

        return response()->noContent();
    }
}
