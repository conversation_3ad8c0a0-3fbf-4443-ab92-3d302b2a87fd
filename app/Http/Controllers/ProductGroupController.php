<?php

namespace App\Http\Controllers;

use App\Services\Product\Group\EnabledStatusModify;
use App\Services\Product\Group\TechConfigModify;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ProductGroupController extends Controller
{
    /**
     * 状态修改
     *
     * @param Request $request
     * @param EnabledStatusModify $enabledStatusModify
     * @param int $productId
     * @return Response
     */
    public function status(Request $request, EnabledStatusModify $enabledStatusModify, int $productId)
    {
        $attributes = $request->validate([
            'is_enabled' => 'required|in:0,1'
        ]);

        $enabledStatusModify->handle($productId, $attributes);

        return response()->noContent(Response::HTTP_OK);
    }

    /**
     * 产品技术配置
     *
     * @param Request $request
     * @param TechConfigModify $techConfigModify
     * @param int $productId
     * @return Response
     */
    public function config(Request $request, TechConfigModify $techConfigModify, int $productId)
    {
        $attributes = $request->validate([
            'third_platform' => 'required',
            'third_product_code' => 'nullable',
            'parameters' => 'nullable'
        ]);

        $techConfigModify->handle($productId, $attributes);

        return response()->noContent(Response::HTTP_OK);
    }
}
