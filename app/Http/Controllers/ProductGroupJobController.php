<?php

namespace App\Http\Controllers;

use App\Exceptions\BusinessException;
use App\Http\Resources\ProductGroup\ProductGroupJobsResource;
use App\Imports\ProductGroupPlanJobsImport;
use App\Models\ProductGroupJob;
use App\Models\ProductGroupPlan;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response as HttpFoundationResponse;

class ProductGroupJobController extends Controller
{
    /** @var ProductGroupPlan */
    protected $productGroupPlan;
    /** @var ProductGroupJob */
    protected $productGroupJob;

    public function __construct(ProductGroupPlan $productGroupPlan, ProductGroupJob $productGroupJob)
    {
        $this->productGroupPlan = $productGroupPlan;
        $this->productGroupJob = $productGroupJob;
    }

    /**
     * 套餐工种价格列表
     *
     * @param int $productId
     * @param int $groupPlanId
     * @return ProductGroupJobsResource
     */
    public function index(int $productId, int $groupPlanId)
    {
        $jobs = $this->productGroupJob->getJobListByGroupPlanId($groupPlanId);

        return new ProductGroupJobsResource($jobs);
    }

    /**
     * 添加套餐工种价格
     *
     * @param Request $request
     * @param int $productId
     * @param int $groupPlanId
     * @return \Illuminate\Http\Response
     * @throws BusinessException
     */
    public function create(Request $request, int $productId, int $groupPlanId)
    {
        $attributes = $request->validate([
            'code' => ['required', 'min:1', 'max:50'],
            'grade' => ['nullable', 'min:1', 'max:50'],
            'major_class' => ['nullable', 'min:1', 'max:50'],
            'occupation_code' => ['nullable', 'min:1', 'max:50'],
            'name' => ['required', 'min:1', 'max:50'],
            'price' => 'required|numeric',
        ]);

        /** @var ProductGroupPlan $productGroupPlan */
        $productGroupPlan = $this->productGroupPlan->newQuery()->findOrFail($groupPlanId);

        $attributes = array_merge($attributes, [
            'product_id' => $productGroupPlan->getAttribute('product_id'),
            'group_plan_id' => $productGroupPlan->getAttribute('id'),
            'is_enabled' => 1,
        ]);

        $this->productGroupJob->isJobExisted($attributes, $groupPlanId);
        $this->productGroupJob->newQuery()->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 导入雇主套餐工种价格信息.`
     *
     * @param   Request  $request
     * @param   int  $productId
     * @param   int  $groupPlanId
     *
     * @retrun  \Illuminate\Http\Response
     */
    public function import(Request $request, int $productId, int $planId)
    {
        $attributes = $request->validate([
            'file' => ['required', 'file', 'mimes:xlsx,xls'],
        ]);

        Excel::import(new ProductGroupPlanJobsImport($productId, $planId), $attributes['file']);

        return response()->noContent(HttpFoundationResponse::HTTP_CREATED);
    }

    /**
     * 修改套餐工种价格
     *
     * @param \Illuminate\Http\Request $request
     * @param int $productId
     * @param int $productPlanId
     * @param int $groupJobId
     * @return \Illuminate\Http\Response
     * @throws BusinessException
     */
    public function update(Request $request, int $productId, int $productPlanId, int $groupJobId)
    {
        /** @var ProductGroupJob $productGroupJob */
        $productGroupJob = $this->productGroupJob->newQuery()->findOrFail($groupJobId);

        $attributes = $request->validate([
            'code' => ['required', 'min:1', 'max:50'],
            'grade' => ['nullable', 'min:1', 'max:50'],
            'major_class' => ['nullable', 'min:1', 'max:50'],
            'occupation_code' => ['nullable', 'min:1', 'max:50'],
            'name' => ['required', 'min:1', 'max:50'],
            'price' => 'required|numeric',
        ]);

        $this->productGroupJob->isJobExisted($attributes, $productGroupJob->getAttribute('group_plan_id'), $groupJobId);
        $productGroupJob->checkHasRelatedPolicies();
        $productGroupJob->update($attributes);

        return response()->noContent(Response::HTTP_OK);
    }

    /**
     * 删除套餐工种价格
     *
     * @param int $productId
     * @param int $productPlanId
     * @param int $groupJobId
     * @return \Illuminate\Http\Response
     * @throws BusinessException
     */
    public function delete(int $productId,  int $productPlanId, int $groupJobId)
    {
        /** @var ProductGroupJob $productGroupJob */
        $productGroupJob = $this->productGroupJob->newQuery()->findOrFail($groupJobId);

        $productGroupJob->checkHasRelatedPolicies();
        $productGroupJob->delete();

        return response()->noContent();
    }

    /**
     * 套餐工种启用/禁用
     *
     * @param Request $request
     * @param $groupJobId
     * @return Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function enabled(Request $request, $groupJobId)
    {
        $attributes = $request->validate([
            'is_enabled' => 'required|in:0,1'
        ]);

        $productGroupPlan = $this->productGroupJob->newQuery()->findOrFail($groupJobId);
        $productGroupPlan->update($attributes);

        return response()->noContent();
    }
}
