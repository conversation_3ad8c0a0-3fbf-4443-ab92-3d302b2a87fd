<?php

namespace App\Http\Controllers;

use App\Http\Resources\ProductGroup\ProductGroupPlansResource;
use App\Models\ProductGroupPlan;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Response;

class ProductGroupPlanController extends Controller
{
    /** @var ProductGroupPlan */
    protected $productGroupPlan;

    /**
     * ProductGroupPlanController constructor.
     * @param ProductGroupPlan $productGroupPlan
     */
    public function __construct(ProductGroupPlan $productGroupPlan)
    {
        $this->productGroupPlan = $productGroupPlan;
    }

    /**
     * 获取雇主险产品套餐列表
     *
     * @param int $productId
     * @return JsonResource
     */
    public function index(int $productId)
    {
        $planList = $this->productGroupPlan->getPlanListByProductId($productId);

        return new ProductGroupPlansResource($planList);
    }

    /**
     * 获取产品套餐详情
     *
     * @param int $productId
     * @param int $planId
     * @return ProductGroupPlansResource
     */
    public function show(int $productId, int $planId)
    {
        $productGroupPlan = $this->productGroupPlan->newQuery()
            ->with(['jobs', 'product.additional:product_id,id,third_platform,third_product_code'])
            ->where([
                'product_id' => $productId,
                'id' => $planId
            ])
            ->firstOrFail();

        return new ProductGroupPlansResource($productGroupPlan);
    }

    /**
     * 创建产品套餐
     *
     * @param Request $request
     * @param ProductGroupPlan $productGroupPlan
     * @param int $productId
     * @return Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function create(Request $request, ProductGroupPlan $productGroupPlan, int $productId)
    {
        $attributes = $request->validate([
            'title' => ['required', 'min:1', 'max:50'],
            'code' => ['nullable', 'min:1', 'max:50'],
            'accidental_death' => ['nullable', 'min:1'],
            'accidental_medical' => ['nullable', 'min:1'],
            'accidental_injury' => ['nullable', 'min:1'],
            'lost_wages' => ['nullable', 'min:1', 'max:250'],
            'accidental_allowance' => ['nullable', 'min:1', 'max:250'],
            'legal_liability' => ['nullable', 'min:1', 'max:250'],
            'total_indemnity' => ['nullable', 'min:1', 'max:250'],
            'is_enabled' => 'nullable|bool'
        ]);
        $attributes['product_id'] = $productId;
        $this->productGroupPlan->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新产品套餐
     *
     * @param Request $request
     * @param int $productId
     * @param int $planId
     * @return Response
     */
    public function update(Request $request, int $productId, int $planId)
    {
        $attributes = $request->validate([
            'title' => ['required', 'min:1', 'max:50'],
            'code' => ['nullable', 'min:1', 'max:50'],
            'accidental_death' => ['nullable', 'min:1'],
            'accidental_injury' => ['nullable', 'min:1'],
            'accidental_medical' => ['nullable', 'min:1'],
            'lost_wages' => ['nullable', 'min:1', 'max:250'],
            'accidental_allowance' => ['nullable', 'min:1', 'max:250'],
            'legal_liability' => ['nullable', 'min:1', 'max:250'],
            'total_indemnity' => ['nullable', 'min:1', 'max:250'],
            'is_enabled' => ['nullable', 'boolean']
        ]);
        $productGroupPlan = $this->productGroupPlan->newQuery()->findOrFail($planId);
        $productGroupPlan->update($attributes);

        return response()->noContent();
    }

    /**
     * 删除产品套餐
     *
     * @param int $productId
     * @param int $planId
     * @return Response
     * @throws \App\Exceptions\BusinessException
     */
    public function delete(int $productId, int $planId)
    {
        /** @var ProductGroupPlan $productGroupPlan */
        $productGroupPlan = $this->productGroupPlan->newQuery()->where('id', $planId)->first();
        $productGroupPlan->checkHasRelatedPolicies();

        $productGroupPlan->delete();

        return response()->noContent();
    }

    /**
     * 产品套餐启用/禁用
     *
     * @param Request $request
     * @param $planId
     * @return Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function enabled(Request $request, $planId)
    {
        $attributes = $request->validate([
            'is_enabled' => 'required|bool'
        ]);

        $productGroupPlan = $this->productGroupPlan->newQuery()->findOrFail($planId);
        $productGroupPlan->update($attributes);

        return response()->noContent();
    }
}
