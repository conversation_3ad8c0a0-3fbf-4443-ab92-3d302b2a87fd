<?php

namespace App\Http\Controllers;

use App\Http\Resources\ProductOffline\OfflineCategoriesResource;
use App\Http\Resources\ProductOffline\OfflineCategoryResource;
use App\Models\ProductOfflineCategory;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ProductOfflineCategoryController extends Controller
{
    /**
     * 分类列表
     *
     * @param Request $request
     * @param ProductOfflineCategory $offlineCategory
     * @return OfflineCategoriesResource
     */
    public function index(Request $request, ProductOfflineCategory $offlineCategory)
    {
        $pageable = $request->input('is_pageable');
        $isParent = $request->input('is_parent');

        $data = $offlineCategory->getCategories($pageable, $isParent);

        return new OfflineCategoriesResource($data);
    }

    /**
     * 分类详情
     *
     * @param ProductOfflineCategory $offlineCategory
     * @param $id
     * @return OfflineCategoryResource
     */
    public function show(ProductOfflineCategory $offlineCategory, $id)
    {
        $category = $offlineCategory->findOrFail($id);

        return new OfflineCategoryResource($category);
    }

    /**
     * 添加分类
     *
     * @param Request $request
     * @param ProductOfflineCategory $offlineCategory
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, ProductOfflineCategory $offlineCategory)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'parent_id' => 'nullable',
            'is_enabled' => 'nullable|bool',
        ]);

        $offlineCategory->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新分类
     *
     * @param Request $request
     * @param ProductOfflineCategory $offlineCategory
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductOfflineCategory $offlineCategory, $id)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'parent_id' => 'nullable',
            'is_enabled' => 'nullable|bool',
        ]);

        $category = $offlineCategory->findOrFail($id);
        $category->update($attributes);

        return response()->noContent();
    }
}
