<?php

namespace App\Http\Controllers;

use App\Http\Resources\Product\ProductOfflineResource;
use App\Http\Resources\Product\ProductsResource;
use App\Models\ProductOfflineTemplate;
use App\Models\Product;
use App\Services\Product\Offline\CreateProductOffline;
use App\Services\Product\Offline\UpdateProductOffline;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ProductOfflineController extends Controller
{
    /**
     * 线下录入保单产品列表
     *
     * @param Product $product
     * @return ProductsResource
     */
    public function index(Product $product)
    {
        $data = $product->getProducts(Product::TYPE_OFFLINE);

        return new ProductsResource($data);
    }

    /**
     * 线下录入保单产品详情
     *
     * @param Product $product
     * @param $id
     * @return ProductOfflineResource
     */
    public function show(Product $product, $id)
    {
        $data = $product->findOrFail($id);

        return new ProductOfflineResource($data);
    }

    /**
     * 添加线下录入保单产品
     *
     * @param Request $request
     * @param CreateProductOffline $createProductOffline
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, CreateProductOffline $createProductOffline)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'platform_id' => 'required',
            'category_id' => 'required',
            'insurance_id' => 'required',
            'template_id' => 'required',
        ]);

        $validate = ProductOfflineTemplate::getValidate($attributes['template_id']);

        $attributes = array_merge($attributes, $request->validate($validate));

        $createProductOffline->handle($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新线下录入保单产品
     *
     * @param Request $request
     * @param UpdateProductOffline $updateProductOffline
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, UpdateProductOffline $updateProductOffline, $id)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'platform_id' => 'required',
            'category_id' => 'required',
            'template_id' => 'required',
        ]);
        $validate = ProductOfflineTemplate::getValidate($attributes['template_id']);

        $attributes = array_merge($attributes, $request->validate($validate));

        $product = Product::findOrFail($id);

        $updateProductOffline->handle($product, $attributes);

        return response()->noContent();
    }

    /**
     * 禁启用线下录入保单产品
     *
     * @param Product $product
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function updateEnabled(Product $product, int $id)
    {
        $product = $product->findOrFail($id);

        $product->update(['is_enabled' => !$product['is_enabled']]);

        return response()->noContent();
    }
}
