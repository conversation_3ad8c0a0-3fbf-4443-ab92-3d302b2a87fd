<?php

namespace App\Http\Controllers;

use App\Http\Resources\ProductOffline\OfflineFieldsResource;
use App\Models\ProductOfflineField;
use App\Models\ProductOfflineTemplateField;
use App\Services\Product\Offline\UpdateTemplateFields;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ProductOfflineFieldsController extends Controller
{
    /**
     * 线下录入字段列表
     *
     * @param Request $request
     * @param ProductOfflineField $templateField
     * @param $categoryId
     * @return OfflineFieldsResource
     */
    public function fields(Request $request, ProductOfflineField $templateField, $categoryId)
    {
        $pageable = $request->input('is_pageable');

        $data = $templateField->getFields($categoryId, $pageable);

        return new OfflineFieldsResource($data);
    }

    /**
     * 添加字段
     *
     * @param Request $request
     * @param ProductOfflineField $templateField
     * @param $categoryId
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, ProductOfflineField $templateField, $categoryId)
    {
        $attributes = $request->validate([
            'title' => 'required',
            'name' => 'required|unique:product_offline_fields,name,NULL,id,category_id,'.$categoryId.',deleted_at,NULL',
            'type' => 'required',
            'order' => 'required',
            'options' => 'required_if:type,select',
            'file' => 'required_if:type,_file',
        ]);

        $attributes['category_id'] = $categoryId;

        if($request->hasFile('file')){
            $attributes['file'] = $request->file('file')->storePublicly('offline/product/fields', ['disk' => 'public']);
        }

        $templateField->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新字段
     *
     * @param Request $request
     * @param UpdateTemplateFields $updateTemplateFields
     * @param $categoryId
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, UpdateTemplateFields $updateTemplateFields, $categoryId, $id)
    {
        $attributes = $request->validate([
            'title' => 'required',
            'name' => 'required|unique:product_offline_fields,name,'.$id.',id,category_id,'.$categoryId.',deleted_at,NULL',
            'type' => 'required',
            'order' => 'required',
            'options' => 'required_if:type,select',
            'file' => 'required_if:type,_file',
        ]);

        $updateTemplateFields->handle(ProductOfflineField::findOrFail($id), $attributes);

        return response()->noContent();
    }

    /**
     * 删除字段
     *
     * @param ProductOfflineField $templateField
     * @param $categoryId
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function delete(ProductOfflineField $templateField, $categoryId, $id)
    {
        $templateField->where('id', $id)->delete();

        return response()->noContent();
    }
}
