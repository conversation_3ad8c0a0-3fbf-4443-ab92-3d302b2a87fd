<?php

namespace App\Http\Controllers;

use App\Http\Resources\ProductOffline\OfflineTemplateResource;
use App\Models\ProductOfflineTemplate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ProductOfflineTemplateController extends Controller
{
    /**
     * 模板列表
     *
     * @param Request $request
     * @param ProductOfflineTemplate $offlineTemplate
     * @return OfflineTemplateResource
     */
    public function index(Request $request, ProductOfflineTemplate $offlineTemplate)
    {
        $pageable = $request->input('is_pageable');

        $isOwnPlatform = $request->input('is_own_platform');

        $data = $offlineTemplate->getTemplates($pageable, $isOwnPlatform);

        return new OfflineTemplateResource($data);
    }

    /**
     * 添加模板
     *
     * @param Request $request
     * @param ProductOfflineTemplate $offlineTemplate
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, ProductOfflineTemplate $offlineTemplate)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'category_id' => 'required',
            'is_enabled' => 'nullable|bool',
        ]);

        $offlineTemplate->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新模板
     *
     * @param Request $request
     * @param ProductOfflineTemplate $offlineTemplate
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProductOfflineTemplate $offlineTemplate, $id)
    {
        $attributes = $request->validate([
            'name' => 'required',
            'category_id' => 'required',
            'is_enabled' => 'nullable|bool',
        ]);

        $template = $offlineTemplate->findOrFail($id);
        $template->update($attributes);

        return response()->noContent();
    }

}
