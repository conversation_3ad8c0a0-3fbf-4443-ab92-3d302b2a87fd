<?php

namespace App\Http\Controllers;

use App\Models\Recommendation;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Symfony\Component\HttpFoundation\Response;

class RecommendationController extends Controller
{
    /**
     * 获取推荐产品列表.
     *
     * @param   Recommendation  $recommendation
     *
     * @return  \Illuminate\Http\Response
     */
    public function index(Recommendation $recommendation)
    {
        $recommendations = $recommendation->getRecommendations();

        return new JsonResource($recommendations);
    }

    /**
     * 创建推荐.
     *
     * @param   Request         $request
     * @param   Recommendation  $recommendation
     *
     * @return  \Illuminate\Http\Response
     */
    public function create(Request $request, Recommendation $recommendation)
    {
        $attributes = $request->validate([
            'title' => 'required|min:1|max:99',
            'product_type' => 'required|in:1,2,3,4,5',
            'starting_price' => 'nullable',
            'tags' => 'nullable',
            'image' => 'required|image',
            'content' => 'required|min:1',
        ]);

        $attributes['platform_id'] = Auth::user()['platform_id'];
        $attributes['image'] = $request->file('image')->storePublicly('recommendations', ['disk' => 'public']);

        $recommendation->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 获取推荐详情.
     *
     * @param   Recommendation  $recommendation
     * @param   int             $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function show(Recommendation $recommendation, int $id)
    {
        $recommendation = $recommendation->ofPlatform()->findOrFail($id);

        $recommendation['image'] = url(Storage::url($recommendation['image']));

        return new JsonResource($recommendation);
    }

    /**
     * 更新推荐.
     *
     * @param   Request         $request
     * @param   Recommendation  $recommendation
     * @param   int             $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function update(Request $request, Recommendation $recommendation, int $id)
    {
        $attributes = $request->validate([
            'title' => 'required|min:1|max:99',
            'product_type' => 'required|in:1,2,3,4,5',
            'starting_price' => 'nullable',
            'tags' => 'nullable',
            'image' => 'nullable',
            'content' => 'required|min:1',
        ]);

        if ($request->hasFile('image')) {
            $attributes['image'] = $request->file('image')->storePublicly('recommendations', ['disk' => 'public']);
        } else {
            unset($attributes['image']);
        }

        $recommendation->whereId($id)->update($attributes);

        return response()->noContent();
    }

    /**
     * 删除推荐产品.
     *
     * @param   Recommendation  $recommendation
     * @param   int             $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function delete(Recommendation $recommendation, int $id)
    {
        $recommendation->ofPlatform()->whereId($id)->delete();

        return response()->noContent();
    }
}
