<?php

namespace App\Http\Controllers;

use App\Http\Resources\User\UserRegistrationsResource;
use App\Models\UserRegistration;
use Illuminate\Http\Resources\Json\JsonResource;

class RegistrationController extends Controller
{
    /**
     * 获取用户注册申请数量.
     *
     * @param   UserRegistration  $userRegistration
     *
     * @return  \Illuminate\Http\Response
     */
    public function index(UserRegistration $userRegistration)
    {
        $registrations = $userRegistration->getRegistrations();

        return new UserRegistrationsResource($registrations);
    }

    /**
     * 注册申请待处理条数.
     *
     * @param   UserRegistration  $userRegistration
     *
     * @return  \Illuminate\Http\Response
     */
    public function pendingCount(UserRegistration $userRegistration)
    {
        $count = $userRegistration->pendingCount();

        return new JsonResource([
            'count' => $count
        ]);
    }

    /**
     * 删除注册申请.
     *
     * @param   UserRegistration  $userRegistration
     * @param   int               $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function delete(UserRegistration $userRegistration, int $id)
    {
        $userRegistration->ofPlatform()->whereId($id)->delete();

        return response()->noContent();
    }
}
