<?php

namespace App\Http\Controllers;

use App\Http\Resources\Role\RoleResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Spatie\Permission\Models\Role;
use Symfony\Component\HttpFoundation\Response;

class RoleController extends Controller
{
    /**
     * 获取角色列表.
     *
     * @param   Role  $role
     *
     * @return  \Illuminate\Http\Resources\Json\JsonResource
     */
    public function index(Role $role)
    {
        $roles = $role->when(Auth::user()['platform_id'] !== -1, function ($q) {
            $q->where('platform_id', Auth::user()['platform_id'])
                ->orWhere('is_platform_default', true);
        })
            ->get();

        return new JsonResource($roles);
    }

    /**
     * 获取权限详情.
     *
     * @param   Role  $role
     * @param   int   $id
     *
     * @return  \Illuminate\Http\Resources\Json\JsonResource
     */
    public function show(Role $role, int $id)
    {
        $role = $role->findById($id);

        return new RoleResource($role);
    }

    /**
     * 创建角色.
     *
     * @param   Request  $request
     * @param   Role     $role
     *
     * @return  \Illuminate\Http\Response
     */
    public function create(Request $request, Role $role)
    {
        $attributes = $request->validate([
            'is_platform' => 'required|bool',
            'is_platform_default' => 'required|bool',
            'name' => 'required|min:1|max:32|unique:roles',
            'display_name' => 'required|min:1|max:32',
        ]);

        $role = $role->create($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新角色信息.
     *
     * @param   Request  $request
     * @param   Role     $role
     * @param   int      $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function update(Request $request, Role $role, int $id)
    {
        $attributes = $request->validate([
            'is_platform' => 'required|bool',
            'is_platform_default' => 'required|bool',
            'name' => [
                'required',
                'min:1',
                'max:32',
                Rule::unique('roles')->ignore($id)
            ],
            'display_name' => 'required|min:1|max:32',
        ]);

        $role->where('id', $id)->update($attributes);

        return response()->noContent();
    }

    /**
     * 同步权限.
     *
     * @param   Request  $request
     * @param   Role     $role
     * @param   int      $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function syncPermissions(Request $request, Role $role, int $id)
    {
        $attributes = $request->validate([
            'permissions' => 'required|array',
        ]);

        $role = $role->findOrFail($id);

        $role->syncPermissions($attributes['permissions']);

        return response()->noContent();
    }

    /**
     * 删除角色.
     *
     * @param   Role  $role
     * @param   int   $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function delete(Role $role, int $id)
    {
        /** @var Role $role */
        $role = $role->findById($id);

        if (!$role->users->isEmpty()) {
            abort(403, '当前角色已有关联用户不允许删除');
        }

        if (!Auth::user()->hasRole('super-admin') && $role->is_platform_default) {
            abort(403, '平台默认角色不允许删除');
        }

        $role->delete();

        return response()->noContent();
    }
}
