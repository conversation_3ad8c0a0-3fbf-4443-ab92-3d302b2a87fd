<?php

namespace App\Http\Controllers;

use App\Exports\SettlementExport;
use App\Exports\SettlementMultipleSheelsExport;
use App\Exports\SettlementPoliciesExport;
use App\Http\Resources\Settlement\SettlementResource;
use App\Http\Resources\Settlement\SettlementsResource;
use App\Models\Settlement;
use App\Services\Finance\Settlement\ApplySettlement;
use App\Services\Finance\Settlement\HandleSettlement;
use App\Services\Finance\Settlement\SendBackSettlement;
use App\Services\Finance\Settlement\SendSettlementMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;

class SettlementController extends Controller
{
    public function index(Settlement $settlement)
    {
        $data = $settlement->getSettlements();

        return new SettlementsResource($data);
    }

    /**
     * 销账详情
     *
     * @param Settlement $settlement
     * @param $id
     * @return SettlementResource
     */
    public function show(Settlement $settlement, $id)
    {
        $data = $settlement->ofPlatform()->findOrFail($id);

        return new SettlementResource($data);
    }

    /**
     * 销账申请
     *
     * @param Request $request
     * @param ApplySettlement $applySettlement
     * @return \Illuminate\Http\Response
     */
    public function applySettlement(Request $request, ApplySettlement $applySettlement)
    {
        $attributes = $request->validate([
            'memo_ids' => 'required|array',
            'receivable_ids' => 'required|array',
        ]);
        $applySettlement->handle($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 领取销账申请
     *
     * @param Settlement $settlement
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function receiveSettlement(Settlement $settlement, $id)
    {
        $data = [
            'operated_id' => Auth::user()['id'],
            'status' => Settlement::STATUS_IN_REVIEW,
        ];

        $settlement->where('id', $id)->update($data);

        return response()->noContent();
    }

    /**
     * 确认销账
     *
     * @param HandleSettlement $handleSettlement
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function handleSettlement(HandleSettlement $handleSettlement, $id)
    {
        $handleSettlement->handle($id);

        return response()->noContent();
    }

    /**
     * 销账申请退回
     *
     * @param Request $request
     * @param SendBackSettlement $sendBackSettlement
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function sendBackSettlement(Request $request, SendBackSettlement $sendBackSettlement, $id)
    {
        $attributes = $request->validate([
            'reason' => 'required'
        ]);

        $settlement = Settlement::findOrFail($id);

        $sendBackSettlement->handle($settlement, $attributes);

        return response()->noContent();

    }

    /**
     * 销账管理数据导出
     *
     * @param Settlement $settlement
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(Settlement $settlement)
    {
        $data = $settlement->getSettlements($pageable = false);

        return Excel::download(new SettlementMultipleSheelsExport($data), '销账管理数据导出-'.date('Y-m-d H:i:s').'.xlsx');
    }

    /**
     * 销账保单导出
     *
     * @param Settlement $settlement
     * @param $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function settlementPoliciesExport(Settlement $settlement, $id)
    {
        $data = $settlement->ofPlatform()->findOrFail($id);

        return Excel::download(new SettlementPoliciesExport($data['receivables']), '销账保单导出-'.date('Y-m-d H:i:s').'.xlsx');
    }

    /**
     * 销账发送邮件
     *
     * @param Settlement $settlement
     * @param SendSettlementMail $sendSettlementMail
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function sendMail(Settlement $settlement, SendSettlementMail $sendSettlementMail, $id)
    {
        $settlement = $settlement->ofPlatform()
        ->with([
            'receivables:id'])
        ->findOrFail($id);

        $sendSettlementMail->handle($settlement);

        return response()->noContent();
    }
}
