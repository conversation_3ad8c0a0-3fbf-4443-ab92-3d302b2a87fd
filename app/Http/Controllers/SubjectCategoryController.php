<?php

namespace App\Http\Controllers;

use App\Models\SubjectCategory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SubjectCategoryController extends Controller
{
    /**
     * 获取标的小类
     *
     * @param   SubjectCategory  $subjectCategory
     * @param   int              $subjectId
     *
     * @return  JsonResponse
     */
    public function index(SubjectCategory $subjectCategory, int $subjectId)
    {
        $subjectCategories = $subjectCategory
            ->where('subject_id', $subjectId)
            ->get(['id', 'subject_id', 'name', 'is_enabled']);

        return new JsonResponse($subjectCategories);
    }

    /**
     * 创建标的小类
     *
     * @param   Request          $request
     * @param   SubjectCategory  $subjectCategory
     * @param   int              $subjectId
     *
     * @return  JsonResponse
     */
    public function store(Request $request, SubjectCategory $subjectCategory, int $subjectId)
    {
        $attributes = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $attributes['subject_id'] = $subjectId;

        $subjectCategory->create($attributes);

        return response()->noContent(201);
    }

    /**
     * 更新标的小类
     *
     * @param   Request          $request
     * @param   SubjectCategory  $subjectCategory
     * @param   int              $subjectId
     * @param   int              $id
     *
     * @return  JsonResponse
     */
    public function update(Request $request, SubjectCategory $subjectCategory, int $subjectId, int $id)
    {
        $attributes = $request->validate([
            'name' => 'required|string|max:255',
        ]);

        $subjectCategory
            ->where('subject_id', $subjectId)
            ->where('id', $id)
            ->update($attributes);

        return response()->noContent(204);
    }

    /**
     * 删除标的小类
     *
     * @param   SubjectCategory  $subjectCategory
     * @param   int              $subjectId
     * @param   int              $id
     *
     * @return  JsonResponse
     */
    public function delete(SubjectCategory $subjectCategory, int $subjectId, int $id)
    {
        $subjectCategory
            ->where('subject_id', $subjectId)
            ->where('id', $id)
            ->delete();

        return response()->noContent(204);
    }

    /**
     * 更改小类状态
     *
     * @param   Request          $request
     * @param   SubjectCategory  $subjectCategory
     * @param   int              $subjectId
     * @param   int              $id
     *
     * @return  JsonResponse
     */
    public function updateStatus(Request $request, SubjectCategory $subjectCategory, int $subjectId, int $id)
    {
        $attributes = $request->validate([
            'is_enabled' => 'required|boolean',
        ]);

        $subjectCategory
            ->where('subject_id', $subjectId)
            ->where('id', $id)
            ->update(['is_enabled' => $attributes['is_enabled']]);

        return response()->noContent(204);
    }
}
