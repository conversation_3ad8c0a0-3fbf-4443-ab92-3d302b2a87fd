<?php

namespace App\Http\Controllers;

use App\Models\Subject;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubjectController extends Controller
{
    /**
     * 获取标的.
     *
     * @param   Subject  $subject
     *
     * @return  JsonResource
     */
    public function index(Subject $subject)
    {
        $subjects = $subject->getSubjects();

        return new JsonResource($subjects);
    }

    /**
     * 更新标的关键词建议状态.
     *
     * @param   \Illuminate\Http\Request  $request
     * @param   Subject  $subject
     * @param   int  $subjectId
     *
     * @return  \Illuminate\Http\Response
     */
    public function updateKeywordsSuggestionStatus(Request $request, Subject $subject, int $subjectId)
    {
        $attributes = $request->validate([
            'is_enabled_keywords_suggestion' => 'required|boolean',
        ]);

        $subject->updateKeywordsSuggestionStatus($subjectId, $attributes['is_enabled_keywords_suggestion']);

        return response()->noContent();
    }
}
