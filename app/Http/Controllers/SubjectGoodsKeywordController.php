<?php

namespace App\Http\Controllers;

use App\Http\Resources\Subject\KeywordsResource;
use App\Models\SubjectGoodsKeyword;
use Illuminate\Http\Request;

class SubjectGoodsKeywordController extends Controller
{
    /**
     * 获取标的关键词.
     *
     * @param   RequSubjectGoodsKeywordest  $subjectGoodsKeyword
     * @param   int                         $subjectId
     *
     * @return  \Illuminate\Http\Resoureces\Json\JsonResource
     */
    public function index(SubjectGoodsKeyword $subjectGoodsKeyword, int $subjectId)
    {
        $keywords = $subjectGoodsKeyword->getKeywords($subjectId);

        return new KeywordsResource($keywords);
    }

    /**
     * 添加关键词.
     *
     * @param   \Illuminate\Http\Request  $request
     * @param   \App\Models\SubjectGoodsKeyword  $subjectGoodsKeyword
     * @param   int  $subjectId
     *
     * @return  \Illuminate\Http\Response
     */
    public function create(Request $request, SubjectGoodsKeyword $subjectGoodsKeyword, int $subjectId)
    {
        $attributes = $request->validate([
            'keyword' => 'required|string',
        ]);

        $subjectGoodsKeyword->addKeyword($subjectId, $attributes['keyword']);

        return response()->noContent();
    }

    /**
     * 转换关键词.
     *
     * @param   \Illuminate\Http\Request  $request
     * @param   \App\Models\SubjectGoodsKeyword  $subjectGoodsKeyword
     * @param   int  $subjectId
     * @param   int  $keywordId
     *
     * @return  \Illuminate\Http\Response
     */
    public function convert(Request $request, SubjectGoodsKeyword $subjectGoodsKeyword, int $subjectId, int $keywordId)
    {
        $attributes = $request->validate([
            'keyword' => 'required|string',
        ]);

        $subjectGoodsKeyword->convertKeyword($subjectId, $keywordId, $attributes['keyword']);

        return response()->noContent();
    }

    /**
     * 删除关键词.
     *
     * @param   \App\Models\SubjectGoodsKeyword  $subjectGoodsKeyword
     * @param   int  $subjectId
     * @param   int  $keywordId
     *
     * @return  \Illuminate\Http\Response
     */
    public function delete(SubjectGoodsKeyword $subjectGoodsKeyword, int $subjectId, int $keywordId)
    {
        $subjectGoodsKeyword->deleteKeyword($subjectId, $keywordId);

        return response()->noContent();
    }
}
