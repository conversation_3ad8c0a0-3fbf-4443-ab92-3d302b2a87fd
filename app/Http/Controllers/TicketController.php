<?php

namespace App\Http\Controllers;

use App\Http\Resources\Ticket\TicketResource;
use App\Http\Resources\Ticket\TicketsResource;
use App\Models\Policy;
use App\Models\Ticket;
use App\Services\Ticket\ApiCallback;
use App\Services\Ticket\Dispose;
use App\Services\Ticket\ApiHandle;
use App\Services\Ticket\TicketSendBack;
use App\Services\Ticket\TicketSendBackSupplementInfo;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Symfony\Component\HttpFoundation\Response;

class TicketController extends Controller
{
    /**
     * 工单列表
     *
     * @param Ticket $ticket
     * @return TicketsResource
     */
    public function index(Ticket $ticket)
    {
        $tickets = $ticket->getTickets();

        return new TicketsResource($tickets);
    }

    /**
     * 工单详情
     *
     * @param Ticket $ticket
     * @param $id
     * @return TicketResource
     */
    public function show(Ticket $ticket, $id)
    {
        $ticket = $ticket->getTicket($id);

        return new TicketResource($ticket);
    }

    /**
     * 领取工单
     *
     * @param Ticket $ticket
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function receive(Ticket $ticket, $id)
    {
        $ticket = $ticket->findOrFail($id);
        if (!empty($ticket['operator_id']) || $ticket['operator_id'] == -1) {
            abort(403, '当前工单已被其他业务员领取');
        }
        if ($ticket['status'] != Ticket::STATUS_UNRECEIVE) {
            abort(403, '当前工单状态不可领取');
        }
        $ticket->update([
            'operator_id' => auth()->user()->id,
            'operator_at' => now(),
            'status' => Ticket::STATUS_RECEIVE
        ]);

        return response()->noContent();
    }

    /**
     * 退回工单
     *
     * @param Request $request
     * @param TicketSendBack $ticketSendBack
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function sendBack(Request $request, TicketSendBack $ticketSendBack, $id)
    {
        $attributes = $request->validate([
            'reason' => 'required'
        ]);

        $ticket = Ticket::findOrFail($id);

        $ticketSendBack->handle($ticket, $attributes);

        return response()->noContent();
    }

    /**
     * 处理工单
     *
     * @param Request $request
     * @param Dispose $dispose
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function handle(Request $request, Dispose $dispose, $id)
    {
        $ticket = Ticket::findOrFail($id);
        $attributes = $request->validate([
            'endorse_no' => Rule::requiredIf(fn() => $ticket['type'] == Ticket::TYPE_MODIFY),
            'policy_file' => Rule::requiredIf(fn() => $ticket['type'] == Ticket::TYPE_MODIFY && $ticket['policy']['company']['identifier'] != 'PICC'),
            'rate' => 'nullable|numeric',
            'premium' => 'nullable|numeric',
            'platform_rate' => 'nullable|numeric',
            'platform_premium' => 'nullable|numeric',
            'agent_rate' => 'nullable|numeric',
            'agent_premium' => 'nullable|numeric',
            'user_rate' => 'nullable|numeric',
            'user_premium' => 'nullable|numeric',
            'service_charge' => 'nullable|numeric',
            'platform_commission_rate' => 'nullable|numeric',
            'agent_commission_rate' => 'nullable|numeric',
        ]);

        $dispose->handle($ticket, $attributes);

        return response()->noContent();
    }

    /**
     * 处理接口批改
     *
     * @param Request $request
     * @param ApiHandle $handle
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function handleApi(Request $request, ApiHandle $handle, $id)
    {
        $ticket = Ticket::findOrFail($id);

        $attributes = $request->validate([
            'endorse_type' => Rule::requiredIf(function () use ($ticket) {
                return $ticket['policy']['product']['additional']['config']['policy_mode'] === 'API_DIC' && $ticket['type'] === Ticket::TYPE_MODIFY;
            })
        ]);

        $handle->handle($ticket, $attributes);

        return response()->noContent();
    }

    /**
     * 退回工单(补充资料)
     *
     * @param Request $request
     * @param TicketSendBackSupplementInfo $sendBackSupplementInfo
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function sendBackSupplementInfo(Request $request, TicketSendBackSupplementInfo $sendBackSupplementInfo, $id)
    {
        $attributes = $request->validate([
            'reason' => 'required'
        ]);
        $ticket = Ticket::findOrFail($id);

        $sendBackSupplementInfo->handle($ticket, $attributes);

        return response()->noContent();
    }

    /**
     * 接口回调
     *
     * @param Request $request
     * @param ApiCallback $callback
     * @return string
     */
    public function callbacks(Request $request, ApiCallback $callback)
    {
        $attributes = $request->validate([
            'event' => 'nullable',
            'policy_no' => 'nullable',
            'apply_no' => 'nullable',
            'sys_order_no' => 'nullable',
            'reason' => 'nullable',
            'callback' => 'nullable',
            'status' => 'nullable|in:1,2,3',
        ]);

        $callback->handle($attributes);

        Log::debug('Ticket Callbacks', ['data' => $attributes]);

        return 'SUCCESS';

    }
}
