<?php

namespace App\Http\Controllers;

use App\Models\TransportMethod;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Symfony\Component\HttpFoundation\Response;

class TransportMethodController extends Controller
{
    /**
     * 运输方式列表.
     *
     * @param   TransportMethod  $transportMethod
     * @param   int            $companyId
     *
     * @return  \Illuminate\Http\Resources\Json\JsonResource
     */
    public function index(TransportMethod $transportMethod, int $companyId)
    {
        $transportMethods = $transportMethod
            ->with(['loadingMethods'])
            ->ofCompany($companyId)
            ->get(['id'] + $transportMethod->getFillable())
            ->map(function ($method) {
                $method->offsetSet('loading_method_ids', $method['loadingMethods']->pluck('id')->toArray());

                return $method;
            });

        return new JsonResource($transportMethods);
    }

    /**
     * 创建运输方式.
     *
     * @param   Request        $request
     * @param   TransportMethod  $transportMethod
     * @param   int            $companyId
     *
     * @return  \Illuminate\Http\Response
     */
    public function create(Request $request, TransportMethod $transportMethod, int $companyId)
    {
        $attributes = $request->validate([
            'name' => 'required|min:1',
            'value' => 'nullable|min:1',
            'display_name' => 'required|min:1',
            'loading_method_ids' => 'required|array',
        ]);

        $attributes['company_id'] = $companyId;

        DB::transaction(function () use ($transportMethod, $attributes) {
            $transportMethod = $transportMethod->create($attributes);
            $transportMethod->loadingMethods()->sync($attributes['loading_method_ids']);
        });

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新运输方式.
     *
     * @param   Request        $request
     * @param   TransportMethod  $transportMethod
     * @param   int            $companyId
     *
     * @return  \Illuminate\Http\Response
     */
    public function update(Request $request, TransportMethod $transportMethod, int $companyId, int $id)
    {
        $attributes = $request->validate([
            'name' => 'required|min:1',
            'value' => 'nullable|min:1',
            'display_name' => 'required|min:1',
            'loading_method_ids' => 'required|array',
        ]);

        DB::transaction(function () use ($transportMethod, $attributes, $id) {
            $transportMethod = $transportMethod->findOrFail($id);

            $transportMethod->update($attributes);
            $transportMethod->loadingMethods()->sync($attributes['loading_method_ids']);
        });

        return response()->noContent();
    }

    /**
     * 删除运输方式.
     *
     * @param   TransportMethod  $transportMethod
     * @param   int            $companyId
     * @param   int            $id
     *
     * @return  \Illuminate\Http\Response
     */
    public function delete(TransportMethod $transportMethod, int $companyId, int $id)
    {
        $transportMethod->where('id', $id)->delete();

        return response()->noContent();
    }
}
