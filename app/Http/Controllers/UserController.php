<?php

namespace App\Http\Controllers;

use App\Exports\UserExport;
use App\Http\Resources\Payment\PaymentResource;
use App\Http\Resources\Policy\PoliciesResource;
use App\Http\Resources\Policy\PolicyResource;
use App\Http\Resources\Transaction\TransactionsResource;
use App\Http\Resources\User\SaleClientsResource;
use App\Http\Resources\User\UserProductsResource;
use App\Http\Resources\User\UserResource;
use App\Http\Resources\User\UsersResource;
use App\Models\Payment;
use App\Models\Policy;
use App\Models\Transaction;
use App\Models\User;
use App\Services\User\AssignProduct;
use App\Services\User\CreateUser;
use App\Services\User\GetEnabledProducts;
use App\Services\User\ResetPassword;
use App\Services\User\UpdateProductStatus;
use App\Services\User\UpdateUser;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Str;

class UserController extends Controller
{
    /**
     * 获取客户列表
     *
     * @param  User $user
     *
     * @return UsersResource|JsonResource
     */
    public function index(User $user)
    {
        $users = $user->getUsers();

        return new UsersResource($users);
    }

    /**
     * 客户详情
     *
     * @param User $user
     * @param $id
     * @return JsonResource
     */
    public function show(User $user, $id)
    {
        $user = $user->findOrFail($id);

        $this->authorize('update', $user);

        return new UserResource($user);
    }

    /**
     * 添加客户
     *
     * @param Request $request
     * @param CreateUser $createUser
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request, CreateUser $createUser)
    {
        $attributes = $request->validate([
            'registration_id' => 'nullable',
            'type' => 'required|in:1,2',
            'agent_id' => 'nullable|numeric',
            'salesman_id' => 'required',
            'name' => 'required|min:2|max:32',
            'username' => ['required', 'min:4', Rule::unique('users', 'username')->where('platform_id', Auth::user()['platform_id'])],
            'password' => 'required|min:6|max:32',
            'email' => ['required', 'email', Rule::unique('users', 'email')->where('platform_id', Auth::user()['platform_id'])],
            'cc_email' => 'nullable',
            'address' => 'nullable',
            'idcard_no' => 'required',
            'idcard_no_file' => 'nullable',
            'attachment' => 'nullable',
            'phone_number' => 'required',
            'is_agent' => 'nullable|bool',
            'is_enabled' => 'nullable|bool',
            'is_online_payment' => 'nullable|bool',
            'charge_type' => 'nullable|bool',
            'is_use_insure_preset_data' => 'nullable|bool',
        ]);

        $createUser->handle($attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 更新客户信息
     *
     * @param Request $request
     * @param UpdateUser $updateUser
     * @param $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function update(Request $request, UpdateUser $updateUser, $id)
    {
        $attributes = $request->validate([
            'agent_id' => 'nullable|numeric',
            'type' => 'required|in:1,2',
            'salesman_id' => 'required',
            'name' => 'required|min:2|max:32',
            'username' => ['required', 'min:4', Rule::unique('users', 'username')->where('platform_id', Auth::user()['platform_id'])->ignore($id)],
            'password' => 'nullable|min:6|max:32',
            'email' => ['required', 'email', Rule::unique('users', 'email')->where('platform_id', Auth::user()['platform_id'])->ignore($id)],
            'cc_email' => 'nullable',
            'idcard_no' => 'required',
            'idcard_no_file' => 'nullable',
            'attachment' => 'nullable',
            'phone_number' => 'required',
            'address' => 'nullable',
            'remark' => 'nullable',
            'is_agent' => 'required|bool',
            'is_enabled' => 'nullable|bool',
            'is_online_payment' => 'nullable|bool',
            'charge_type' => 'nullable|bool',
            'is_use_insure_preset_data' => 'nullable|bool',
        ]);

        $user = User::findOrFail($id);

        $this->authorize('update', $user);

        $updateUser->handle($user, $attributes);

        return response()->noContent();
    }

    /**
     * 充值/扣款.
     *
     * @param Request $request
     * @param User $user
     * @param $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function balance(Request $request, User $user, $id)
    {
        $attributes = $request->validate([
            'type' => 'required|in:1,2',
            'amount' => 'required|numeric',
            'remark' => 'nullable',
        ]);

        $user = $user->findOrFail($id);

        $this->authorize('update', $user);

        $method = $attributes['type'] === Transaction::TYPE_CHARGE ? 'charge' : 'pay';

        $user->{$method}($attributes['amount'], null, $attributes['remark']);

        $before = $user->toArray();

        $user->userLogs()->create([
            'admin_id' => Auth::id(),
            'content' => '用户充值',
            'payload' => $attributes,
            'before' => $before,
            'after' => $user->toArray(),
        ]);

        return response()->noContent();
    }

    /**
     * 重置密码
     *
     * @param Request $request
     * @param ResetPassword $resetPassword
     * @param $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function resetPassword(Request $request, ResetPassword $resetPassword, $id)
    {
        $attributes = $request->validate([
            'password' => 'required'
        ]);

        $user = User::findOrFail($id);

        $this->authorize('update', $user);

        $resetPassword->handle($user, $attributes);

        return response()->noContent();
    }

    /**
     * 用户产品
     *
     * @param User $user
     * @param $id
     * @return UserProductsResource
     */
    public function products(User $user, $id)
    {
        $user = $user->with(['products'])->findOrFail($id);

        return new UserProductsResource($user['products']);
    }

    /**
     * 设置产品
     *
     * @param Request $request
     * @param AssignProduct $assignProduct
     * @param $userId
     * @param $productId
     * @return \Illuminate\Http\Response
     */
    public function assignProduct(Request $request, AssignProduct $assignProduct, $userId, $productId)
    {
        $attributes = $request->validate([
            'is_cargo_product' => 'required|bool',
            'is_premium_sync' => 'required|bool',
            'user_rate' => 'required_if:is_cargo_product,1|nullable|numeric',
            'minimum_premium' => 'required_if:is_cargo_product,1|nullable|numeric',
            'agent_commission_rate' => 'nullable|numeric',
            'is_allowed_invoice' => 'nullable|numeric',
            'is_enabled' => 'nullable|numeric',
        ]);

        $assignProduct->handle($userId, $productId, $attributes);

        return response()->noContent(Response::HTTP_CREATED);
    }

    public function assignProductMultiple(Request $request, GetEnabledProducts $getEnabledProducts, AssignProduct $assignProduct, $userId)
    {
        $attributes = $request->validate([
            'is_cargo_product' => 'required|bool',
            'is_premium_sync' => 'required|bool',
            'select_all' => 'required|bool',
            'except_ids' => 'nullable|array',
            'ids' => 'required_if:select_all,false|array',
            'user_rate' => 'required_if:is_cargo_product,1|nullable|numeric',
            'minimum_premium' => 'required_if:is_cargo_product,1|nullable|numeric',
            'agent_commission_rate' => 'nullable|numeric',
            'is_allowed_invoice' => 'nullable|numeric',
            'is_enabled' => 'nullable|numeric',
        ]);

        $user = User::findOrFail($userId);

        $data = $getEnabledProducts->handle($user, false, ['product_id'], $attributes['ids'] ?? null, $attributes['except_ids'] ?? null);

        DB::transaction(function () use ($assignProduct, $user, $data, $attributes) {
            $data->each(function ($product) use ($assignProduct, $user, $attributes) {
                $assignProduct->handle($user['id'], $product['product_id'], $attributes);
            });
        });

        return response()->noContent();
    }

    /**
     * 禁用/启用产品
     *
     * @param UpdateProductStatus $updateProductStatus
     * @param $userId
     * @param $productId
     * @return \Illuminate\Http\Response
     */
    public function disableOrEnableProduct(UpdateProductStatus $updateProductStatus, $userId, $productId)
    {
        $updateProductStatus->handle($userId, $productId);

        return response()->noContent();
    }

    /**
     * 用户保单列表
     *
     * @param Policy $policy
     * @param $userId
     * @param $type
     * @return PoliciesResource
     */
    public function policies(Policy $policy, $userId, $type)
    {
        $policies = $policy->getPolicies($type, $userId);

        return new PoliciesResource($policies);
    }

    /**
     * 用户保单详情
     *
     * @param Policy $policy
     * @param $policyId
     * @return PolicyResource
     */
    public function policy(Policy $policy, $policyId)
    {
        $policy = $policy->getPolicy($policyId);

        return new PolicyResource($policy);
    }

    /**
     * 用户财务记录
     *
     * @param Transaction $transaction
     * @param $userId
     * @return TransactionsResource
     */
    public function transactions(Transaction $transaction, $userId)
    {
        $transactions = $transaction->getTransactions($userId);

        return new TransactionsResource($transactions);
    }

    /**
     * 用户充值记录
     *
     * @param Payment $payment
     * @param $userId
     * @return PaymentResource
     */
    public function payments(Payment $payment, $userId)
    {
        $payments = $payment->getPayments($pageable = true, $userId);

        return new PaymentResource($payments);
    }

    /**
     * 用户列表导出
     *
     * @param User $user
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function export(User $user)
    {
        $users = $user->getUsers(false);

        return Excel::download(new UserExport($users), '用户列表导出-' . date('Y-m-d H:i:s') . '.xlsx');
    }

    /**
     * 禁/启用用户
     *
     * @param User $user
     * @param $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function disableOrEnableUser(User $user, $id)
    {
        $client = $user->findOrFail($id);
        $this->authorize('update', $client);

        DB::transaction(function () use ($user, $client) {
            if ($client['is_enabled']) {
                $user->where('agent_id', $client['id'])->update(['is_enabled' => !$client['is_enabled']]);
            }
            $before = $client->toArray();

            $client->update(['is_enabled' => !$client['is_enabled']]);

            $client->userLogs()->create([
                'admin_id' => Auth::id(),
                'content' => '禁/启用客户',
                'before' => $before,
                'after' => $client->toArray(),
            ]);
        });

        return response()->noContent();
    }

    /**
     * 获取业务员下属客户
     *
     * @param User $user
     * @param $id
     * @return SaleClientsResource
     */
    public function getSaleClients(User $user, $id)
    {
        $data = $user->where('salesman_id', $id)->get();

        return new SaleClientsResource($data);
    }

    /**
     * 修改用户发票信息
     *
     * @param Request $request
     * @param User $user
     * @param $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function updateInvoiceData(Request $request, User $user, $id)
    {
        $attributes = $request->validate([
            'company_name' => 'nullable',
            'tax_no' => 'nullable',
            'bank_name' => 'nullable',
            'bankcard_no' => 'nullable',
            'registered_addr' => 'nullable',
            'registered_phone_number' => 'nullable',
            'recipient' => 'nullable',
            'recipient_phone_number' => 'nullable',
            'delivery_address' => 'nullable',
        ]);

        $user = $user->findOrFail($id);

        $this->authorize('update', $user);

        $user->invoice()->updateOrCreate([
            'user_id' => $id
        ], $attributes);

        $user->userLogs()->create([
            'admin_id' => Auth::id(),
            'content' => '更新发票信息',
            'payload' => $attributes,
        ]);

        return response()->noContent();
    }

    /**
     * 修改用户投保预设信息
     *
     * @param Request $request
     * @param User $user
     * @param $id
     * @return \Illuminate\Http\Response
     */
    public function updateInsurePresetData(Request $request, User $user, $id)
    {
        $attributes = $request->validate([
            'policyholder' => 'nullable',
            'policyholder_type' => 'nullable',
            'policyholder_idcard_no' => 'nullable',
            'policyholder_phone_number' => 'nullable',
            'policyholder_address' => 'nullable',
            'policyholder_idcard_issue_date' => 'nullable',
            'policyholder_idcard_valid_till' => 'nullable',
            'insured' => 'nullable',
            'insured_type' => 'nullable',
            'insured_idcard_no' => 'nullable',
            'insured_phone_number' => 'nullable',
            'insured_address' => 'nullable',
            'insured_idcard_issue_date' => 'nullable',
            'insured_idcard_valid_till' => 'nullable',
        ]);

        $user = $user->findOrFail($id);

        $before = $user->toArray();

        $user->update(['insure_preset_data' => json_encode($attributes)]);

        $user->userLogs()->create([
            'admin_id' => Auth::id(),
            'content' => '更新投保预设信息',
            'payload' => $attributes,
            'before' => $before,
            'after' => $user->toArray(),
        ]);

        return response()->noContent();
    }

    /**
     * 生成接口调用信息.
     *
     * @param User $user
     * @param int $id
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Auth\Access\AuthorizationException
     */
    public function keySecret(User $user, int $id)
    {
        $user = $user->findOrFail($id);

        $this->authorize('update', $user);

        $user->update([
            'api_key' => md5($user['username']),
            'api_secret' => Str::random(64),
        ]);

        $user->userLogs()->create([
            'admin_id' => Auth::id(),
            'content' => '生成接口信息'
        ]);

        return response()->noContent(Response::HTTP_CREATED);
    }

    /**
     * 获取用户或代理商
     *
     * @param User $user
     * @return JsonResource
     */
    public function getUsers(User $user)
    {
        return new JsonResource($user->getUsersOrAgents());
    }
}
