<?php

namespace App\Http\Controllers;

use App\Models\UserGoodsKeyword;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserGoodsKeywordController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @param UserGoodsKeyword $model
     * @param int $userId
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function index(UserGoodsKeyword $model, int $userId)
    {
        $data = $model->where('user_id', $userId)
            ->get(['id', 'keyword', 'action', 'data', 'created_at'])
            ->map(function ($item) {
                $item->offsetSet('added_at', $item->created_at->toDateTimeString());
                return $item;
            })
            ->toArray();

        return new JsonResource($data);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  Request $request
     * @param  UserGoodsKeyword $model
     * @param  int $userId
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, UserGoodsKeyword $model, int $userId)
    {
        $data = $request->validate([
            'keyword' => 'required|string',
            'action' => 'required|integer|in:1,2',
            'data' => 'required_if:action,1|array'
        ]);

        $data['user_id'] = $userId;

        $model->create($data);

        return response()->noContent(201);
    }

    /**
     * 更新关键字
     * @param  \Illuminate\Http\Request $request
     * @param  \App\Models\UserGoodsKeyword $model
     * @param  int $userId
     * @param  int $id
     * @return mixed|\Illuminate\Http\Response
     */
    public function update(Request $request, UserGoodsKeyword $model, int $userId, int $id)
    {
        $data = $request->validate([
            'keyword' => 'required|string',
            'action' => 'required|integer|in:1,2',
            'data' => 'required_if:action,1|array'
        ]);

        $model->where('user_id', $userId)
            ->findOrFail($id)
            ->update($data);

        return response()->noContent();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  UserGoodsKeyword $model
     * @param  int $userId
     * @param  int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(UserGoodsKeyword $model, int $userId, int $id)
    {
        $model->where('user_id', $userId)->findOrFail($id)->delete();

        return response()->noContent();
    }
}
