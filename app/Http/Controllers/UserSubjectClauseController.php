<?php

namespace App\Http\Controllers;

use App\Models\UserSubjectClause;
use App\Models\User;
use DB;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserSubjectClauseController extends Controller
{
    /**
     * 获取用户标的条款
     *
     * @param  \App\Models\UserSubjectClause $subjectClause
     * @param  string $userId
     * @return \Illuminate\Http\Resources\Json\JsonResource
     */
    public function index(UserSubjectClause $subjectClause, string $userId)
    {
        return new JsonResource($subjectClause->getSubjectClauses($userId));
    }

    /**
     * 创建用户标的条款
     *
     * @param  \Illuminate\Http\Request $request
     * @param  string $userId
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request, string $userId)
    {
        $attributes = $request->validate([
            'subject_id' => 'required|exists:subjects,id',
            'company_branch_id.*' => 'required|exists:company_branches,id',
            'product_type' => 'required|in:1,2',
            'deductible' => 'nullable|string',
            'special_agreement' => 'nullable|string',
            'without_port_to_port_clause' => 'nullable|boolean',
        ]);

        $user = User::findOrFail($userId);

        $this->authorize('update', $user);

        DB::transaction(function () use ($userId, $attributes) {
            $model = UserSubjectClause::updateOrCreate([
                'subject_id' => $attributes['subject_id'],
                'product_type' => $attributes['product_type'],
                'user_id' => $userId,
            ], $attributes);

            $model->companyBranches()->sync($attributes['company_branch_id']);
        });

        return response()->noContent(201);
    }

    /**
     * 删除用户标的条款
     *
     * @param  string $userId
     * @param  string $subjectClauseId
     * @return \Illuminate\Http\Response
     */
    public function destroy(string $userId, string $subjectClauseId)
    {
        $user = User::findOrFail($userId);

        $this->authorize('update', $user);

        $record = UserSubjectClause::where('user_id', $userId)
            ->findOrFail($subjectClauseId);

        $record->delete();
        $record->companyBranches()->detach();

        return response()->noContent(204);
    }
}
