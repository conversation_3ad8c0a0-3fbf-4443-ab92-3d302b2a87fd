<?php

namespace App\Services\Inquiry;

use App\Enums\InquiryLoadingMethod;
use App\Enums\InquiryStatus;
use App\Models\Currency;
use App\Models\Inquiry;
use App\Models\Policy;
use App\Models\PolicyCargo;
use App\Models\Product;
use App\Models\Subject;
use App\Models\UserProduct;
use Arr;
use DB;


class CreateNewPolicy
{
    /**
     * 保单主表暂存字段.
     *
     * @var array
     */
    public array $policyFields = [
        'type' => -1,
        'product_id' => -1,
        'platform_id' => -1,
        'user_id' => -1,
        'order_no' => '',
        'agent_id' => -1,
        'company_id' => -1,
        'company_branch_id' => -1,
        'channel_id' => -1,
        'policyholder' => ' ',
        'policyholder_type' => Policy::CLIENT_TYPE_GROUP,
        'policyholder_overseas' => 0,
        'policyholder_idcard_issue_date' => null,
        'policyholder_idcard_valid_till' => null,
        'policyholder_phone_number' => ' ',
        'policyholder_address' => ' ',
        'policyholder_idcard_no' => ' ',
        'insured' => ' ',
        'insured_type' => Policy::CLIENT_TYPE_GROUP,
        'insured_overseas' => 0,
        'insured_idcard_issue_date' => null,
        'insured_idcard_valid_till' => null,
        'insured_phone_number' => ' ',
        'insured_address' => ' ',
        'insured_idcard_no' => ' ',
        'sticky_note' => '',
        'coverage' => 0,
        'user_rate' => 0.00,
        'user_premium' => 0.00,
        'remark' => '',
        'webhook_url' => '',
        'status' => Policy::STATUS_UNSUBMITTED,
    ];

    /**
     * 条款表.
     *
     * @var string[]
     */
    protected $clauseFields = [
        'main_clause_id' => '',
        'additional_clause_ids' => [],
    ];

    /**
     * 人工审核原因.
     *
     * @var string[]
     */
    protected $subjectCategoryFields = [
        'subject_category_ids' => [],
    ];

    /**
     * 保单货运险表暂存字段.
     *
     * @var array
     */
    public array $policyCargoFields = [
        'is_new' => 1,
        'subject_id' => -1,
        'manual_conditions' => '',
        'transport_method_id' => -1,
        'loading_method_id' => -1,
        'packing_method_id' => -1,
        'coverage_currency_id' => -1,
        'invoice_currency_id' => -1,
        'goods_type_id' => -1,
        'bonus' => 10,
        'waybill_no' => '',
        'contract_no' => '',
        'transport_no' => '',
        'invoice_no' => '',
        'insure_type' => -1,
        'departure' => '',
        'departure_port' => '',
        'transmit' => '',
        'transmit_port' => '',
        'destination' => '',
        'destination_port' => '',
        'anti_dated_file' => '',
        'goods_name' => '',
        'goods_amount' => '',
        'shipping_date' => '',
        'ship_construction_year' => '',
        'shipping_date_print_format' => 1,
        'payable_at' => '',
        'freight' => '',
        'shipping_mark' => '',
        'invoice_amount' => 0,
        'is_credit' => 0,
        'credit_no' => '',
        'deductible' => '',
        'special' => '',
        'main_clause' => '',
        'additional_clause' => '',
        'clause_content' => '',
        'custom_file' => '',
        // cbec
        'coverage_scope' => -1,
        'company_branch_id' => -1,
        'weight' => 0,
        'shipment_id' => '',
        'destination_type_id' => -1,
        'delivery_method_id' => -1,
        'express_company_id' => -1,
        'express_no' => '',
        // end cbec
    ];

    /**
     * 创建保单暂存单
     *
     * @param  int $inquiryId
     * @param  array $attributes
     * @return \App\Models\Policy
     */
    public function handle(int $inquiryId, array $attributes)
    {
        $inquiry = Inquiry::ofPlatform()->with(['user'])->findOrFail($inquiryId);

        abort_if($inquiry->policy, 400, '保单已存在');

        abort_if(!in_array($inquiry->status, [InquiryStatus::Quoted, InquiryStatus::Sent]), 400, '询价单状态不正确');

        $data = $this->data($inquiry, $attributes);

        $policy = $this->create($data, $attributes);

        $inquiry->update(['policy_id' => $policy->id]);

        return $policy;
    }

    /**
     * 创建保单暂存单
     *
     * @param  array $data
     * @param  array $attributes
     * @return \App\Models\Policy
     */
    protected function create(array $data, array $attributes)
    {
        return DB::transaction(function () use ($data, $attributes) {
            $policy = Policy::create(Arr::only($data, array_keys($this->policyFields)));

            $policyCargo = $policy->policyCargo()->create(Arr::only($data, array_keys($this->policyCargoFields)));

            $policyCargo->subjectCategories()->sync($attributes['manual_subject_reason_ids']);

            return $policy;
        });
    }

    /**
     * 保单数据
     *
     * @param  \App\Models\Inquiry $inquiry
     * @param  array $attributes
     * @return array
     */
    protected function data(Inquiry $inquiry, array $attributes): array
    {
        $product = Product::findOrFail($attributes['product_id']);

        $userProduct = (new UserProduct())->getProduct($inquiry->user_id, $product->id, );

        $insuredAmount = $this->insuredAmount($inquiry, $attributes);

        $data = array_merge(
            [
                'subject_id' => Subject::where('identifier', 'MANUAL')->value('id'),
                'product_id' => $product->id,
                'policyholder' => $inquiry->policyholder_name,
                'insured' => $inquiry->insured_name,
                'goods_name' => $inquiry->goods_name,
                'goods_amount' => $inquiry->packaging_and_quantity,
                'departure' => $this->removeAddressPrefixes($inquiry->departure),
                'transmit' => $this->removeAddressPrefixes($inquiry->transit),
                'destination' => $this->removeAddressPrefixes($inquiry->destination),
                'shipping_date' => $inquiry->departure_date,
                'insure_type' => $this->guessInsureType($attributes['product_type'], $inquiry->departure, $inquiry->destination),
                'user_rate' => $attributes['rate'],
                'user_premium' => $this->calcPremium($insuredAmount['coverage'], $attributes['rate'], $userProduct['minimum_premium']),
                'type' => $attributes['product_type'],
                'platform_id' => $inquiry->user->platform_id,
                'user_id' => $inquiry->user->id,
                'agent_id' => $inquiry->user->agent_id,
                'order_no' => order_no('TMP'),
                'manual_conditions' => $attributes['deductible_special_terms'],
                'status' => Policy::STATUS_UNSUBMITTED,
                // 'loading_method_id' => $this->guessLoadingMethod($inquiry->loading_method, $product),
            ],
            $product->only([
                'platform_id',
                'company_id',
                'company_branch_id',
                'channel_id',
            ]),
            $insuredAmount
        );

        return $this->fillValues(
            array_merge(
                $this->policyFields,
                $this->policyCargoFields,
                $this->clauseFields,
                $this->subjectCategoryFields
            ),
            $data
        );
    }

    /**
     * 猜测保障范围
     *
     * @param   int  $productType
     * @param   string  $from
     * @param   string  $to
     *
     * @return  int
     */
    protected function guessInsureType(int $productType, string $from, string $to)
    {
        if ($productType === Product::TYPE_DOMESTIC) {
            return PolicyCargo::INSURE_TYPE_DOMESTIC;
        }

        if (str_starts_with($from, '中国大陆') && str_ends_with($to, '境外地区')) {
            return PolicyCargo::INSURE_TYPE_EXPORT;
        }

        if (str_starts_with($from, '境外地区') && str_ends_with($to, '中国大陆')) {
            return PolicyCargo::INSURE_TYPE_IMPORT;
        }

        return PolicyCargo::INSURE_TYPE_OVERSEAS;
    }

    /**
     * 去除地址前缀
     *
     * @param   string|null  $address
     *
     * @return  string
     */
    protected function removeAddressPrefixes(null|string $address): string
    {
        if (!$address) {
            return '';
        }

        $removable = ['中国大陆-', '境外地区-'];
        return str_replace($removable, '', $address);
    }

    /**
     * Fill values.
     *
     * @param   array  $fields
     * @param   array  $attributes
     *
     * @return  array
     */
    protected function fillValues(array $fields, array $attributes)
    {
        $values = [];
        foreach ($fields as $field => $defaultValue) {
            $values[$field] = $attributes[$field] ?? $defaultValue;
        }

        return $values;
    }

    /**
     * 计算保费.
     *
     * @param   int  $coverage
     * @param   float  $rate
     * @param   int  $minPremium
     *
     * @return  float
     */
    protected function calcPremium(int $coverage, float $rate, int $minimum = 0)
    {
        $premium = bcmul($coverage, bcdiv($rate, 10000, 6), 5);

        return (int) round($premium < $minimum ? $minimum : $premium, 0);
    }
    /**
     * 保险金额.
     *
     * @param  \App\Models\Inquiry $inquiry
     * @param  array $attributes
     * @return array
     */
    protected function insuredAmount(Inquiry $inquiry, array $attributes): array
    {
        $data = [
            'coverage' => $inquiry->goods_value * 100
        ];

        if ((int) $attributes['product_type'] === Product::TYPE_INTL) {
            $currency = Currency::where('code', $inquiry->goods_value_currency)
                ->latest()
                ->first();

            $data['bonus'] = 10;
            $data['invoice_amount'] = $currency->rate * $data['coverage'];
            $data['invoice_currency_id'] = $currency->id;
            $data['coverage'] = $data['invoice_amount'] * (1 + $data['bonus'] / 100);
            $data['coverage_currency_id'] = $currency->id;
        }

        return $data;
    }

    // /**
    //  * Guess the loading method based on the inquiry value and product configuration.
    //  *
    //  * @param  int  $loadingMethod
    //  * @param  \App\Models\Product  $product
    //  * @return int
    //  */
    // protected function guessLoadingMethod(int $loadingMethod, Product $product): int
    // {
    //     $loadingMethodText = InquiryLoadingMethod::from($loadingMethod)->text();

    //     // Get the allowed loading methods for this product
    //     $allowedLoadingMethods = $product->loadingMethods()->get();

    //     // Try to find a matching loading method in the product's allowed methods
    //     foreach ($allowedLoadingMethods as $allowedMethod) {
    //         if (
    //             $allowedMethod->name === $loadingMethodText ||
    //             $allowedMethod->display_name === $loadingMethodText
    //         ) {
    //             return $allowedMethod->id;
    //         }
    //     }

    //     return -1;
    // }
}
